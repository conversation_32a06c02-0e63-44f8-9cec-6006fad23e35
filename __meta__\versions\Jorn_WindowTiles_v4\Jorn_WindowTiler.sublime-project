{
    "folders": [
        // [ROOT]
        {
            // [DEFAULT]
            // =======================================================
            "path": ".",
            "folder_exclude_patterns": [".git", "__pycache__", "build", "dist", "venv", "node_modules", ".backups", "logs", ],
            "file_exclude_patterns": ["*.sublime-workspace", "*.pyc", "*.pyo", "*.swp", "*.tmp", "*.log", ],

            // // [PY.AI]
            // // =======================================================
            // "name": "[ai]",
            // "path": ".",
            // "folder_exclude_patterns": [".git", "__pycache__", "build", "dist", "venv", "node_modules", ".backups", "logs", ],
            // "file_include_patterns": ["*.py", "*.bat", "*.history*", "*.chains*", "*.pairs*", "*.last_execution*"],
        },

    ],
    "settings": {
        "tab_size": 4,
        "default_line_ending": "unix",
        "translate_tabs_to_spaces": true,
        "ensure_newline_at_eof_on_save": true,
        "trim_trailing_white_space_on_save": true,
        "python_interpreter": "$project_path\\venv\\Scripts\\python",
        // "python_interpreter": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\playground\\venv\\Scripts\\python",
        "python_formatter": "black",
        "python_linter": "flake8",
        "python_format_on_save": false
    },
    "build_systems": [
        {
            "name": "Jorn_WindowTiler.sublime-project",
            "cmd": [
                "$project_path\\venv\\Scripts\\python",
                // "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\playground\\venv\\Scripts\\python",
                "-u",
                "${file}"
            ],
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "shell": true,
            "syntax": "Packages/build2/buildfile.sublime-syntax",
            "working_dir": "${project_path}"
        }
    ]
}