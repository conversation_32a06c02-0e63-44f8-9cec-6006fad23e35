# Project Brief: Window Tiler

## Overview
Jorn_WindowTiler is a Python utility for Windows that helps users organize their desktop workspace by automatically tiling windows in a grid pattern. The application identifies active windows, groups them by type or process, and arranges them neatly on the user's selected monitor.

## Core Requirements
- **Windows Operating System**: Relies on Windows-specific APIs
- **Python Environment**: Python 3.x with specific dependencies
- **Window Identification**: Detect Alt+Tab style top-level windows
- **Window Manipulation**: Move and resize windows programmatically
- **Classification System**: Group windows by type or by process
- **Multi-Monitor Support**: Work across multiple displays
- **Grid Configuration**: Allow customization of rows and columns
- **Minimized Window Handling**: Option to restore minimized windows

## Core Dependencies
- **pywin32**: For Windows API interactions (window management)
- **psutil**: For process information access

## Scope
- Command-line interface for window tiling operations
- Detection and grouping of windows based on window style and process information
- Support for multiple monitors with primary monitor identification
- Grid-based tiling with customizable rows and columns
- No permanent configuration storage (settings applied per session)
