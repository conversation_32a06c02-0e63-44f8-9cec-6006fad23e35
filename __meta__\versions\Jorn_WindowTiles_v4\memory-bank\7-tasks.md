# Tasks

This document outlines current and future tasks for the Jorn_WindowTiler project, organized by priority and category.

## High Priority

### Architectural Improvements
- [x] **Class-Based Architecture**: Refactor to the proposed class-based design
  - Create WindowTilerApp as the main application class ✅
  - Implement WindowManager to handle window operations ✅
  - Implement MonitorManager for display handling ✅
  - Implement LayoutManager for window arrangement strategies ✅
  - Create UserInterface class to separate UI from core logic ✅

### Core Functionality Improvements
- [ ] **Error Handling**: Add try/except blocks for window operations to prevent crashes
  - Handle cases where windows cannot be moved or resized
  - Add graceful fallback for inaccessible processes
- [ ] **CLI Enhancement**: Improve the command-line interface
  - Add default values in prompts
  - Implement input validation
  - Add help text and usage instructions
- [ ] **Window Filtering Refinement**: Improve window detection accuracy
  - Filter out transient dialogs and popups
  - Better handling of hidden windows
  - Account for virtual desktop/workspace context

### Documentation
- [ ] **Code Documentation**: Add comprehensive docstrings to all functions and classes
- [ ] **User Guide**: Create basic usage documentation
  - Installation instructions
  - Command-line parameter reference
  - Common use cases and examples

## Medium Priority

### Feature Enhancements
- [ ] **Configuration File**: Implement settings persistence
  - Save preferred grouping method
  - Store default grid dimensions
  - Remember monitor preferences
- [ ] **Layout Presets**: Create predefined layouts
  - Standard layouts (2x2, 3x3, etc.)
  - Special layouts (main + sidebar, etc.)
  - Custom named layouts

### Technical Improvements
- [ ] **Logging System**: Implement proper logging
  - Debug information for troubleshooting
  - Operation history for reference
  - Error logging for diagnostics
- [ ] **Window Classification**: Expand window type detection
  - Add more application categories
  - Implement custom classification rules
  - Handle edge cases for non-standard applications
- [ ] **Testing Framework**: Create basic unit and integration tests
  - Test window detection logic
  - Validate classification system
  - Ensure tiling operations work correctly

## Low Priority

### Future Features
- [ ] **GUI Wrapper**: Create a simple graphical interface
  - Visual monitor selection
  - Drag-and-drop window assignment
  - Live preview of layouts
- [ ] **Keyboard Shortcuts**: Implement hotkey support
  - Global shortcuts for quick tiling
  - Custom key bindings
  - Modifier keys for variations
- [ ] **Advanced Layouts**: Support for more sophisticated arrangements
  - Non-uniform grid layouts
  - Percentage-based sizing
  - Hierarchical layouts

### Experimental Ideas
- [ ] **Background Service**: Run as a Windows service for persistent access
- [ ] **Application Integration**: Hook into application launch events for automatic tiling
- [ ] **Multi-Desktop Support**: Handle Windows 10/11 virtual desktops properly
- [ ] **Plugin System**: Allow for custom extensions and integrations

## Technical Debt

- [ ] **Refactoring**: Clean up code structure and organization
  - ✅ Implement the class-based architecture documented in `9-classBasedArchitecture.md`
  - Extract constants and magic numbers to defined variables
  - Group related functionality into separate modules
  - Improve naming conventions for clarity
- [ ] **Performance Optimization**: Improve handling of large numbers of windows
  - Lazy loading of window properties
  - Caching process information
  - More efficient enumeration
- [ ] **DPI Awareness**: Add proper support for high-DPI displays
  - Scale coordinates and dimensions appropriately
  - Handle DPI changes between monitors
