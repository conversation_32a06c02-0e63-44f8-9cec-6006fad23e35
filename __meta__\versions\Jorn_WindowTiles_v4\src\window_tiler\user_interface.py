"""
User Interface module for Window Tiler application.
Handles command-line interaction with the user.
"""

import sys


class UserInterface:
    """
    <PERSON>les user interaction through the command line.
    """

    def __init__(self):
        pass

    def show_welcome(self):
        """Display welcome message and application information."""
        print("Gathering top-level alt-tab windows (including minimized) with psutil checks.")

    def show_window_count(self, count):
        """Display the number of windows found."""
        if count == 0:
            print("No windows found!")
            return False
        
        print(f"Total windows found: {count}")
        return True

    def show_grouping_options(self):
        """Display window grouping options to the user."""
        print("\nChoose grouping style:")
        print("1) By process name (e.g. 'chrome.exe')")
        print("2) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
        return input("Enter [1/2]: ").strip()

    def show_window_groups(self, groups, keys):
        """
        Display grouped windows to the user.
        
        Args:
            groups: Dictionary of window groups
            keys: Sorted list of group keys
            
        Returns:
            True if groups were displayed successfully
        """
        print("\nGroups Detected:")
        for idx, key in enumerate(keys):
            label = str(key)
            if hasattr(key, 'name'):  # Handle Enum types
                label = key.name
            print(f"{idx}) {label} -> {len(groups[key])} windows")

        if not keys:
            print("No groups found!")
            return False
        
        return True

    def get_group_selection(self, group_keys):
        """
        Get the user's group selection.
        
        Args:
            group_keys: List of available group keys
            
        Returns:
            Selected group key or None if selection failed
        """
        chosen = input("\nWhich group do you want to tile? Enter index: ").strip()
        try:
            chosen_idx = int(chosen)
            if 0 <= chosen_idx < len(group_keys):
                return group_keys[chosen_idx]
        except (ValueError, IndexError):
            print("Invalid choice, quitting.")
        
        return None

    def get_grid_dimensions(self):
        """
        Get grid dimensions from the user.
        
        Returns:
            Tuple of (rows, columns)
        """
        r = input("Number of rows [default=2]: ").strip()
        c = input("Number of columns [default=2]: ").strip()
        rows = int(r) if r.isdigit() else 2
        cols = int(c) if c.isdigit() else 2
        return rows, cols

    def get_restore_minimized_preference(self):
        """
        Ask if minimized windows should be restored.
        
        Returns:
            Boolean indicating if minimized windows should be restored
        """
        restore_prompt = input("Restore minimized windows? [Y/n]: ").strip().lower()
        return restore_prompt != 'n'

    def show_monitor_selection(self, wrapped_monitors):
        """
        Display available monitors and get user selection.
        
        Args:
            wrapped_monitors: List of WrappedMonitor objects
            
        Returns:
            Selected monitor index or None to use primary
        """
        print("\nMonitors Detected:")
        for w in wrapped_monitors:
            print(w)

        choice = input("Select a monitor index [blank=choose primary]: ").strip()
        if not choice:
            return None
            
        try:
            idx = int(choice)
            if 0 <= idx < len(wrapped_monitors):
                return idx
            else:
                print("Invalid index, defaulting to primary.")
                return None
        except ValueError:
            print("Invalid input, defaulting to primary monitor.")
            return None

    def show_result(self, count, monitor_info, grid_info):
        """
        Display the result of the tiling operation.
        
        Args:
            count: Number of windows tiled
            monitor_info: Information about the monitor used
            grid_info: Tuple of (rows, columns)
        """
        rows, cols = grid_info
        print(f"\nTiled {count} windows in a {rows}x{cols} grid on {monitor_info}.")
        print("\nDone.")

    def show_error(self, message):
        """Display an error message."""
        print(f"Error: {message}")

    def exit(self, code=0):
        """Exit the application with the given code."""
        sys.exit(code)
