"""
Main Window Tiler application class that orchestrates all components.
"""

from .window_manager import WindowManager
from .monitor_manager import MonitorManager
from .layout_manager import LayoutManager
from .user_interface import UserInterface


class WindowTilerApp:
    """
    Main application class that coordinates all Window Tiler operations.
    """

    def __init__(self):
        """Initialize application components."""
        self.window_manager = WindowManager()
        self.monitor_manager = MonitorManager()
        self.layout_manager = LayoutManager()
        self.ui = UserInterface()

    def run(self):
        """Execute the main application workflow."""
        try:
            # Show welcome message
            self.ui.show_welcome()

            # Detect windows
            self.window_manager.detect_windows(skip_explorer=True, min_size=2)
            window_count = self.window_manager.get_window_count()
            
            # Show window count and exit if none found
            if not self.ui.show_window_count(window_count):
                return

            # Get grouping preference
            grouping_choice = self.ui.show_grouping_options()
            
            # Group windows based on user preference
            if grouping_choice == "1":
                groups = self.window_manager.get_windows_by_process()
            else:
                groups = self.window_manager.get_windows_by_type()
            
            # Sort group keys for consistent display
            group_keys = sorted(groups.keys(), key=lambda k: str(k))
            
            # Show groups and get selection
            if not self.ui.show_window_groups(groups, group_keys):
                return
                
            # Get user's group selection
            group_key = self.ui.get_group_selection(group_keys)
            if group_key is None:
                return
                
            # Get grid dimensions
            rows, cols = self.ui.get_grid_dimensions()
            
            # Get restore minimized preference
            restore_minimized = self.ui.get_restore_minimized_preference()
            
            # Show monitor options and get selection
            wrapped_monitors = self.monitor_manager.get_wrapped_monitors()
            monitor_idx = self.ui.show_monitor_selection(wrapped_monitors)
            
            # Get selected monitor or primary if None
            if monitor_idx is not None:
                monitor = self.monitor_manager.get_monitor_by_index(monitor_idx)
            else:
                monitor = self.monitor_manager.get_primary_monitor()
            
            if not monitor:
                self.ui.show_error("No valid monitor selected.")
                return
                
            # Apply grid layout
            window_count = self.layout_manager.apply_grid_layout(
                groups[group_key], monitor, rows, cols, restore_minimized
            )
            
            # Show results
            self.ui.show_result(window_count, monitor.device, (rows, cols))
            
        except Exception as e:
            self.ui.show_error(f"An unexpected error occurred: {e}")
            
    def exit(self, code=0):
        """Exit the application."""
        self.ui.exit(code)
