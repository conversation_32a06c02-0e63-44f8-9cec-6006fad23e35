# =============================================================================
# [11:01] -> 30.05.2024: user menus
```python
def handle_list_windows(choice):
    """Handle the list windows options."""
    ...
def handle_close_duplicate_windows(choice):
    """Handle the close duplicate windows options."""
    ...
def handle_export_layout(choice):
    """Handle the export layout options."""
    ...
def handle_import_layout(choice):
    """Handle the import layout options."""
    ...
    options_handle_list_windows = {
        "Listing all windows...",
        "Listing explorer windows...",
        "Listing other windows..."
    }
    options_handle_close_duplicate_windows = {
        "Closing all duplicate windows...",
        "Closing duplicate explorer windows...",
        "Closing duplicate other windows..."
    }
    options_handle_export_layout = {
        "Exporting all open windows...",
        "Exporting explorer windows...",
        "Exporting other windows..."
    }
    options_handle_import_layout = {
        "Importing layout for all windows...",
        "Importing layout for explorer windows...",
        "Importing layout for other windows..."
    }
    options_main = [
        "List current layout",
        "Close duplicate windows",
        "Export Layout",
        "Import Layout",
        "Exit the window manager"
    ]
```


# =============================================================================
# [11:02] -> 30.05.2024: calling
```python
def main_menu():
    """Main function to run the CLI."""
    while True:

        main_choice = display_menu("Main Menu", main_options)

        if main_choice == "1":
            list_options = ["All windows", "Explorer windows", "Other windows", "Back"]
            list_choice = display_menu("List current layout", list_options)
            if list_choice != "4":
                handle_list_windows(list_choice)
        elif main_choice == "2":
            close_options = ["All duplicate windows", "Duplicate explorer windows", "Duplicate other windows", "Back"]
            close_choice = display_menu("Close duplicate windows", close_options)
            if close_choice != "4":
                handle_close_duplicate_windows(close_choice)
        elif main_choice == "3":
            export_options = ["Save all open windows", "Save open explorer windows", "Save open other windows", "Back"]
            export_choice = display_menu("Export Layout", export_options)
            if export_choice != "4":
                handle_export_layout(export_choice)
        elif main_choice == "4":
            import_options = ["Load layout for all windows", "Load layout for explorer windows", "Load layout for other windows", "Back"]
            import_choice = display_menu("Import Layout", import_options)
            if import_choice != "4":
                handle_import_layout(import_choice)
        elif main_choice == "5":
            print("Exiting the window manager.")
            sys.exit()
        else:
            print("Invalid choice. Please try again.")
```
