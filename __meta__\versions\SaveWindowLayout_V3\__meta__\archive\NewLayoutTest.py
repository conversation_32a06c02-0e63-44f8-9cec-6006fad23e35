# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
#
import psutil
# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process

class WindowType(Enum):
    """Enum class to define the window type"""
    SPECIAL_FOLDER = 1
    NORMAL_FOLDER = 2
    UNSPECIFIED = 3
    UNLINKED = 4
    UNKNOWN = 5


class WindowsShell:
    """
    Class for interacting with the Windows shell and accessing
    additional window data.
    """
    SHELL_APP_CLSID = 'Shell.Application'
    WINDOW_CLASS = 'CabinetWClass'
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]

    @classmethod
    def initialize(cls):
        """
        Initializes a COM object instance of the 'Shell.Application'
        CLSID/ProgID and retrieves special folder paths.
        """
        shell_windows_instance = win32com.client.Dispatch(cls.SHELL_APP_CLSID)
        shell_windows_instance_mapping = {
            window.HWND: window for window in shell_windows_instance.Windows()
        }

        csidl_namespaces = [shell_windows_instance.Namespace(constant) for constant in cls.CSIDL_CONSTANTS]
        valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]

        special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
        special_folders_mapping = {item[0]: item[1] for item in special_folders}

        return shell_windows_instance_mapping, special_folders_mapping

class Window:
    """
    Class representing a window, providing access to additional data
    such as process, type, and path.
    """
    def __init__(self, hwnd, shell_instance_mapping, special_folders_mapping):
        self._hwnd = hwnd
        self._placement = win32gui.GetWindowPlacement(self._hwnd)
        self._rect = win32gui.GetWindowRect(self._hwnd)

        self._shell_instance_mapping = shell_instance_mapping
        self._special_folders_mapping = special_folders_mapping

    @property
    def hwnd(self):
        """Returns the hwnd of the window."""
        return self._hwnd

    @property
    def visibility_state(self):
        """Returns the visibility state of the window."""
        return win32gui.IsWindowVisible(self._hwnd)

    @property
    def title(self):
        """Returns the title of the window."""
        return win32gui.GetWindowText(self._hwnd)

    @property
    def window_class(self):
        """Returns the class of the window."""
        return win32gui.GetClassName(self._hwnd)

    @property
    def controls_state(self):
        """Returns the state of the window's controls."""
        return self._placement[1]

    @property
    def position(self):
        """Returns the position of the window."""
        return (self._rect[0], self._rect[1])

    @property
    def size(self):
        """Returns the size of the window."""
        return (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

    @property
    def process(self):
        """Returns the process that the window belongs to."""
        # if not self._process:
            # self._get_additional_data()
        return self._process

    @property
    def process_id(self):
        """Returns the process id of the window."""
        if not self._process_id:
            self._get_additional_data()
        return self._process_id

    @property
    def create_cmd(self):
        """Returns the command used to create the process that the window belongs to."""
        if not self._create_cmd:
            self._get_additional_data()
        return self._create_cmd

    @property
    def type(self):
        """Returns the type of the window."""
        if not self._type:
            self._get_additional_data()
        return self._type

    # def _get_additional_data(self):
    #     """Retrieves additional data for the window, such as process, type, and path."""
    #     thread_id, process_id = win32process.GetWindowThreadProcessId(self._hwnd)
    #     process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, False, process_id)

    #     if process_handle:
    #         self._process = win32process.GetModuleFileNameEx(process_handle, 0)
    #         self._process_id = process_id

    #         if self.window_class == WindowsShell.WINDOW_CLASS:
    #             shell_instance = self._shell_instance_mapping[self._hwnd]
    #             shell_path = shell_instance.LocationURL

    #             if self.title in self._special_folders_mapping:
    #                 self._type = WindowType.SPECIAL_FOLDER
    #                 folder_path = self._special_folders_mapping[self.title]
    #                 folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
    #                 folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
    #                 command_prefix = 'Shell:' if folder_path_is_guid else ''
    #                 self._path = command_prefix + folder_path
    #             elif shell_path.startswith('file:///'):
    #                 self._type = WindowType.NORMAL_FOLDER
    #                 self._path = urllib.parse.unquote(shell_path[8:])
    #             else:
    #                 self._type = WindowType.UNSPECIFIED
    #                 self._path = shell_path
    #         else:
    #             self._type = WindowType.UNKNOWN
    #             self._path = None

    def _get_additional_data(self):
        """Retrieves additional data for the window."""
        pid, _ = win32process.GetWindowThreadProcessId(self._hwnd)
        self._process = psutil.Process(pid)
        self._process_id = pid
        self._create_cmd = ' '.join(self._process.cmdline())

        window_path = self._get_path()
        if window_path in self._special_folders_mapping.values():
            self._type = WindowType.SPECIAL_FOLDER
        elif os.path.isdir(window_path):
            self._type = WindowType.NORMAL_FOLDER
        elif self._is_unlinked():
            self._type = WindowType.UNLINKED
        else:
            self._type = WindowType.UNKNOWN

    def _get_path(self):
        """Retrieves the path of the window."""
        if self._hwnd in self._shell_instance_mapping:
            return self._shell_instance_mapping[self._hwnd].Document.Folder.Self.Path
        else:
            return None

    def _is_unlinked(self):
        """Checks if the window is unlinked."""
        if self._hwnd in self._shell_instance_mapping:
            window = self._shell_instance_mapping[self._hwnd]
            folder_items = window.Document.Folder.Items()
            return not any(item.IsLink for item in folder_items)
        else:
            return None

    def __repr__(self):
        """Returns a string representation of the window."""
        return f'<Window: hwnd={self._hwnd}, title={self.title}, process={self.process.name()}>'

# Initialize the WindowsShell instance
shell_instance_mapping, special_folders_mapping = WindowsShell.initialize()
def enum_callback(hwnd, _):
    if win32gui.IsWindowVisible(hwnd):
        open_windows = Window(hwnd, shell_instance_mapping, special_folders_mapping)
        result_windows.append(open_windows)

# Result
result_windows = []
# Enumerate all windows and pass them to the wm_enumerate_windows_callback method
win32gui.EnumWindows(enum_callback, None)
for x in result_windows:
    print(x.title)
    # print(dir(x))
    # try:
    print(x.process)
    # except:
        # pass
    # print(x.process)
