# config/categories.yaml

categories:
  coding:  # Category name: "coding"
    criteria: # Rules to identify windows belonging to this category
      app_names: ["Code.exe", "idea64.exe", "pycharm64.exe", "Sublime Text"] # Application executable names
      window_title_patterns: ["- VSCode", "IntelliJ IDEA", "PyCharm", "Sublime Text"] # Title patterns (e.g., for specific projects)
    layout: coding_layout # Layout to apply when this category is active (defined in layouts/)

  communication: # Category name: "communication"
    criteria:
      app_names: ["slack.exe", "discord.exe", "teams.exe", "zoom.exe"]
      window_title_patterns: ["Slack", "Discord", "Microsoft Teams", "Zoom Meeting"]
    layout: communication_layout # Layout to apply

  browser: # Category name: "browser"
    criteria:
      app_names: ["chrome.exe", "firefox.exe", "msedge.exe", "brave.exe"]
    layout: default_layout # Default layout can be shared

  reference: # Category for documentation, etc.
    criteria:
      window_title_patterns: ["Documentation", " - ReadMe", " - Help"] # Match titles containing "Documentation", "ReadMe", etc.
    layout: reference_layout

  media: # For media players
    criteria:
      app_names: ["spotify.exe", "vlc.exe", "mpv.exe"]
    layout: media_layout