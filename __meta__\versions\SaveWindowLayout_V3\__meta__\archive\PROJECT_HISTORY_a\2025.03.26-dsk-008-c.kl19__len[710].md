i'm currently working on the code for this utility, here's my current filestructure and code, please outline the next steps:

```
├── __init__.py
├── main.py
├── entrypoints
│   ├── __init__.py
│   └── window_manager_cli.py
└── workflow
    ├── __init__.py
    ├── 01_detection
    │   ├── __init__.py
    │   └── window_detector.py
    ├── 02_categorization
    │   ├── __init__.py
    │   └── window_categorizer.py
    ├── 03_layout
    │   ├── __init__.py
    │   └── layout_manager.py
    └── controllers
        ├── __init__.py
        └── window_controller.py
```

---

#### `__init__.py`

```python
    """
    Window Manager Package
    
    A comprehensive window management system for organizing, tracking, and manipulating
    windows across the desktop environment.
    
    This package provides a structured approach to window management through a staged workflow:
    - Detection and enumeration of windows
    - Categorization and classification
    - Layout management and positioning
    - Automation of window operations
    - State tracking and persistence
    - User interaction and control
    
    The design emphasizes clean separation of concerns, extensibility, and robust error handling.
    """
```

---

#### `main.py`

```python
    """
    Window Manager - Main Application
    
    This is the main entry point for the Window Manager application.
    It integrates all components of the system and provides the core functionality.
    
    Usage:
        python -m src.main [options]
    
    Options:
        --save NAME     Save current window layout to specified name
        --apply NAME    Apply window layout from specified name
        --list          List all open windows
        --list-layouts  List all saved layouts
        --monitor       Start monitoring active window (continuous mode)
        --find TITLE    Find windows by title substring
        --debug         Enable debug logging
    """
    
    import os
    import sys
    import argparse
    import time
    from pathlib import Path
    
    # Configure the application paths
    APP_DIR = Path(__file__).parent.parent
    CONFIG_DIR = APP_DIR / "config"
    LAYOUTS_DIR = CONFIG_DIR / "layouts"
    
    # Create necessary directories
    os.makedirs(LAYOUTS_DIR, exist_ok=True)
    
    # Set up logging
    try:
        from loguru import logger
    
        # Configure loguru logger
        log_file = APP_DIR / "logs" / "window_manager.log"
        os.makedirs(log_file.parent, exist_ok=True)
    
        logger.remove()  # Remove default logger
        logger.add(sys.stderr, level="INFO")  # Console output
        logger.add(
            log_file,
            rotation="10 MB",
            retention="1 week",
            level="DEBUG",
            backtrace=True,
            diagnose=True
        )
    
    except ImportError:
        import logging
    
        # Configure standard logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        logger = logging.getLogger(__name__)
    
    # Import the controller to manage all operations
    from workflow.controllers.window_controller import WindowController
    
    
    def setup_argparse():
        """Set up command line argument parsing."""
        parser = argparse.ArgumentParser(description="Window Manager Application")
    
        # Layout management
        parser.add_argument("--save", type=str, help="Save current window layout to specified name")
        parser.add_argument("--apply", type=str, help="Apply window layout from specified name")
        parser.add_argument("--list-layouts", action="store_true", help="List all saved layouts")
    
        # Window operations
        parser.add_argument("--list", action="store_true", help="List all open windows")
        parser.add_argument("--list-by-category", type=str, help="List windows by category")
        parser.add_argument("--find", type=str, help="Find windows by title substring")
        parser.add_argument("--monitor", action="store_true", help="Start monitoring active window")
    
        # Application settings
        parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
        return parser
    
    
    def list_windows(controller):
        """List all windows with their details."""
        windows = controller.get_all_windows()
        print(f"\nFound {len(windows)} windows:\n")
    
        # Format the output
        for hwnd, window in windows.items():
            title = window.title[:50] + "..." if len(window.title) > 50 else window.title
            if not title:
                title = "<No Title>"
    
            # Get position and size
            pos = window.base_info.position
            size = window.base_info.size
    
            # Get process info if available
            proc_info = ""
            if window.base_info.process:
                proc_info = f" - {window.base_info.process.exe_name} (PID: {window.base_info.process.pid})"
    
            print(f"[{window.category.name}] {title}{proc_info}")
            print(f"    Position: ({pos.x}, {pos.y}), Size: {size.width}x{size.height}")
            print(f"    HWND: {hwnd}, Class: {window.class_name}")
    
            # Add specialized information based on window category
            if hasattr(window, 'browser_type') and window.browser_type:
                print(f"    Browser: {window.browser_type.name}")
                if hasattr(window, 'url') and window.url:
                    print(f"    URL: {window.url}")
    
            if hasattr(window, 'location_path') and window.location_path:
                print(f"    Path: {window.location_path}")
    
            print()
    
    
    def list_windows_by_category(controller, category_name):
        """List windows filtered by category."""
        windows = controller.get_windows_by_category(category_name)
    
        if not windows:
            print(f"No windows found in category: {category_name}")
            return
    
        print(f"\nFound {len(windows)} windows in category {category_name}:\n")
    
        # Format the output
        for hwnd, window in windows.items():
            title = window.title[:50] + "..." if len(window.title) > 50 else window.title
            if not title:
                title = "<No Title>"
    
            # Get position and size
            pos = window.base_info.position
            size = window.base_info.size
    
            print(f"{title}")
            print(f"    Position: ({pos.x}, {pos.y}), Size: {size.width}x{size.height}")
            print(f"    HWND: {hwnd}, Class: {window.class_name}")
            print()
    
    
    def find_windows_by_title(controller, title_substring):
        """Find windows by title substring."""
        windows = controller.find_windows(title=title_substring)
    
        if not windows:
            print(f"No windows found with title containing: {title_substring}")
            return
    
        print(f"\nFound {len(windows)} windows with title containing '{title_substring}':\n")
    
        # Format the output
        for hwnd, window in windows.items():
            title = window.title[:50] + "..." if len(window.title) > 50 else window.title
            if not title:
                title = "<No Title>"
    
            # Get position and size
            pos = window.base_info.position
            size = window.base_info.size
    
            print(f"[{window.category.name}] {title}")
            print(f"    Position: ({pos.x}, {pos.y}), Size: {size.width}x{size.height}")
            print(f"    HWND: {hwnd}, Class: {window.class_name}")
            print()
    
    
    def monitor_active_window(controller):
        """
        Continuously monitor and display information about the active window.
        Press Ctrl+C to exit.
        """
        print("Monitoring active window. Press Ctrl+C to exit.\n")
    
        last_hwnd = None
    
        try:
            while True:
                # Get the currently active window
                active_window = controller.get_active_window()
    
                if active_window and active_window.base_info.hwnd != last_hwnd:
                    # Clear console
                    os.system('cls' if os.name == 'nt' else 'clear')
    
                    print("ACTIVE WINDOW:")
                    print(f"Title: {active_window.title}")
                    print(f"HWND: {active_window.base_info.hwnd}")
                    print(f"Class: {active_window.class_name}")
                    print(f"Category: {active_window.category.name}")
    
                    # Get position and size
                    pos = active_window.base_info.position
                    size = active_window.base_info.size
                    print(f"Position: ({pos.x}, {pos.y})")
                    print(f"Size: {size.width}x{size.height}")
    
                    # Get process info if available
                    if active_window.base_info.process:
                        print("\nPROCESS INFO:")
                        print(f"Name: {active_window.base_info.process.exe_name}")
                        print(f"PID: {active_window.base_info.process.pid}")
                        print(f"Path: {active_window.base_info.process.exe_path}")
    
                    # Display specialized info based on window type
                    if active_window.category.name == "BROWSER" and hasattr(active_window, 'url'):
                        print("\nBROWSER INFO:")
                        print(f"Browser Type: {active_window.browser_type.name if hasattr(active_window, 'browser_type') else 'Unknown'}")
                        print(f"URL: {active_window.url}")
    
                    elif active_window.category.name == "EXPLORER" and hasattr(active_window, 'location_path'):
                        print("\nEXPLORER INFO:")
                        print(f"Path: {active_window.location_path}")
    
                    # Update last handle
                    last_hwnd = active_window.base_info.hwnd
    
                # Slight pause to reduce CPU usage
                time.sleep(0.1)
    
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
    
    
    def list_saved_layouts(controller):
        """List all saved layouts."""
        layouts = controller.list_layouts()
    
        if not layouts:
            print("No saved layouts found.")
            return
    
        print(f"\nFound {len(layouts)} saved layouts:\n")
        for layout in layouts:
            print(f"  {layout}")
        print()
    
    
    def save_layout(controller, name):
        """Save current window layout."""
        try:
            # Refresh windows to ensure we have the latest information
            controller.refresh_windows()
    
            # Save the layout
            layout_path = controller.save_layout(name)
    
            if layout_path:
                print(f"Layout saved to: {layout_path}")
            else:
                print("Failed to save layout.")
    
        except Exception as e:
            logger.error(f"Error saving layout: {e}")
            print(f"Error saving layout: {e}")
    
    
    def apply_layout(controller, name):
        """Apply a saved window layout."""
        try:
            # Apply the layout
            count = controller.apply_layout(name)
    
            if count > 0:
                print(f"Successfully applied layout: {count} windows restored.")
            else:
                print("Failed to apply layout or no windows were restored.")
    
        except Exception as e:
            logger.error(f"Error applying layout: {e}")
            print(f"Error applying layout: {e}")
    
    
    def main():
        """Main application entry point."""
        parser = setup_argparse()
        args = parser.parse_args()
    
        # Update logging level if debug mode is enabled
        if args.debug:
            try:
                logger.remove()
                logger.add(sys.stderr, level="DEBUG")
                logger.debug("Debug logging enabled")
            except:
                logging.getLogger().setLevel(logging.DEBUG)
                logger.debug("Debug logging enabled")
    
        # Initialize the window controller
        controller = WindowController(layouts_dir=LAYOUTS_DIR)
    
        # Process command line arguments
        if args.list:
            controller.refresh_windows()
            list_windows(controller)
    
        elif args.list_by_category:
            controller.refresh_windows()
            list_windows_by_category(controller, args.list_by_category)
    
        elif args.find:
            controller.refresh_windows()
            find_windows_by_title(controller, args.find)
    
        elif args.monitor:
            monitor_active_window(controller)
    
        elif args.list_layouts:
            list_saved_layouts(controller)
    
        elif args.save:
            save_layout(controller, args.save)
    
        elif args.apply:
            apply_layout(controller, args.apply)
    
        else:
            # No arguments provided, show help
            parser.print_help()
    
    
    if __name__ == "__main__":
        main()
```

---

#### `entrypoints\__init__.py`

```python
    """
    Window Manager Entry Points
    
    Contains modules that serve as entry points for the window management system,
    including command-line interfaces, GUI applications, and service runners.
    """
```

---

#### `entrypoints\window_manager_cli.py`

```python
    #!/usr/bin/env python3
    """
    Window Manager CLI
    
    Command-line interface for the window management system, providing access to
    window management functionality through the terminal.
    
    This module allows users to:
    1. List all windows and their properties
    2. List windows by category
    3. Capture window layouts
    4. Restore window layouts
    5. Get information about specific windows
    
    The CLI offers both interactive and non-interactive modes for flexibility.
    """
    
    import os
    import sys
    import argparse
    import time
    from pathlib import Path
    import json
    from typing import Dict, List, Optional, Any, Union
    
    # Add parent directory to path to allow imports from our package
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.append(parent_dir)
    
    # Import our window management components
    from workflow.controllers.window_controller import WindowController
    from workflow.02_categorization.window_categorizer import WindowCategory
    
    # Import the logger
    try:
        from loguru import logger
        logger.remove()
        logger.add(sys.stderr, level="INFO")
    except ImportError:
        import logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
    
    
    def list_all_windows(controller: WindowController) -> None:
        """
        List all windows with their details.
    
        Args:
            controller: WindowController instance
        """
        windows = controller.get_all_windows()
        print(f"Found {len(windows)} windows:")
    
        # Group by category
        by_category = {}
        for window in windows.values():
            category = window.category
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(window)
    
        # Print categorized
        for category, category_windows in by_category.items():
            print(f"\n{category.name} ({len(category_windows)}):")
            for i, window in enumerate(category_windows, 1):
                print(f"  {i}. [{window.hwnd}] {window.title}")
    
    
    def list_windows_by_category(controller: WindowController, category: WindowCategory) -> None:
        """
        List windows of a specific category.
    
        Args:
            controller: WindowController instance
            category: Category to filter by
        """
        windows = controller.get_windows_by_category(category)
        print(f"Found {len(windows)} {category.name} windows:")
    
        for i, window in enumerate(windows, 1):
            print(f"{i}. [{window.hwnd}] {window.title}")
    
            # Show specialized info based on category
            if hasattr(window, 'location_path') and window.location_path:
                print(f"   Location: {window.location_path}")
            elif hasattr(window, 'browser_type'):
                print(f"   Browser: {window.browser_type.name}")
    
            if hasattr(window, 'process') and window.base_info.process:
                process = window.base_info.process
                if process.exe_name:
                    print(f"   Process: {process.exe_name} (PID: {process.pid})")
    
    
    def show_active_window(controller: WindowController) -> None:
        """
        Show details of the currently active window.
    
        Args:
            controller: WindowController instance
        """
        active = controller.get_active_window()
        if not active:
            print("No active window found")
            return
    
        print(f"Active window: [{active.hwnd}] {active.title}")
        print(f"Category: {active.category.name}")
        print(f"Class: {active.class_name}")
    
        # Show position and size
        pos = active.base_info.position
        size = active.base_info.size
        print(f"Position: ({pos.x}, {pos.y})")
        print(f"Size: {size.width} x {size.height}")
    
        # Show process info
        if active.base_info.process:
            proc = active.base_info.process
            print(f"Process: {proc.exe_name} (PID: {proc.pid})")
            if proc.exe_path:
                print(f"Executable: {proc.exe_path}")
    
        # Show specialized info based on category
        if active.category == WindowCategory.EXPLORER and hasattr(active, 'location_path'):
            print(f"Location: {active.location_path}")
            print(f"Is Desktop: {getattr(active, 'is_desktop', False)}")
    
        elif active.category == WindowCategory.BROWSER and hasattr(active, 'browser_type'):
            print(f"Browser Type: {active.browser_type.name}")
            print(f"URL: {getattr(active, 'url', 'Unknown')}")
    
        elif active.category == WindowCategory.DOCUMENT:
            if hasattr(active, 'app_name'):
                print(f"Application: {active.app_name}")
            if hasattr(active, 'document_name'):
                print(f"Document: {active.document_name}")
                print(f"Modified: {getattr(active, 'is_modified', False)}")
    
    
    def find_windows(controller: WindowController, pattern: str, by_title: bool = True) -> None:
        """
        Find windows matching a pattern.
    
        Args:
            controller: WindowController instance
            pattern: Pattern to search for
            by_title: If True, search by title; otherwise, search by process name
        """
        if by_title:
            windows = controller.find_windows_by_title(pattern)
            search_type = "title"
        else:
            windows = controller.find_windows_by_process(pattern)
            search_type = "process"
    
        print(f"Found {len(windows)} windows matching {search_type} '{pattern}':")
    
        for i, window in enumerate(windows, 1):
            print(f"{i}. [{window.hwnd}] {window.title}")
            if window.base_info.process:
                print(f"   Process: {window.base_info.process.exe_name}")
    
    
    def monitor_active_window(controller: WindowController, interval: float = 0.5) -> None:
        """
        Monitor the active window, updating at regular intervals.
    
        Args:
            controller: WindowController instance
            interval: Update interval in seconds
        """
        print("Monitoring active window. Press Ctrl+C to stop.")
        print("-" * 50)
    
        try:
            last_hwnd = None
            while True:
                active = controller.get_active_window()
                if active and active.hwnd != last_hwnd:
                    last_hwnd = active.hwnd
                    print(f"Active window: [{active.hwnd}] {active.title}")
                    print(f"Category: {active.category.name}")
                    print(f"Process: {active.process_name}")
                    print("-" * 50)
    
                time.sleep(interval)
    
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
    
    
    def main():
        """Main entry point for the CLI."""
        parser = argparse.ArgumentParser(description="Window Manager CLI")
    
        # Add command-line arguments
        parser.add_argument("--list", action="store_true", help="List all windows")
        parser.add_argument("--category", type=str, help="List windows of specific category")
        parser.add_argument("--active", action="store_true", help="Show active window details")
        parser.add_argument("--monitor", action="store_true", help="Monitor active window")
        parser.add_argument("--find", type=str, help="Find windows by title pattern")
        parser.add_argument("--process", type=str, help="Find windows by process name")
        parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
        args = parser.parse_args()
    
        # Configure logging
        if args.debug:
            try:
                logger.remove()
                logger.add(sys.stderr, level="DEBUG")
            except:
                logging.basicConfig(level=logging.DEBUG)
    
        # Create controller
        controller = WindowController()
    
        # Execute requested command
        if args.list:
            list_all_windows(controller)
    
        elif args.category:
            try:
                category = WindowCategory[args.category.upper()]
                list_windows_by_category(controller, category)
            except KeyError:
                print(f"Unknown category: {args.category}")
                print("Available categories:", ", ".join([c.name for c in WindowCategory]))
    
        elif args.active:
            show_active_window(controller)
    
        elif args.monitor:
            monitor_active_window(controller)
    
        elif args.find:
            find_windows(controller, args.find, by_title=True)
    
        elif args.process:
            find_windows(controller, args.process, by_title=False)
    
        else:
            # No command specified, use interactive mode
            print("Window Manager CLI")
            print("=" * 50)
            print("Select an option:")
            print("1. List all windows")
            print("2. Show active window details")
            print("3. Monitor active window")
            print("4. Find windows by title")
            print("5. Find windows by process")
            print("6. Exit")
    
            choice = input("\nEnter option number: ")
    
            if choice == "1":
                list_all_windows(controller)
            elif choice == "2":
                show_active_window(controller)
            elif choice == "3":
                monitor_active_window(controller)
            elif choice == "4":
                pattern = input("Enter title pattern: ")
                find_windows(controller, pattern, by_title=True)
            elif choice == "5":
                pattern = input("Enter process name: ")
                find_windows(controller, pattern, by_title=False)
            elif choice == "6":
                print("Exiting...")
            else:
                print("Invalid option!")
    
    
    if __name__ == "__main__":
        main()
```

---

#### `workflow\__init__.py`

```python
    """
    Window Manager Workflow Package
    
    This package contains the core workflow components of the window management system,
    organized into stages that process windows in a sequential pipeline.
    
    Main workflow stages:
    1. Detection: Discovers and extracts basic information about windows
    2. Categorization: Classifies windows into categories and extracts specialized information
    3. Layout: Manages window layouts, including saving and restoring arrangements
    
    Each stage builds upon the previous one, transforming window information into increasingly
    specialized and useful forms. The stages are orchestrated by controller components.
    """
```

---

#### `workflow\01_detection\__init__.py`

```python
    """
    Window Detection Package
    
    Contains modules for detecting windows and their basic properties.
    """
```

---

#### `workflow\01_detection\window_detector.py`

```python
    """
    Window Detection Module
    
    This module provides functionality to detect and gather information about all windows
    in the system using Win32 API. It serves as the first stage in the window management
    workflow, handling the raw detection of windows and collecting their basic properties.
    
    Key responsibilities:
    1. Enumerate all windows in the system
    2. Collect basic window properties (hwnd, title, class, etc.)
    3. Filter windows based on visibility, process, or other criteria
    4. Provide a consistent data structure for detected windows
    
    This module operates at the lowest level of the window management stack, focusing only
    on detection and basic property gathering. Window categorization, specialized window
    handling, and advanced operations are handled by subsequent workflow stages.
    """
    
    import os
    import sys
    import time
    from typing import Dict, List, Optional, Tuple, Set, Union, Callable
    from dataclasses import dataclass, field
    import ctypes
    from ctypes import wintypes
    import win32gui
    import win32con
    import win32process
    import win32api
    
    # Import the logger
    try:
        from loguru import logger
    except ImportError:
        import logging
        logger = logging.getLogger(__name__)
    
    # Define base data structures for window information
    @dataclass
    class WindowPosition:
        """Represents the position of a window on the screen."""
        x: int
        y: int
    
        def as_tuple(self) -> Tuple[int, int]:
            """Return the position as a tuple."""
            return (self.x, self.y)
    
    @dataclass
    class WindowSize:
        """Represents the size of a window."""
        width: int
        height: int
    
        def as_tuple(self) -> Tuple[int, int]:
            """Return the size as a tuple."""
            return (self.width, self.height)
    
    @dataclass
    class WindowPlacement:
        """Represents the full placement information of a window."""
        flags: int
        show_cmd: int
        min_position: Tuple[int, int]
        max_position: Tuple[int, int]
        normal_position: Tuple[int, int, int, int]
    
        @classmethod
        def from_win32_placement(cls, placement):
            """Create a WindowPlacement from a win32 WINDOWPLACEMENT structure."""
            return cls(
                flags=placement.flags,
                show_cmd=placement.showCmd,
                min_position=(placement.ptMinPosition.x, placement.ptMinPosition.y),
                max_position=(placement.ptMaxPosition.x, placement.ptMaxPosition.y),
                normal_position=(
                    placement.rcNormalPosition.left,
                    placement.rcNormalPosition.top,
                    placement.rcNormalPosition.right,
                    placement.rcNormalPosition.bottom
                )
            )
    
    @dataclass
    class ProcessInfo:
        """Information about the process that owns a window."""
        pid: int
        exe_path: str = ""
        exe_name: str = ""
    
        def __post_init__(self):
            """Initialize additional fields after creation."""
            if self.pid and not self.exe_path:
                try:
                    # Try to get the executable path from the process ID
                    hProcess = win32api.OpenProcess(
                        win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ,
                        False, self.pid
                    )
                    if hProcess:
                        self.exe_path = win32process.GetModuleFileNameEx(hProcess, 0)
                        self.exe_name = os.path.basename(self.exe_path)
                except Exception as e:
                    logger.debug(f"Error getting process info for PID {self.pid}: {e}")
    
    
    @dataclass
    class WindowInfo:
        """Basic information about a window."""
        hwnd: int
        title: str = ""
        class_name: str = ""
        is_visible: bool = False
        is_enabled: bool = False
        position: WindowPosition = field(default_factory=lambda: WindowPosition(0, 0))
        size: WindowSize = field(default_factory=lambda: WindowSize(0, 0))
        placement: Optional[WindowPlacement] = None
        process: Optional[ProcessInfo] = None
        parent_hwnd: int = 0
    
        def __post_init__(self):
            """Initialize fields that weren't provided."""
            if not self.title and self.hwnd:
                self.title = win32gui.GetWindowText(self.hwnd)
    
            if not self.class_name and self.hwnd:
                self.class_name = win32gui.GetClassName(self.hwnd)
    
            if self.hwnd:
                self.is_visible = bool(win32gui.IsWindowVisible(self.hwnd))
                self.is_enabled = bool(win32gui.IsWindowEnabled(self.hwnd))
    
                # Get window position and size
                try:
                    rect = win32gui.GetWindowRect(self.hwnd)
                    self.position = WindowPosition(rect[0], rect[1])
                    self.size = WindowSize(rect[2] - rect[0], rect[3] - rect[1])
                except Exception as e:
                    logger.debug(f"Error getting window rect for {self.hwnd}: {e}")
    
                # Get window placement
                try:
                    placement = win32gui.GetWindowPlacement(self.hwnd)
                    if placement:
                        placement_struct = win32gui.WINDOWPLACEMENT()
                        placement_struct.flags = placement[0]
                        placement_struct.showCmd = placement[1]
                        placement_struct.ptMinPosition = placement[2]
                        placement_struct.ptMaxPosition = placement[3]
                        placement_struct.rcNormalPosition = placement[4]
                        self.placement = WindowPlacement.from_win32_placement(placement_struct)
                except Exception as e:
                    logger.debug(f"Error getting window placement for {self.hwnd}: {e}")
    
                # Get process info
                try:
                    _, pid = win32process.GetWindowThreadProcessId(self.hwnd)
                    if pid:
                        self.process = ProcessInfo(pid=pid)
                except Exception as e:
                    logger.debug(f"Error getting thread/process ID for {self.hwnd}: {e}")
    
                # Get parent window
                try:
                    self.parent_hwnd = win32gui.GetParent(self.hwnd)
                except Exception as e:
                    logger.debug(f"Error getting parent for {self.hwnd}: {e}")
    
    
    class WindowDetector:
        """
        Detects and enumerates windows in the system.
    
        This class provides methods to find and collect information about all
        windows in the system, with options to filter by various criteria.
        """
    
        def __init__(self):
            """Initialize the window detector."""
            self._windows_info = {}
            self._last_refresh_time = 0
    
        def refresh(self, visible_only: bool = True) -> Dict[int, WindowInfo]:
            """
            Refresh the collection of windows.
    
            Args:
                visible_only: If True, only collect visible windows
    
            Returns:
                Dictionary mapping window handles to WindowInfo objects
            """
            self._windows_info = {}
    
            def enum_windows_callback(hwnd, results):
                """Callback for EnumWindows."""
                if visible_only and not win32gui.IsWindowVisible(hwnd):
                    return True
    
                # Skip certain system windows or windows without titles
                if visible_only:
                    title = win32gui.GetWindowText(hwnd)
                    if not title:
                        return True
    
                # Create a WindowInfo object for this window
                window_info = WindowInfo(hwnd=hwnd)
                self._windows_info[hwnd] = window_info
                return True
    
            try:
                win32gui.EnumWindows(enum_windows_callback, None)
                logger.debug(f"Found {len(self._windows_info)} windows")
                self._last_refresh_time = time.time()
            except Exception as e:
                logger.error(f"Error enumerating windows: {e}")
    
            return self._windows_info
    
        def get_window_info(self, hwnd: int) -> Optional[WindowInfo]:
            """
            Get information about a specific window by handle.
    
            Args:
                hwnd: Window handle
    
            Returns:
                WindowInfo object if the window exists, None otherwise
            """
            if hwnd in self._windows_info:
                return self._windows_info[hwnd]
    
            # Window not in cache, try to get it directly
            if win32gui.IsWindow(hwnd):
                window_info = WindowInfo(hwnd=hwnd)
                self._windows_info[hwnd] = window_info
                return window_info
    
            return None
    
        def get_active_window(self) -> Optional[WindowInfo]:
            """
            Get the currently active window.
    
            Returns:
                WindowInfo for the active window, or None if no active window
            """
            try:
                hwnd = win32gui.GetForegroundWindow()
                return self.get_window_info(hwnd)
            except Exception as e:
                logger.error(f"Error getting active window: {e}")
                return None
    
        def find_windows_by_title(self, title_pattern: str,
                                 exact_match: bool = False) -> List[WindowInfo]:
            """
            Find windows by title pattern.
    
            Args:
                title_pattern: Full or partial window title to search for
                exact_match: If True, require exact match; otherwise, use substring match
    
            Returns:
                List of matching WindowInfo objects
            """
            results = []
    
            for window in self._windows_info.values():
                if exact_match and window.title == title_pattern:
                    results.append(window)
                elif not exact_match and title_pattern.lower() in window.title.lower():
                    results.append(window)
    
            return results
    
        def find_windows_by_class(self, class_pattern: str,
                                 exact_match: bool = True) -> List[WindowInfo]:
            """
            Find windows by class name pattern.
    
            Args:
                class_pattern: Full or partial class name to search for
                exact_match: If True, require exact match; otherwise, use substring match
    
            Returns:
                List of matching WindowInfo objects
            """
            results = []
    
            for window in self._windows_info.values():
                if exact_match and window.class_name == class_pattern:
                    results.append(window)
                elif not exact_match and class_pattern.lower() in window.class_name.lower():
                    results.append(window)
    
            return results
    
        def find_windows_by_process_name(self, process_name: str) -> List[WindowInfo]:
            """
            Find windows belonging to a specific process by executable name.
    
            Args:
                process_name: Process executable name (e.g., "chrome.exe")
    
            Returns:
                List of matching WindowInfo objects
            """
            results = []
            process_name = process_name.lower()
    
            for window in self._windows_info.values():
                if (window.process and window.process.exe_name and
                    window.process.exe_name.lower() == process_name):
                    results.append(window)
    
            return results
    
        def find_child_windows(self, parent_hwnd: int) -> List[WindowInfo]:
            """
            Find all child windows of a parent window.
    
            Args:
                parent_hwnd: Handle of the parent window
    
            Returns:
                List of child WindowInfo objects
            """
            child_windows = []
    
            def enum_child_callback(hwnd, results):
                """Callback for EnumChildWindows."""
                window_info = WindowInfo(hwnd=hwnd)
                child_windows.append(window_info)
                return True
    
            try:
                win32gui.EnumChildWindows(parent_hwnd, enum_child_callback, None)
            except Exception as e:
                logger.error(f"Error enumerating child windows for {parent_hwnd}: {e}")
    
            return child_windows
    
    
    # Example usage
    if __name__ == "__main__":
        # Set up basic logging
        try:
            logger.remove()
            logger.add(sys.stderr, level="INFO")
        except:
            logging.basicConfig(level=logging.INFO)
    
        # Create detector and find windows
        detector = WindowDetector()
        windows = detector.refresh(visible_only=True)
    
        # Print summary
        print(f"Found {len(windows)} visible windows")
    
        # Get active window
        active = detector.get_active_window()
        if active:
            print(f"\nActive window: {active.title} ({active.hwnd})")
            print(f"Class: {active.class_name}")
            print(f"Position: ({active.position.x}, {active.position.y})")
            print(f"Size: {active.size.width} x {active.size.height}")
            if active.process:
                print(f"Process: {active.process.exe_name} (PID: {active.process.pid})")
    
        # List Chrome windows as an example
        chrome_windows = detector.find_windows_by_process_name("chrome.exe")
        if chrome_windows:
            print(f"\nFound {len(chrome_windows)} Chrome windows:")
            for i, window in enumerate(chrome_windows, 1):
                print(f"{i}. {window.title}")
```

---

#### `workflow\02_categorization\__init__.py`

```python
    """
    Window Categorization Package
    
    Contains modules for categorizing windows into specific types.
    """
```

---

#### `workflow\02_categorization\window_categorizer.py`

```python
    """
    Window Categorization Module
    
    This module builds upon the window detection functionality to categorize windows into
    specific types based on their class names, process information, and other properties.
    It serves as the second stage in the window management workflow, handling the intelligent
    classification of windows to enable specialized handling in subsequent stages.
    
    Key responsibilities:
    1. Define window categories (Explorer, Browser, Office, etc.)
    2. Categorize detected windows based on their properties
    3. Provide specialized window type classes with enhanced functionality
    4. Extract additional properties specific to each window type
    
    This module works with the window detection data to add semantic meaning and
    categorization, enabling more sophisticated operations in later workflow stages.
    """
    
    import os
    import sys
    import re
    from enum import Enum, auto
    from typing import Dict, List, Optional, Tuple, Set, Union, Callable
    from dataclasses import dataclass, field
    
    # Import from our detection module using relative imports
    from ..01_detection.window_detector import WindowInfo, WindowDetector
    
    # Import the logger
    try:
        from loguru import logger
    except ImportError:
        import logging
        logger = logging.getLogger(__name__)
    
    # Define window categories
    class WindowCategory(Enum):
        """Enumeration of window categories."""
        UNKNOWN = auto()
        EXPLORER = auto()
        BROWSER = auto()
        DOCUMENT = auto()
        DEVELOPMENT = auto()
        MEDIA = auto()
        COMMUNICATION = auto()
        SYSTEM = auto()
        UTILITY = auto()
    
    
    # Browser-specific window types
    class BrowserType(Enum):
        """Enumeration of known browser types."""
        UNKNOWN = auto()
        CHROME = auto()
        EDGE = auto()
        FIREFOX = auto()
        SAFARI = auto()
        OPERA = auto()
        BRAVE = auto()
        VIVALDI = auto()
    
    
    @dataclass
    class CategorizedWindowInfo:
        """Enhanced window information with categorization."""
        base_info: WindowInfo
        category: WindowCategory = WindowCategory.UNKNOWN
    
        @property
        def hwnd(self) -> int:
            """Get the window handle."""
            return self.base_info.hwnd
    
        @property
        def title(self) -> str:
            """Get the window title."""
            return self.base_info.title
    
        @property
        def class_name(self) -> str:
            """Get the window class name."""
            return self.base_info.class_name
    
        @property
        def process_name(self) -> str:
            """Get the process name, if available."""
            if self.base_info.process and self.base_info.process.exe_name:
                return self.base_info.process.exe_name
            return ""
    
    
    @dataclass
    class ExplorerWindowInfo(CategorizedWindowInfo):
        """Information specific to Explorer windows."""
        location_path: str = ""
        is_desktop: bool = False
        view_mode: str = ""
        selected_items: List[str] = field(default_factory=list)
    
        def __post_init__(self):
            """Initialize Explorer-specific properties."""
            self.category = WindowCategory.EXPLORER
    
            # Detect if this is the desktop window
            if self.class_name == "WorkerW" or self.class_name == "Progman":
                self.is_desktop = True
    
            # Try to extract location path from title
            if " - " in self.title:
                self.location_path = self.title.split(" - ")[0]
    
    
    @dataclass
    class BrowserWindowInfo(CategorizedWindowInfo):
        """Information specific to web browser windows."""
        browser_type: BrowserType = BrowserType.UNKNOWN
        url: str = ""
        is_private_mode: bool = False
    
        def __post_init__(self):
            """Initialize browser-specific properties."""
            self.category = WindowCategory.BROWSER
    
            # Detect browser type based on class and process
            process_name = self.process_name.lower()
            class_name = self.class_name.lower()
    
            if "chrome" in process_name or "chrome" in class_name:
                self.browser_type = BrowserType.CHROME
            elif "msedge" in process_name or "edge" in class_name:
                self.browser_type = BrowserType.EDGE
            elif "firefox" in process_name or "mozilla" in class_name:
                self.browser_type = BrowserType.FIREFOX
            elif "safari" in process_name:
                self.browser_type = BrowserType.SAFARI
            elif "opera" in process_name:
                self.browser_type = BrowserType.OPERA
            elif "brave" in process_name:
                self.browser_type = BrowserType.BRAVE
    
            # Try to extract URL or page info from title
            # Most browsers put page title first, then browser name
            if " - " in self.title and not self.title.startswith("http"):
                # Extract page title (assume format "Page Title - Browser Name")
                pass
    
    
    @dataclass
    class DocumentWindowInfo(CategorizedWindowInfo):
        """Information specific to document editing windows (Word, Excel, etc.)."""
        app_name: str = ""
        document_path: str = ""
        document_name: str = ""
        is_modified: bool = False
    
        def __post_init__(self):
            """Initialize document-specific properties."""
            self.category = WindowCategory.DOCUMENT
    
            # Detect Microsoft Office applications
            process_name = self.process_name.lower()
            if "winword" in process_name:
                self.app_name = "Microsoft Word"
            elif "excel" in process_name:
                self.app_name = "Microsoft Excel"
            elif "powerpnt" in process_name:
                self.app_name = "Microsoft PowerPoint"
            elif "acrobat" in process_name or "reader" in process_name:
                self.app_name = "Adobe Reader/Acrobat"
    
            # Try to extract document name from title
            # Most document apps use format "DocumentName - AppName"
            if " - " in self.title:
                parts = self.title.split(" - ")
                # Check if the document is modified (usually indicated by * prefix)
                if parts[0].startswith("*"):
                    self.is_modified = True
                    self.document_name = parts[0][1:]  # Remove the asterisk
                else:
                    self.document_name = parts[0]
    
    
    @dataclass
    class DevelopmentWindowInfo(CategorizedWindowInfo):
        """Information specific to development IDE windows."""
        ide_name: str = ""
        project_name: str = ""
        file_path: str = ""
    
        def __post_init__(self):
            """Initialize development-specific properties."""
            self.category = WindowCategory.DEVELOPMENT
    
            # Detect common IDEs
            process_name = self.process_name.lower()
            if "devenv" in process_name:
                self.ide_name = "Visual Studio"
            elif "code" in process_name:
                self.ide_name = "VS Code"
            elif "pycharm" in process_name:
                self.ide_name = "PyCharm"
            elif "idea" in process_name:
                self.ide_name = "IntelliJ IDEA"
            elif "sublime_text" in process_name:
                self.ide_name = "Sublime Text"
    
            # Try to extract project/file info from title
            # Many IDEs use formats like "FileName - ProjectName - IDE"
            if self.ide_name and " - " in self.title:
                parts = self.title.split(" - ")
                if len(parts) >= 3:
                    self.file_path = parts[0]
                    self.project_name = parts[1]
    
    
    class WindowCategorizer:
        """
        Categorizes detected windows into specific types.
    
        This class analyzes window properties to determine the category
        and creates the appropriate specialized window info objects.
        """
    
        def __init__(self, detector: Optional[WindowDetector] = None):
            """
            Initialize the window categorizer.
    
            Args:
                detector: Optional WindowDetector to use for window detection
            """
            self.detector = detector or WindowDetector()
            self._categorized_windows = {}
            self._window_category_mapping = self._build_category_mapping()
    
        def _build_category_mapping(self) -> Dict[str, Dict[str, WindowCategory]]:
            """
            Build mappings from window classes and process names to categories.
    
            Returns:
                Dictionary with mappings for classification
            """
            # Maps for classification by class name and process name
            class_map = {
                # Explorer windows
                "CabinetWClass": WindowCategory.EXPLORER,
                "ExploreWClass": WindowCategory.EXPLORER,
                "Progman": WindowCategory.EXPLORER,
                "WorkerW": WindowCategory.EXPLORER,
    
                # Browser windows
                "Chrome_WidgetWin_1": WindowCategory.BROWSER,
                "MozillaWindowClass": WindowCategory.BROWSER,
                "IEFrame": WindowCategory.BROWSER,
    
                # Document windows
                "OpusApp": WindowCategory.DOCUMENT,  # Word
                "XLMAIN": WindowCategory.DOCUMENT,   # Excel
                "PPTFrameClass": WindowCategory.DOCUMENT,  # PowerPoint
    
                # Development windows
                "SunAwtFrame": WindowCategory.DEVELOPMENT,  # Java apps
                "Notepad": WindowCategory.DEVELOPMENT,
                "ConsoleWindowClass": WindowCategory.DEVELOPMENT,  # Command prompt
    
                # Media windows
                "WMP Skin Host": WindowCategory.MEDIA,
                "iTunes": WindowCategory.MEDIA,
                "VLC": WindowCategory.MEDIA,
    
                # Communication windows
                "TConversationForm": WindowCategory.COMMUNICATION,  # Skype
                "TEAMS_FORM": WindowCategory.COMMUNICATION,  # Teams
    
                # System windows
                "Shell_TrayWnd": WindowCategory.SYSTEM,
                "Windows.UI.Core.CoreWindow": WindowCategory.SYSTEM,
    
                # Utility windows
                "CalcFrame": WindowCategory.UTILITY,  # Calculator
                "Notepad": WindowCategory.UTILITY,
            }
    
            process_map = {
                # Browsers
                "chrome.exe": WindowCategory.BROWSER,
                "msedge.exe": WindowCategory.BROWSER,
                "firefox.exe": WindowCategory.BROWSER,
                "brave.exe": WindowCategory.BROWSER,
                "opera.exe": WindowCategory.BROWSER,
    
                # Documents
                "winword.exe": WindowCategory.DOCUMENT,
                "excel.exe": WindowCategory.DOCUMENT,
                "powerpnt.exe": WindowCategory.DOCUMENT,
                "acrobat.exe": WindowCategory.DOCUMENT,
                "acrord32.exe": WindowCategory.DOCUMENT,
    
                # Development
                "devenv.exe": WindowCategory.DEVELOPMENT,
                "code.exe": WindowCategory.DEVELOPMENT,
                "pycharm64.exe": WindowCategory.DEVELOPMENT,
                "idea64.exe": WindowCategory.DEVELOPMENT,
                "sublime_text.exe": WindowCategory.DEVELOPMENT,
                "eclipse.exe": WindowCategory.DEVELOPMENT,
    
                # Media
                "vlc.exe": WindowCategory.MEDIA,
                "spotify.exe": WindowCategory.MEDIA,
                "wmplayer.exe": WindowCategory.MEDIA,
    
                # Communication
                "teams.exe": WindowCategory.COMMUNICATION,
                "slack.exe": WindowCategory.COMMUNICATION,
                "skype.exe": WindowCategory.COMMUNICATION,
                "outlook.exe": WindowCategory.COMMUNICATION,
    
                # Utilities
                "calc.exe": WindowCategory.UTILITY,
                "notepad.exe": WindowCategory.UTILITY,
                "mspaint.exe": WindowCategory.UTILITY,
                "taskmgr.exe": WindowCategory.UTILITY,
            }
    
            return {
                "class_name": class_map,
                "process_name": process_map
            }
    
        def refresh(self, visible_only: bool = True) -> Dict[int, CategorizedWindowInfo]:
            """
            Refresh and categorize all windows.
    
            Args:
                visible_only: If True, only collect visible windows
    
            Returns:
                Dictionary mapping window handles to CategorizedWindowInfo objects
            """
            # Get the latest windows
            windows = self.detector.refresh(visible_only=visible_only)
            self._categorized_windows = {}
    
            # Categorize each window
            for hwnd, window_info in windows.items():
                categorized = self.categorize_window(window_info)
                self._categorized_windows[hwnd] = categorized
    
            logger.debug(f"Categorized {len(self._categorized_windows)} windows")
            return self._categorized_windows
    
        def categorize_window(self, window_info: WindowInfo) -> CategorizedWindowInfo:
            """
            Categorize a single window.
    
            Args:
                window_info: Basic window information
    
            Returns:
                Categorized window information with the appropriate type
            """
            # Determine the category
            category = self._determine_category(window_info)
    
            # Create the appropriate specialized window info object
            if category == WindowCategory.EXPLORER:
                return ExplorerWindowInfo(base_info=window_info)
            elif category == WindowCategory.BROWSER:
                return BrowserWindowInfo(base_info=window_info)
            elif category == WindowCategory.DOCUMENT:
                return DocumentWindowInfo(base_info=window_info)
            elif category == WindowCategory.DEVELOPMENT:
                return DevelopmentWindowInfo(base_info=window_info)
            else:
                # Generic categorized window
                return CategorizedWindowInfo(base_info=window_info, category=category)
    
        def _determine_category(self, window_info: WindowInfo) -> WindowCategory:
            """
            Determine the category of a window.
    
            Args:
                window_info: Basic window information
    
            Returns:
                Determined window category
            """
            # Check by class name
            if window_info.class_name in self._window_category_mapping["class_name"]:
                return self._window_category_mapping["class_name"][window_info.class_name]
    
            # Check by process name
            if (window_info.process and window_info.process.exe_name and
                window_info.process.exe_name in self._window_category_mapping["process_name"]):
                return self._window_category_mapping["process_name"][window_info.process.exe_name]
    
            # Use title-based heuristics for common window types
            title = window_info.title.lower()
    
            # Explorer windows typically end with "- File Explorer"
            if title.endswith("- file explorer"):
                return WindowCategory.EXPLORER
    
            # Browser windows often contain browser names
            if any(browser in title for browser in ["chrome", "firefox", "edge", "safari", "opera"]):
                return WindowCategory.BROWSER
    
            # Development windows often contain code file extensions or IDE names
            if any(ext in title for ext in [".py", ".js", ".html", ".css", ".cpp", ".cs"]):
                return WindowCategory.DEVELOPMENT
    
            # Default to unknown
            return WindowCategory.UNKNOWN
    
        def get_windows_by_category(self, category: WindowCategory) -> List[CategorizedWindowInfo]:
            """
            Get all windows of a specific category.
    
            Args:
                category: Category to filter by
    
            Returns:
                List of windows in the specified category
            """
            return [window for window in self._categorized_windows.values()
                    if window.category == category]
    
        def get_active_window(self) -> Optional[CategorizedWindowInfo]:
            """
            Get the currently active window.
    
            Returns:
                Categorized info for the active window, or None if no active window
            """
            active_window = self.detector.get_active_window()
            if active_window:
                # Check if we already have a categorized version
                if active_window.hwnd in self._categorized_windows:
                    return self._categorized_windows[active_window.hwnd]
    
                # Otherwise, categorize it
                return self.categorize_window(active_window)
    
            return None
    
    
    # Example usage
    if __name__ == "__main__":
        # Set up basic logging
        try:
            logger.remove()
            logger.add(sys.stderr, level="INFO")
        except:
            logging.basicConfig(level=logging.INFO)
    
        # Create categorizer and find windows
        categorizer = WindowCategorizer()
        windows = categorizer.refresh(visible_only=True)
    
        # Print summary by category
        category_counts = {}
        for window in windows.values():
            category = window.category
            if category not in category_counts:
                category_counts[category] = 0
            category_counts[category] += 1
    
        print("Windows by category:")
        for category, count in category_counts.items():
            print(f"  {category.name}: {count}")
    
        # Get active window
        active = categorizer.get_active_window()
        if active:
            print(f"\nActive window: {active.title} ({active.category.name})")
    
            # Show specialized info based on window type
            if active.category == WindowCategory.EXPLORER:
                explorer_window = active  # It's already an ExplorerWindowInfo
                print(f"Location: {explorer_window.location_path}")
                print(f"Is Desktop: {explorer_window.is_desktop}")
    
            elif active.category == WindowCategory.BROWSER:
                browser_window = active  # It's already a BrowserWindowInfo
                print(f"Browser Type: {browser_window.browser_type.name}")
                print(f"URL: {browser_window.url}")
    
            elif active.category == WindowCategory.DOCUMENT:
                document_window = active  # It's already a DocumentWindowInfo
                print(f"Application: {document_window.app_name}")
                print(f"Document: {document_window.document_name}")
                print(f"Modified: {document_window.is_modified}")
    
            elif active.category == WindowCategory.DEVELOPMENT:
                dev_window = active  # It's already a DevelopmentWindowInfo
                print(f"IDE: {dev_window.ide_name}")
                print(f"Project: {dev_window.project_name}")
                print(f"File: {dev_window.file_path}")
```

---

#### `workflow\03_layout\__init__.py`

```python
    """
    Window Layout Package
    
    Contains modules for managing window layouts, including saving, loading, and applying layouts.
    """
```

---

#### `workflow\03_layout\layout_manager.py`

```python
     
```

---

#### `workflow\controllers\__init__.py`

```python
    """
    Window Controller Package
    
    Contains modules for coordinating between different stages of the window management workflow.
    """
```

---

#### `workflow\controllers\window_controller.py`

```python
    """
    Window Controller Module
    
    This module provides the central controller for window management operations,
    coordinating between different stages of the window management workflow.
    
    Key responsibilities:
    1. Initialize and coordinate workflow components
    2. Provide a high-level API for window management operations
    3. Handle error logging and recovery
    4. Maintain system state
    """
    
    import os
    import time
    from pathlib import Path
    from typing import Dict, List, Optional, Any, Union, Set
    
    # Import workflow components
    from ..01_detection.window_detector import WindowDetector, WindowInfo
    from ..02_categorization.window_categorizer import WindowCategorizer, CategorizedWindowInfo, WindowCategory
    from ..03_layout.layout_manager import LayoutManager
    
    # Try to import logger
    try:
        from loguru import logger
    except ImportError:
        import logging
        logger = logging.getLogger(__name__)
    
    
    class WindowController:
        """
        Central controller for window management operations.
    
        This class coordinates between different stages of the window management workflow,
        providing a high-level API for window-related operations.
        """
    
        def __init__(self, layouts_dir: Optional[str] = None):
            """
            Initialize the window controller.
    
            Args:
                layouts_dir: Optional directory for storing layout files
            """
            # Initialize workflow components
            self.detector = WindowDetector()
            self.categorizer = WindowCategorizer()
            self.layout_manager = LayoutManager(layouts_dir)
    
            # Initialize state
            self.windows: Dict[int, CategorizedWindowInfo] = {}
            self.active_window: Optional[CategorizedWindowInfo] = None
    
            # Initial window refresh
            self.refresh_windows()
    
            logger.debug("WindowController initialized")
    
        def refresh_windows(self) -> Dict[int, CategorizedWindowInfo]:
            """
            Refresh the list of windows.
    
            Returns:
                Dict of window handles to categorized window information
            """
            # Detect all windows
            raw_windows = self.detector.detect_windows()
            logger.debug(f"Detected {len(raw_windows)} windows")
    
            # Categorize windows
            self.windows = self.categorizer.categorize_windows(raw_windows)
            logger.debug(f"Categorized {len(self.windows)} windows")
    
            # Update active window
            active_hwnd = self.detector.get_active_window_handle()
            self.active_window = self.windows.get(active_hwnd)
    
            return self.windows
    
        def get_all_windows(self) -> Dict[int, CategorizedWindowInfo]:
            """
            Get all detected windows.
    
            Returns:
                Dict of window handles to categorized window information
            """
            return self.windows
    
        def get_active_window(self) -> Optional[CategorizedWindowInfo]:
            """
            Get the currently active window.
    
            Returns:
                Active window info or None if not found
            """
            # Always get the latest active window handle
            active_hwnd = self.detector.get_active_window_handle()
    
            # If active window isn't in our list or has changed, refresh
            if active_hwnd not in self.windows or active_hwnd != (self.active_window.base_info.hwnd if self.active_window else None):
                self.active_window = self.windows.get(active_hwnd)
    
                # If still not found, try a full refresh
                if not self.active_window:
                    self.refresh_windows()
                    self.active_window = self.windows.get(active_hwnd)
    
            return self.active_window
    
        def get_windows_by_category(self, category_name: str) -> Dict[int, CategorizedWindowInfo]:
            """
            Get windows filtered by category.
    
            Args:
                category_name: Name of the category to filter by
    
            Returns:
                Dict of window handles to categorized window information
            """
            try:
                # Try to convert string to enum
                category = WindowCategory[category_name.upper()]
    
                # Filter windows by category
                return {
                    hwnd: window for hwnd, window in self.windows.items()
                    if window.category == category
                }
    
            except (KeyError, ValueError):
                logger.error(f"Invalid category: {category_name}")
                return {}
    
        def find_windows(self,
                        title: Optional[str] = None,
                        class_name: Optional[str] = None,
                        process_name: Optional[str] = None) -> Dict[int, CategorizedWindowInfo]:
            """
            Find windows by various criteria.
    
            Args:
                title: Substring to match in window title
                class_name: Substring to match in window class name
                process_name: Substring to match in process name
    
            Returns:
                Dict of window handles to categorized window information
            """
            result = {}
    
            for hwnd, window in self.windows.items():
                match = True
    
                # Match title if provided
                if title and (not window.title or title.lower() not in window.title.lower()):
                    match = False
    
                # Match class name if provided
                if match and class_name and (not window.class_name or class_name.lower() not in window.class_name.lower()):
                    match = False
    
                # Match process name if provided
                if match and process_name and (
                    not window.base_info.process or
                    not window.base_info.process.exe_name or
                    process_name.lower() not in window.base_info.process.exe_name.lower()
                ):
                    match = False
    
                # Add to result if all criteria match
                if match:
                    result[hwnd] = window
    
            return result
    
        # Layout management methods
        def save_layout(self, name: Optional[str] = None) -> str:
            """
            Save the current window layout.
    
            Args:
                name: Optional name for the layout
    
            Returns:
                Path to the saved layout file
            """
            # Refresh windows to ensure we have the latest information
            self.refresh_windows()
    
            # Save the layout
            layout_path = self.layout_manager.save_layout(self.windows, name)
    
            if layout_path:
                logger.info(f"Saved layout to {layout_path}")
            else:
                logger.error("Failed to save layout")
    
            return layout_path
    
        def apply_layout(self,
                        layout_name: str,
                        restore_explorer: bool = True,
                        restore_browsers: bool = True,
                        restore_documents: bool = True,
                        restore_others: bool = True) -> int:
            """
            Apply a saved window layout.
    
            Args:
                layout_name: Name of the layout to apply
                restore_explorer: Whether to restore Explorer windows
                restore_browsers: Whether to restore browser windows
                restore_documents: Whether to restore document windows
                restore_others: Whether to restore other windows
    
            Returns:
                Number of windows successfully restored
            """
            # Apply the layout
            count = self.layout_manager.apply_layout(
                layout_name,
                restore_explorer=restore_explorer,
                restore_browsers=restore_browsers,
                restore_documents=restore_documents,
                restore_others=restore_others
            )
    
            # Refresh window list after applying layout
            self.refresh_windows()
    
            if count > 0:
                logger.info(f"Applied layout {layout_name}: {count} windows restored")
            else:
                logger.warning(f"Failed to apply layout {layout_name} or no windows were restored")
    
            return count
    
        def list_layouts(self) -> List[str]:
            """
            Get a list of saved layouts.
    
            Returns:
                List of layout filenames
            """
            return self.layout_manager.list_layouts()
    
    # Example usage
    if __name__ == "__main__":
        # Create a controller
        controller = WindowController()
    
        # Get all windows
        windows = controller.get_all_windows()
        print(f"Found {len(windows)} windows")
    
        # Find Chrome windows as an example
        chrome_windows = controller.find_windows(process_name="chrome.exe")
        if chrome_windows:
            print(f"\nFound {len(chrome_windows)} Chrome windows:")
            for i, window in enumerate(chrome_windows.values(), 1):
                print(f"{i}. {window.title}")
```