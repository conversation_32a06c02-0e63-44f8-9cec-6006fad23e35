# Project Lineage

This document traces the evolution of the SaveWindowLayout project, documenting its progression through various stages of development.

## Overview

The SaveWindowLayout project appears to be a utility for saving and restoring window layouts on Windows systems.

## Chronological Development

### 2025.03.26-dsk-001-a: Initial Concept

This file outlines the philosophy behind personalized window managers, focusing on:

1. **User-Centric Adaptability** - Adapting technology to users rather than forcing users to adapt to interfaces
2. **Efficient Spatial Utilization** - Optimal use of screen space with dynamic tiling and layouts
3. **Streamlined Interaction** - Minimizing friction with keyboard-driven controls and automation
4. **Intentional Focus Management** - Managing user attention through z-order control and minimizing distractions
5. **Task-Oriented State Management** - Saving and restoring application states for workflow continuity
6. **Modularity & Extensibility** - Allowing customization through plugins and scripting
7. **Transparency and Minimalism** - Reducing visual clutter for clarity and efficiency

### 2025.03.26-dsk-001-b: Initial File Structure Concept

This file proposes a file structure that inherently embodies the functionality of the window manager, with a hierarchical representation that corresponds to the order of operations:

```
window-manager-project/
├── README.md
├── requirements.txt
├── config/
├── entry_points/
└── workflow/
    ├── 01_detection/
    ├── 02_categorization/
    ├── 03_layout/
    ├── 04_state_management/
    └── 05_interaction/
```

### 2025.03.26-dsk-001-c: Refined File Structure

This file builds upon the previous structure, providing more detail and mapping existing files to the new structure:

```
window-manager-project/
├── README.md
├── requirements.txt
├── config/
│   ├── categories.yaml
│   ├── layouts/
│   └── settings.yaml
├── scripts/
│   ├── save_layout.bat
│   ├── apply_layout.bat
│   ├── window_monitor.bat
│   └── window_manager.bat
└── workflow/
    ├── __init__.py
    ├── 01_detection/
    ├── 02_categorization/
    ├── 03_layout/
    ├── 04_automation/
    ├── 05_state_management/
    └── 06_interaction/
```

This structure introduces a more detailed breakdown of the project files and their organization, with specific files mapped to their new locations.

### 2025.03.26-dsk-002-a to 2025.03.26-dsk-002-f: Detailed Project Structure and Configuration

These files provide a more detailed exploration of the proposed file structure and how it would work in practice.

#### File 2025.03.26-dsk-002-b: Discussion on Project Structure Refinement

This file contains a lengthy discussion about the most critical aspects of the project and proposes a refined file structure that:

1. Uses sequential numbering to indicate workflow progression
2. Employs clear, descriptive folder names
3. Renames "scripts/" to "entrypoints/" to better reflect their purpose
4. Creates a hierarchical structure that maps directly to the workflow (Detection → Categorization → Layout → Automation → State → Interaction)

It also proposes a structured approach to file naming that uses concise numeric prefixes to indicate order within each module.

#### File 2025.03.26-dsk-002-c: Configuration System Example

This file provides a detailed example of how the configuration system would work, specifically focusing on:

-   How window categories are defined in YAML configuration files
-   How layouts are defined per category
-   How Python code would implement the categorization logic based on these configuration files

#### Files 2025.03.26-dsk-002-d and 2025.03.26-dsk-002-e: Configuration File Examples

These files provide examples of configuration files in YAML and JSON format, showing how window categories would be defined, including:

-   Rules for identifying windows (app names, window title patterns)
-   Layout assignments for each category

#### File 2025.03.26-dsk-002-f: Final Proposed File Structure

This file presents the final proposed file structure, incorporating all the refinements discussed in the previous files:

```
window-manager-project/
├── README.md
├── requirements.txt
├── config/
│   ├── settings.yaml
│   ├── categories.yaml
│   └── layouts/
├── entrypoints/
│   ├── 01_run_monitor.bat
│   ├── 02_save_current_layout.bat
│   ├── 03_apply_layout.bat
│   └── 04_launch_window_manager.bat
└── workflow/
    ├── __init__.py
    ├── 01_detection/
    ├── 02_categorization/
    ├── 03_layout/
    ├── 04_automation/
    ├── 05_state/
    └── 06_interaction/
```

The key insights from these files are:

1. The importance of making the file structure inherently represent the workflow
2. The value of a robust configuration system for flexibility and user customization
3. The need for a clear modular design that separates concerns and allows for easy extension

### 2025.03.26-dsk-003-a to 2025.03.26-dsk-003-e: Controller-Based Architecture and File Naming Schemes

These files explore different variations of the project structure with a focus on creating intuitive controllers that interface between the different workflow stages.

#### File 2025.03.26-dsk-003-a and 2025.03.26-dsk-003-c: Expanded File Structure with Interface Files

These files present a more detailed structure that includes interface.py files in each workflow stage to define the public API for that stage:

```
workflow/
├── 01_detection/
│   ├── __init__.py
│   ├── interface.py              # Public interface definition
│   ├── window.py
│   ├── detector.py
│   ├── explorer.py
│   └── monitor.py
├── 02_categorization/
│   ├── __init__.py
│   ├── interface.py              # Public interface definition
│   ├── window_types.py
│   ├── categorizer.py
│   ├── rules.py
│   └── filters.py
...
```

#### File 2025.03.26-dsk-003-b: Numbered File Names Approach

This file explores a variation where both directories and files use numeric prefixes to indicate their order in the workflow:

```
workflow/
├── 01_detection/
│   ├── 01_foundation.py
│   ├── 02_monitor_loop.py
│   ├── 03_inspector_utils.py
│   └── 04_window_explorer.py
├── 02_categorization/
│   ├── 01_types.py
│   ├── 02_filter_rules.py
│   └── 03_main_categorizer.py
...
```

#### File 2025.03.26-dsk-003-d: Simplified File Names Approach

This file compares a more minimalist approach to file naming while still maintaining the sequence with numeric prefixes:

```
workflow/
├── 01_detection/
│   ├── 01_base.py
│   ├── 02_monitor.py
│   ├── 03_inspector.py
│   └── 04_explorer.py
├── 02_categorization/
│   ├── 01_types.py
│   ├── 02_filters.py
│   └── 03_categorizer.py
...
```

#### File 2025.03.26-dsk-003-e: Introduction of Controllers Layer

This file introduces a new "controllers" directory that contains controller modules responsible for bridging between the different workflow stages:

```
workflow/
├── __init__.py
├── controllers/                  # Controllers bridging entrypoints and workflows
│   ├── __init__.py
│   ├── hotkey_controller.py
│   ├── interaction_controller.py # Controls State ↔ Interaction workflows
│   ├── automation_controller.py  # Controls Layout ↔ Automation workflows
│   ├── cli_controller.py
│   ├── interactive_controller.py
│   ├── layout_controller.py      # Controls Categorization ↔ Layout workflows
│   ├── monitor_controller.py     # Controls Detection ↔ Categorization workflows
│   └── state_controller.py       # Controls Automation ↔ State workflows
├── 01_detection/
...
```

This controllers layer serves as an intermediary between the entrypoints (user interface) and the core workflow modules, creating a more intuitive way to handle the transitions between different stages of the workflow.

### 2025.03.26-dsk-004-a to 2025.03.26-dsk-004-d: Code Implementation and Testing Strategy

These files provide a detailed exploration of how to implement the code in an intuitive manner and outline a testing strategy for the project.

#### File 2025.03.26-dsk-004-a: Inherently Intuitive Code Design

This file provides detailed examples of how to write intuitive code within the project structure, focusing on:

1. **Clear Abstraction with Interfaces** - Using interface.py files to define clear contracts between modules
2. **Data-Driven Configuration Loading** - Dynamically loading configurations from JSON files at runtime
3. **Event-Driven Communication** - Using controllers as event handlers to orchestrate workflow
4. **Descriptive Naming and Docstrings** - Using consistent and clear naming conventions with comprehensive documentation
5. **Type Hinting** - Using Python's type hints to improve readability and catch errors early
6. **Dependency Injection** - Passing dependencies explicitly as arguments rather than using global state

It includes code examples for:

-   A layout controller that orchestrates between categorization and layout application
-   Interface definitions for categorization and layout management
-   How to structure tests for different levels of functionality

#### File 2025.03.26-dsk-004-b and 2025.03.26-dsk-004-c: Refined Project Structure

These files present a highly refined project structure that combines all the previous improvements, with numbered controllers that correspond to workflow stages and numbered files within each module:

```
workflow/
├── controllers/
│   ├── 01_monitor_controller.py     # Detection ↔ Categorization controller
│   ├── 02_layout_controller.py      # Categorization ↔ Layout controller
│   ├── 03_automation_controller.py  # Layout ↔ Automation controller
│   ├── 04_state_controller.py       # Automation ↔ State controller
│   ├── 05_interaction_controller.py # State ↔ Interaction controller
...
```

#### File 2025.03.26-dsk-004-d: Testing Strategy

This file, along with parts of 2025.03.26-dsk-004-a, outlines a comprehensive testing strategy for the project, including:

1. **Unit Tests** - Testing individual modules and functions in isolation
2. **Integration Tests** - Testing interactions between modules, especially through controllers
3. **Configuration Tests** - Testing the loading and validation of configuration files
4. **End-to-End/Workflow Tests** - Testing complete user workflows from entry points to final actions

It also proposes a test directory structure:

```
tests/
├── unit/                   # Unit tests
├── integration/            # Integration tests
│   └── test_controllers/   # Tests for controller integration
├── config/                 # Configuration tests
└── workflows/              # End-to-end/workflow tests
```

The key insights from these files are:

1. The importance of clear interfaces between modules
2. The value of controllers for orchestrating the workflow
3. The need for comprehensive testing at multiple levels
4. The benefit of descriptive naming and documentation

### 2025.03.26-dsk-005-a to 2025.03.26-dsk-005-c: Example Code Implementation

These files provide concrete code examples for how to implement the project in a way that is intuitive, modular, and easy to maintain.

#### File 2025.03.26-dsk-005-a: Stage-by-Stage Example Implementation

This file provides detailed code examples for each stage of the workflow, including:

-   **Stage 1: Detection** - Classes for window representation and functions for detecting windows
-   **Stage 2: Categorization** - Logic for categorizing windows based on rules
-   **Stage 3: Layout** - Functions for applying layouts to windows
-   **Stage 4: Automation** - Actions for manipulating windows
-   **Stage 5: State** - State management for saving and loading window arrangements
-   **Stage 6: Interaction** - CLI and other interfaces for user interaction

It also shows how controllers can orchestrate the workflow between stages, and provides examples of how the full pipeline can be tied together with a high-level orchestrator.

#### File 2025.03.26-dsk-005-b: Unified Interfaces and Dynamic Configuration

This file expands on the concept of interface.py files in each workflow stage, showing how they can be used to create a facade pattern that abstracts the complexity of the underlying code. It also emphasizes the importance of:

-   **Simplified Controller Logic** - Controllers should be simple and focus on orchestrating the workflow
-   **Dynamic Configuration Loading** - Configuration should be loaded dynamically to allow for flexibility
-   **Event-Driven Architecture** - Using events to trigger workflows provides a more dynamic and flexible design

#### File 2025.03.26-dsk-005-c: Simplified Final Implementation Strategy

This file provides a simplified and streamlined approach to implementing the project, focusing on:

1. **Minimal Stage APIs** - Each stage should expose a clear public interface with minimal, clearly named functions
2. **Short, Explicit Controller Bridges** - Controllers should explicitly bridge two or more stages with intuitive naming
3. **Single, Clear Top-Level Orchestrator** - A single orchestrator should provide a high-level entry point for the full pipeline
4. **User-Friendly Entry Points** - Entry scripts should be minimal yet expressive
5. **Simple Testing Approach** - Tests should be structured in a straightforward way that mirrors the stages

This represents a final refinement of the project design, emphasizing simplicity, clarity, and maintainability.

### 2025.03.26-dsk-006-a to 2025.03.26-dsk-006-b: Combining the Best Approaches

These files refine the implementation strategy, combining the most valuable aspects of previous designs into a coherent, unified approach.

#### File 2025.03.26-dsk-006-a: Improved Dynamic Coding Strategy

This file reiterates the four core components of the project implementation:

1. Simplified Stage APIs through interface.py files
2. Controller Layer for explicit bridging between stages
3. Top-Level Orchestrator to provide high-level operations
4. User-Friendly Entry Points for simple user interaction

It provides concrete examples of how these components would be implemented, with an emphasis on minimal, clearly named functions and classes.

#### File 2025.03.26-dsk-006-b: "Best of Both Worlds" Architecture

This file expands on the implementation strategy with additional details:

-   **Unified Interfaces per Stage** - Making each stage's public API explicit and well-encapsulated
-   **Simple Controllers** - Focusing controllers on bridging specific stages with minimal complexity
-   **Centralized Configuration Loading** - Using a common utility for loading configuration to minimize duplication
-   **Clear Naming Conventions** - Using action-oriented or noun-oriented names for intuitive understanding

It also outlines an implementation plan that includes creating foundation structures, implementing interfaces, creating controllers, and setting up entry points.

### 2025.03.26-dsk-007-a to 2025.03.26-dsk-008-d: Initial Implementation and Workflow Possibilities

These files outline concrete examples of the project's implementation, including workflow possibilities and initial code structure.

#### File 2025.03.26-dsk-007-a: Consolidating the Project Structure

This file recaps the chosen project structure with its explicit file naming and organization, reinforcing the architectural decisions made in previous discussions.

#### File 2025.03.26-dsk-008-a and 2025.03.26-dsk-008-b: Concrete Workflow Possibilities

These files explain how the window manager creates new workflow possibilities through structured categorization and automation, including:

-   **Automatic Workspace Creation** - Automatically placing windows according to predefined layouts
-   **Context-Aware Layouts** - Adapting layouts based on the detected application categories
-   **Configuration-Driven Automation** - Using structured config files to automate window positioning

#### File 2025.03.26-dsk-008-c: Initial Code Implementation

This file presents a significant portion of the actual code implementation, including:

-   The main.py entry point with command-line argument handling
-   The window_detector.py module for detecting windows and their properties
-   The window_categorizer.py module for categorizing windows based on their characteristics
-   The window_controller.py module for coordinating between detection and categorization

This initial implementation focuses on the window detection and categorization stages, with placeholders for layout management.

#### File 2025.03.26-dsk-008-d: Implementation Plan

This file outlines a step-by-step implementation plan that combines the architectural design with the coding strategy:

1. Create the foundation structure with numbered directories
2. Implement clear stage interfaces through interface.py files
3. Create bridging controllers to orchestrate workflow stages
4. Implement the top-level orchestrator for high-level operations
5. Create user-friendly entry points
6. Set up a mirrored testing structure

It also identifies implementation priorities: core window detection, basic categorization, simple layout engine, monitor controller, CLI controller, and CLI entry point.
