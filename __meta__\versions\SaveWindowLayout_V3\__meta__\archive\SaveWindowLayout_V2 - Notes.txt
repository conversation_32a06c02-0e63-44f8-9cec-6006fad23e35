26.08.2023:
I'm going to create a window manager with Python (for use on Windows 10/11). I
already know python well, and I've created two different window managers
previously, however I've experienced that I often overcomplicate things when I
don't properly evaluate my exact needs before starting with the programming.

The context/reason why I'm going to create this, is that I've chosen to use a
single 42" screen instead of having multiple smaller monitors. The reason for
this is that with one large screen, it gives me a lot of flexibility. I'll provide some examples to:
- Example #1: I split the monitor into two halfs, with four windows in a grid on the
  left side, and a single window covering the entire right half of the screen.
  I set the resolution/zoom to 150% as this will make it easy to see the
  content of the four windows, while the right half already has enough space
  that it won't matter which resolution I choose.
- Example #2: I divide my screen into four columns and two rows, and set the
  resolution/zoom to 125%. This is for when I need to keep track of a lot of
  different stuff simultaneusly, e.g. navigating/browsing and moving/copying
  files and folders while also reading multiple textfiles from different
  locations. I often place all of the files folders to the left side of the
  monitor, and the textfiles/documents on the right side.
- Example #3: I first divide my screenspace into two halfs (left/right), then
  on the left half I slice it into three rows, and on the right side I slice
  it into three columns. 

There are many different ways and patterns I use, the examples above are just
meant as a point of reference. My point is that with a large monitor, it opens
up new oppurtunities for workflow improvements.


is I split my windows


half of the
  screen/monitor, and use a single window on the right half. This way I am
  able to


I can modify my 


 than multiple smaller screens.

 however I'd like to go back to the start and re-evaluate
my exact needs and wants before I starting doing any programming on the new
window manager. The reason for this is that I