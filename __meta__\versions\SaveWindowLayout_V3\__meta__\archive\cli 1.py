import argparse
import json
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from rich import box
from loguru import logger

from layout_management.apply_layout import apply_explorer_layout, apply_other_layout, apply_saved_layout
from layout_management.layout import save_layout
from window_management.common import close_duplicate_explorer_windows
from configure_logging import configure_logging

console = Console()

def parse_arguments():

    parser = argparse.ArgumentParser(description="Window and layout management utility.")
    parser.add_argument('--close-duplicate-windows', action='store_true', help="Close duplicate explorer windows")
    parser.add_argument('--save-layout', action='store_true', help="Save the current layout")
    parser.add_argument('--apply-saved-layout', action='store_true', help="Apply saved layout")
    parser.add_argument('--apply-explorer-layout', action='store_true', help="Apply explorer layout and close unmatched windows")
    parser.add_argument('--apply-other-layout', action='store_true', help="Apply other layout and close unmatched windows")
    parser.add_argument('--apply-explorer-layout-no-close', action='store_true', help="Apply explorer layout without closing unmatched windows")
    parser.add_argument('--prompt', action='store_true', help="Prompt the user for input values")
    args = parser.parse_args()


    if args.prompt:
        args.close_duplicate_windows = Prompt.ask("Close duplicate explorer windows? (y/n)", default="yes" if args.close_duplicate_windows else "no").lower() in ["yes", "y", "true", "1"]
        args.save_layout = Prompt.ask("Save the current layout? (y/n)", default="yes" if args.save_layout else "no").lower() in ["yes", "y", "true", "1"]
        args.apply_saved_layout = Prompt.ask("Apply saved layout? (y/n)", default="yes" if args.apply_saved_layout else "no").lower() in ["yes", "y", "true", "1"]
        args.apply_explorer_layout = Prompt.ask("Apply explorer layout and close unmatched windows? (y/n)", default="yes" if args.apply_explorer_layout else "no").lower() in ["yes", "y", "true", "1"]
        args.apply_other_layout = Prompt.ask("Apply other layout and close unmatched windows? (y/n)", default="yes" if args.apply_other_layout else "no").lower() in ["yes", "y", "true", "1"]
        args.apply_explorer_layout_no_close = Prompt.ask("Apply explorer layout without closing unmatched windows? (y/n)", default="yes" if args.apply_explorer_layout_no_close else "no").lower() in ["yes", "y", "true", "1"]
    return args


def display_summary(args):

    table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
    table.add_column("Parameter", style="dim", width=40)
    table.add_column("Value", style="bold cyan")
    table.add_row("Close duplicate explorer windows", "Yes" if args.close_duplicate_windows else "No")
    table.add_row("Save layout", "Yes" if args.save_layout else "No")
    table.add_row("Apply saved layout", "Yes" if args.apply_saved_layout else "No")
    table.add_row("Apply explorer layout", "Yes" if args.apply_explorer_layout else "No")
    table.add_row("Apply other layout", "Yes" if args.apply_other_layout else "No")
    table.add_row("Apply explorer layout (no close)", "Yes" if args.apply_explorer_layout_no_close else "No")
    console.print(table)

def handle_actions(args):

    if args.close_duplicate_windows:
        close_duplicate_windows_action()
    if args.save_layout:
        save_layout()
    if args.apply_saved_layout:
        apply_saved_layout()
    if args.apply_explorer_layout:
        apply_explorer_layout(close_unmatched=True)
    if args.apply_other_layout:
        apply_other_layout(close_unmatched=True)
    if args.apply_explorer_layout_no_close:
        apply_explorer_layout(close_unmatched=False)

def close_duplicate_windows_action():

    try:
        closed_windows = close_duplicate_explorer_windows()
        if closed_windows:
            logger.success("Successfully found and closed duplicate windows")
            console.print(json.dumps(closed_windows, indent=4))
        else:
            logger.info("No duplicate explorer windows found or closed")
    except Exception as e:
        logger.error(f"Failed to close duplicate explorer windows: {e}")
        console.print(f"\nError: {e}\n", style="bold red")
