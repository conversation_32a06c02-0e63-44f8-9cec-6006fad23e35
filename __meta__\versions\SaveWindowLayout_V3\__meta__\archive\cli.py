import inquirer3
from inquirer3.themes import load_theme_from_dict

# --- Themes ---
custom_theme_dict = {
    "Question": {
        "mark_color": "bold_orange",
        "brackets_color": "gray75",
        "default_color": "default",
    },
    "Checkbox": {
        "selection_color": "bold_orange",
        "selection_icon": ">",
        "selected_icon": "[X]",
        "selected_color": "bold_blue",
        "unselected_color": "gray60",
        "unselected_icon": "[ ]",
        "locked_option_color": "gray40",
    },
    "List": {
        "selection_color": "bold_blue",
        "selection_cursor": ">",
        "unselected_color": "gray60",
    }
}
custom_theme = load_theme_from_dict(custom_theme_dict)

# --- Menu Actions ---
def list_all_windows():
    print("Listing all windows...")

def list_explorer_windows():
    print("Listing explorer windows...")

def list_other_windows():
    print("Listing other windows...")

def close_all_duplicate_windows():
    print("Closing all duplicate windows...")

def close_duplicate_explorer_windows():
    print("Closing duplicate explorer windows...")

def close_duplicate_other_windows():
    print("Closing duplicate other windows...")

def export_all_windows():
    print("Exporting all open windows...")

def export_explorer_windows():
    print("Exporting explorer windows...")

def export_other_windows():
    print("Exporting other windows...")

def import_all_windows():
    print("Importing layout for all windows...")

def import_explorer_windows():
    print("Importing layout for explorer windows...")

def import_other_windows():
    print("Importing layout for other windows...")

def exit_program():
    print("Exiting the window manager.")
    exit()

# --- Menu Actions Dictionary ---
menu_actions = {
    "list": {
        "all": list_all_windows,
        "explorer": list_explorer_windows,
        "other": list_other_windows
    },
    "close_duplicates": {
        "all": close_all_duplicate_windows,
        "explorer": close_duplicate_explorer_windows,
        "other": close_duplicate_other_windows
    },
    "export": {
        "all": export_all_windows,
        "explorer": export_explorer_windows,
        "other": export_other_windows
    },
    "import": {
        "all": import_all_windows,
        "explorer": import_explorer_windows,
        "other": import_other_windows
    }
}

# --- Menu Functions ---
def ask_menu_question(menu_name, options):
    """Generic function to ask a menu question."""
    choices = [(f"{i+1}. {option}", option) for i, option in enumerate(options.keys())]
    questions = [
        inquirer3.List(
            'choice',
            message=f"{menu_name}: Choose an option",
            choices=choices,
            carousel=True
        )
    ]
    answers = inquirer3.prompt(questions, theme=custom_theme)
    selected_number = answers['choice'].split('.')[0].strip()
    print(selected_number)
    return options[list(options.keys())[int(selected_number) - 1]]

def execute_menu_choice(menu_name, options):
    """Executes the chosen action from a menu."""
    while True:
        try:
            choice_func = ask_menu_question(menu_name, options)
            choice_func()
            if menu_name != "Main Menu":  # Don't break for main menu
                break
        except (KeyboardInterrupt, EOFError):
            print(f"\nReturning to {menu_name}.")
            break

def main_menu():
    options = {
        "List Current Layout": lambda: execute_menu_choice("List Layout Menu", list_layout_menu()),
        "Close Duplicate Windows": lambda: execute_menu_choice("Close Duplicate Windows Menu", close_duplicate_windows_menu()),
        "Export Layout": lambda: execute_menu_choice("Export Layout Menu", export_layout_menu()),
        "Import Layout": lambda: execute_menu_choice("Import Layout Menu", import_layout_menu()),
        "Exit the window manager": exit_program
    }
    execute_menu_choice("Main Menu", options)

def list_layout_menu():
    return {
        "List All Windows...": list_all_windows,
        "List Explorer Windows...": list_explorer_windows,
        "List Other Windows...": list_other_windows
    }

def close_duplicate_windows_menu():
    return {
        "Close All Duplicate Windows...": close_all_duplicate_windows,
        "Close Duplicate Explorer Windows...": close_duplicate_explorer_windows,
        "Close Duplicate Other Windows...": close_duplicate_other_windows
    }

def export_layout_menu():
    return {
        "Export All Windows...": export_all_windows,
        "Export Explorer Windows...": export_explorer_windows,
        "Export Other Windows...": export_other_windows
    }

def import_layout_menu():
    return {
        "Import All Windows...": import_all_windows,
        "Import Explorer Windows...": import_explorer_windows,
        "Import Other Windows...": import_other_windows
    }

# --- Main Execution ---
if __name__ == "__main__":
    main_menu()
