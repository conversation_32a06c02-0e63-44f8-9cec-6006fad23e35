"""
v3_print_active_window_data.py - Object-oriented window monitoring utility

This script monitors active windows using a proper Window class hierarchy,
making it both structured and extensible.
"""

# ===== IMPORTS =====
import os
import sys
import time
import random
import ctypes
import traceback
import pywintypes
# Win32 imports
import win32con
import win32gui
import win32api
import win32process
# Terminal coloring
from ansimarkup import ansiprint, AnsiMarkup, parse


# ===== CONFIG =====
# Colors to avoid (too dark to read)
DARK_COLORS = [0] + list(range(16, 22)) + list(range(52, 55)) + \
              list(range(58, 64)) + list(range(232, 241))


# ===== WINDOW CLASS DEFINITIONS =====

class Window:
    """Base class representing a window with all common properties and methods"""

    def __init__(self, hwnd=None):
        """Initialize a Window object for the given hwnd or active window"""
        self.hwnd = hwnd or win32gui.GetForegroundWindow()

        # Window identification
        self.title = ""
        self.class_name = ""

        # Window state
        self.visibility = False
        self.controls_state = 0

        # Window position/size
        self.position = (0, 0)
        self.size = (0, 0)
        self.placement = None

        # Process information
        self.process_id = None
        self.process_path = None

        # Monitor information
        self.monitor = None

        # Load window data
        self.refresh()

    def refresh(self):
        """Update all window properties"""
        # Skip if invalid handle
        if not self.hwnd or not win32gui.IsWindow(self.hwnd):
            return False

        try:
            # Basic window info
            self.title = win32gui.GetWindowText(self.hwnd)
            self.class_name = win32gui.GetClassName(self.hwnd)
            self.visibility = win32gui.IsWindowVisible(self.hwnd)

            # Position and size
            self.placement = win32gui.GetWindowPlacement(self.hwnd)
            rect = win32gui.GetWindowRect(self.hwnd)
            self.position = (rect[0], rect[1])
            self.size = (rect[2] - rect[0], rect[3] - rect[1])
            self.controls_state = self.placement[1]

            # Process info
            thread_id, self.process_id = win32process.GetWindowThreadProcessId(self.hwnd)
            query_flags = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
            process_handle = ctypes.windll.kernel32.OpenProcess(query_flags, False, self.process_id)
            if process_handle:
                try:
                    self.process_path = win32process.GetModuleFileNameEx(process_handle, 0)
                except:
                    self.process_path = None

            # Monitor info
            monitor_handle = win32api.MonitorFromWindow(self.hwnd, win32con.MONITOR_DEFAULTTONEAREST)
            monitor_info = win32api.GetMonitorInfo(monitor_handle)
            self.monitor = monitor_info["Device"]

            return True
        except Exception as e:
            print(f"Error refreshing window data: {e}")
            return False

    def to_dict(self):
        """Convert window properties to a dictionary"""
        return {
            "hwnd": self.hwnd,
            "title": self.title,
            "class": self.class_name,
            "visibility": self.visibility,
            "controls_state": self.controls_state,
            "position": self.position,
            "size": self.size,
            "placement": self.placement,
            "process_id": self.process_id,
            "process_path": self.process_path,
            "monitor": self.monitor
        }

    def move(self, x, y):
        """Move window to specified position"""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            int(x), int(y),
            self.size[0], self.size[1],
            win32con.SWP_NOSIZE
        )
        self.refresh()

    def resize(self, width, height):
        """Resize window to specified dimensions"""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            self.position[0], self.position[1],
            int(width), int(height),
            win32con.SWP_NOMOVE
        )
        self.refresh()

    def focus(self):
        """Bring window to foreground"""
        win32gui.SetForegroundWindow(self.hwnd)

    def __eq__(self, other):
        """Compare window objects by their properties"""
        if not isinstance(other, Window):
            return False

        # We don't compare hwnd directly as we want to detect property changes
        for key in ["title", "position", "size", "visibility", "controls_state", "process_id"]:
            if getattr(self, key) != getattr(other, key):
                return False
        return True

    def __str__(self):
        """String representation of window"""
        return f"Window(title='{self.title}', hwnd={self.hwnd})"


class ExplorerWindow(Window):
    """Specialized class for Windows Explorer windows"""

    def __init__(self, hwnd=None):
        """Initialize an Explorer window"""
        super().__init__(hwnd)

        # Explorer-specific properties
        self.location_path = None
        self.view_mode = None
        self.icon_size = None
        self.selected_items = []

        # Load explorer-specific data
        self.refresh_explorer_data()

    def refresh(self):
        """Update all window properties including explorer-specific ones"""
        result = super().refresh()
        if result and self.is_explorer():
            self.refresh_explorer_data()
        return result

    def is_explorer(self):
        """Check if this is actually an Explorer window"""
        return self.class_name == "CabinetWClass"

    def refresh_explorer_data(self):
        """Update Explorer-specific data"""
        # This is a placeholder - in a full implementation, you would
        # use shell APIs to get Explorer-specific information
        # Such as view mode, selected items, etc.

        # For now, we just mark it as an Explorer window
        self.location_path = "Unknown"  # Would be populated from shell APIs
        self.view_mode = "Unknown"      # Would be view mode (details, icons, etc)
        self.icon_size = 0              # Would be icon size
        self.selected_items = []        # Would be selected files/folders

    def to_dict(self):
        """Convert window properties to a dictionary including Explorer data"""
        data = super().to_dict()
        if self.is_explorer():
            data.update({
                "is_explorer": True,
                "location_path": self.location_path,
                "view_mode": self.view_mode,
                "icon_size": self.icon_size,
                "selected_items": self.selected_items
            })
        return data


# ===== WINDOW FACTORY =====

def create_window(hwnd=None):
    """Create the appropriate Window object based on window class"""
    if hwnd is None:
        hwnd = win32gui.GetForegroundWindow()

    try:
        class_name = win32gui.GetClassName(hwnd)

        # Create specialized window types based on class
        if class_name == "CabinetWClass":  # Explorer window
            return ExplorerWindow(hwnd)

        # Default to base Window class
        return Window(hwnd)
    except:
        # Fallback to base Window
        return Window(hwnd)


# ===== DISPLAY FORMATTING =====

def format_window_data(window):
    """Format window data for terminal display"""
    data = window.to_dict()
    output_lines = []

    # Create separator line
    separator = "-" * 100
    output_lines.append(separator)

    # Update terminal title if we have the necessary data
    if all(k in data for k in ["monitor", "size", "position"]):
        os.system(f'TITLE "{data["monitor"]} | Size:{data["size"]} | Pos:{data["position"]}"')

    # Format each key-value pair with right-aligned keys
    # Skip hwnd and monitor to save space
    skip_keys = ["hwnd", "monitor"]
    display_items = [(k, v) for k, v in sorted(data.items()) if k not in skip_keys]

    if display_items:
        max_key_length = max(len(key) for key, _ in display_items)
        for key, value in display_items:
            output_lines.append(f"{key.rjust(max_key_length + 5)} : {value}")

    return "\n".join(output_lines)


def colored_output(text):
    """Apply a random color to output text"""
    # Enable ANSI color support in terminal
    os.system('color')

    # Choose a random color (avoiding dark colors)
    color_int = random.randint(15, 255)
    while color_int in DARK_COLORS:
        color_int = random.randint(15, 255)

    # Create colored output function
    return lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")


# ===== WINDOW MONITOR =====

class WindowMonitor:
    """Monitors active window changes and triggers callbacks"""

    def __init__(self, callback=None, interval=0.15):
        """Initialize the window monitor"""
        self.callback = callback or self.default_callback
        self.interval = interval
        self.previous_window = None
        self.running = False

    def start(self):
        """Start monitoring window changes"""
        self.running = True

        try:
            while self.running:
                # Create window object for current foreground window
                current_window = create_window()

                # If different from previous window, call the callback
                if self.previous_window is None or current_window != self.previous_window:
                    self.callback(current_window, self.previous_window)
                    self.previous_window = current_window

                # Wait before checking again
                time.sleep(self.interval)
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
            self.running = False

    def stop(self):
        """Stop monitoring"""
        self.running = False

    def default_callback(self, window, previous_window):
        """Default callback that prints window data"""
        formatted = format_window_data(window)
        color_print = colored_output(formatted)
        color_print(formatted)


# ===== EXTENSION POINT =====
# Add your custom window types here by extending the Window class
# For example:

# class BrowserWindow(Window):
#     """Custom window type for web browsers"""
#     def __init__(self, hwnd=None):
#         super().__init__(hwnd)
#         self.current_url = None
#
#     def refresh(self):
#         super().refresh()
#         # Add code to get browser-specific data
#
#     def to_dict(self):
#         data = super().to_dict()
#         data["current_url"] = self.current_url
#         return data


# ===== MAIN PROGRAM =====

def main():
    """Main program entry point"""
    # Set up initial terminal window size
    cmd_hwnd = win32gui.GetForegroundWindow()
    win32gui.MoveWindow(cmd_hwnd, 160, 120, 1600, 720, True)

    # Create and start the window monitor
    print("Monitoring active windows. Press Ctrl+C to stop.")
    monitor = WindowMonitor()
    monitor.start()


if __name__ == "__main__":
    main()
