:: ============================================================================
:: Batch-Script Initialization
:: ============================================================================
@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "Bat_InitialDir=%CD%"
SET "Bat_FilePath=%~dp0%~nx0" :: example: 'D:\Projects\Directory\script_name.bat'
SET "Bat_DirPath=%~dp0"       :: example: 'D:\Projects\Directory'
SET "Bat_FileName=%~nx0"      :: example: 'script_name.bat'
SET "Bat_BaseName=%~n0"       :: example: 'script_name'
SET "Bat_Extension=%~x0"      :: example: 'bat'
:: ============================================================================
:: Retrieve Project and VENV-Paths
:: ============================================================================
SET "venvFile_Identifier=.PRJ\.VENV"
:Search_Project_Venv
IF EXIST "%CD%\%venvFile_Identifier%" (
    SET /P venvFileData=<"%CD%\%venvFile_Identifier%"
    CALL SET "venvPath=!venvFileData!"
    IF EXIST "!venvPath!\Scripts\python.exe" (GOTO Found_Project_Venv)
) ELSE (
    SET "TMP=%CD%" & CD.. & IF NOT "!CD!"=="!TMP!" GOTO Search_Project_Venv
    EXIT /B 1
)
:Found_Project_Venv
SET "Project_Main_Dir=%CD%"
SET "Project_Venv_Dir=!venvPath!"
SET "Project_Venv_Activate=!venvPath!\Scripts\activate"
SET "Project_Venv_Python=!venvPath!\Scripts\python.exe"
SET "Project_Venv_Pip=!venvPath!\Scripts\pip.exe"
SET "Venv_Requirements=%Project_Main_Dir%\requirements.txt"
CD /D "%Bat_InitialDir%"
:: ============================================================================
:: Extract the directory name of the project
:: ============================================================================
SET "Project_Dir_Name="
FOR %%I IN ("%Project_Main_Dir%") DO (SET "Project_Dir_Name=%%~nI")
:: ============================================================================


:: ----------------------------------------------------------------------------
:: Action -> Set it to execute the python-script identical in file-basename
:: ----------------------------------------------------------------------------
SET "Py_DirPath=%Bat_InitialDir%"
SET "Py_FileName=%Bat_BaseName%.py"
SET "Py_FilePath=%Py_DirPath%\%Py_FileName%"
SET "Py_InputArgs="

:: ----------------------------------------------------------------------------
:: Display information
:: ----------------------------------------------------------------------------
TITLE Execute: "%Bat_FileName%"    -^>    "%Py_FileName%"    "%Py_InputArgs%"
ECHO --------------------------------------------------------------------------
ECHO Information: Current Script:
ECHO   Directory   -^> "%Bat_InitialDir%"
ECHO   Batch File  -^> "%Bat_FileName%"
ECHO   Python File -^> "%Py_FileName%"
ECHO   Python Args -^> "%Py_InputArgs%"
ECHO --------------------------------------------------------------------------
:: ----------------------------------------------------------------------------
:: EXECUTE SCRIPT
:: ----------------------------------------------------------------------------
ECHO Executing script ...
ECHO.
"%Project_Venv_Python%" "%Py_FilePath%" "%Py_InputArgs%"
ECHO.
ECHO Execution complete ...
PAUSE > NUL