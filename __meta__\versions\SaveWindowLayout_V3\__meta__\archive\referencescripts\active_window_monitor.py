"""
active_window_monitor.py - Real-time active window monitoring

This script monitors the active window in real-time, displays detailed
information, and can optionally log changes to a file. It uses the
window_types.py module for structured window data.
"""

import os
import sys
import time
import json
import argparse
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime
import threading

# Setup loguru
from loguru import logger

# Local imports
from window_types import (
    WindowType, WindowState, ExplorerViewMode,
    BaseWindowData, ExplorerWindowData, BrowserWindowData,
    WindowCollection, WindowDataFactory
)
from window_manager import WindowManager, display_window_info

# Configure loguru
logger.remove()  # Remove default handler

# Add custom filter to avoid duplicate error messages
class DuplicateFilter:
    """Filter to suppress repeated log messages"""
    def __init__(self, logger=None):
        self.last_log = {}

    def __call__(self, record):
        # Get the message content
        message = record["message"]
        level = record["level"].name
        key = f"{level}:{message}"

        # If it's an error message, check if it's a duplicate
        if level in ("ERROR", "WARNING"):
            # First time seeing this message
            if key not in self.last_log:
                self.last_log[key] = 1
                return True
            # Repeated message, increment counter but don't log
            self.last_log[key] += 1
            # Log every 10th occurrence to show it's still happening
            if self.last_log[key] % 10 == 0:
                record["message"] = f"{message} (repeated {self.last_log[key]} times)"
                return True
            return False
        return True

# Add handler with filter
logger.add(sys.stderr, level="INFO", filter=DuplicateFilter())

# Optional rich package for enhanced output
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.layout import Layout
    from rich import box
    HAS_RICH = True
    console = Console()
except ImportError:
    HAS_RICH = False

# Optional colorama for basic color output if rich is not available
if not HAS_RICH:
    try:
        from colorama import init, Fore, Back, Style
        init()
        HAS_COLORAMA = True
    except ImportError:
        HAS_COLORAMA = False


class FieldDiff:
    """Class to track changes in window properties"""

    def __init__(self, field_name: str, old_value: Any, new_value: Any):
        """Initialize a field difference

        Args:
            field_name: Name of the field that changed
            old_value: Previous value
            new_value: New value
        """
        self.field_name = field_name
        self.old_value = old_value
        self.new_value = new_value

    def __str__(self) -> str:
        """String representation of the difference"""
        return f"{self.field_name}: {self.old_value} -> {self.new_value}"


class WindowMonitor:
    """Class for monitoring window changes"""

    def __init__(self):
        """Initialize the window monitor"""
        self.manager = WindowManager()
        self.last_window: Optional[BaseWindowData] = None
        self.refresh_interval = 0.25  # seconds
        self.running = False
        self.thread = None
        self.changes = []
        self.max_changes = 100  # Maximum number of changes to keep
        self.tracked_fields = {
            # Common fields to track for all window types
            'all': [
                'title', 'window_type', 'state', 'position', 'size',
                'visible', 'always_on_top', 'z_order'
            ],
            # Explorer-specific fields
            WindowType.EXPLORER: [
                'location_path', 'view_mode', 'selected_files', 'focused_file'
            ],
            # Browser-specific fields
            WindowType.BROWSER: [
                'url', 'browser_type', 'tab_count'
            ]
        }

    def start(self, callback=None):
        """Start monitoring window changes

        Args:
            callback: Optional callback function to call when window changes
        """
        if self.running:
            return

        self.running = True

        # Use the provided callback or the default
        self.callback = callback or self._default_callback

        # Create and start the monitor thread
        self.thread = threading.Thread(target=self._monitor_loop)
        self.thread.daemon = True
        self.thread.start()

        logger.info("Window monitoring started")

    def stop(self):
        """Stop monitoring window changes"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1.0)
        logger.info("Window monitoring stopped")

    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Refresh window information
                self.manager.refresh_windows()

                # Get active window
                active_window = self.manager.get_active_window()

                # Check if window changed or properties changed
                if active_window:
                    window_changed = False

                    # Check if it's a new window
                    if not self.last_window or active_window.hwnd != self.last_window.hwnd:
                        window_changed = True

                    # Check if properties changed
                    elif self.last_window:
                        differences = self._find_differences(self.last_window, active_window)
                        if differences:
                            window_changed = True
                            self.changes.extend(differences)
                            # Trim changes list if needed
                            if len(self.changes) > self.max_changes:
                                self.changes = self.changes[-self.max_changes:]

                    # Call the callback if window changed
                    if window_changed:
                        self.callback(active_window, self.last_window)
                        self.last_window = active_window

                # Wait before next check
                time.sleep(self.refresh_interval)

            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")

    def _default_callback(self, window: BaseWindowData, previous_window: Optional[BaseWindowData]):
        """Default callback when window changes

        Args:
            window: Current window data
            previous_window: Previous window data or None
        """
        if HAS_RICH:
            self._display_window_rich(window, previous_window)
        else:
            # Fall back to simple display
            display_window_info(window)

    def _find_differences(self, old_window: BaseWindowData,
                          new_window: BaseWindowData) -> List[FieldDiff]:
        """Find differences between old and new window data

        Args:
            old_window: Previous window data
            new_window: Current window data

        Returns:
            List of field differences
        """
        differences = []

        # Skip if windows have different handles
        if old_window.hwnd != new_window.hwnd:
            return differences

        # Check common fields
        for field in self.tracked_fields['all']:
            old_value = getattr(old_window, field)
            new_value = getattr(new_window, field)

            # Special handling for certain types
            if isinstance(old_value, (list, dict, set)):
                if str(old_value) != str(new_value):
                    differences.append(FieldDiff(field, old_value, new_value))
            elif old_value != new_value:
                differences.append(FieldDiff(field, old_value, new_value))

        # Check type-specific fields
        if (new_window.window_type == WindowType.EXPLORER and
            isinstance(old_window, ExplorerWindowData) and
            isinstance(new_window, ExplorerWindowData)):

            for field in self.tracked_fields[WindowType.EXPLORER]:
                old_value = getattr(old_window, field)
                new_value = getattr(new_window, field)

                if isinstance(old_value, (list, dict, set)):
                    if str(old_value) != str(new_value):
                        differences.append(FieldDiff(field, old_value, new_value))
                elif old_value != new_value:
                    differences.append(FieldDiff(field, old_value, new_value))

        elif (new_window.window_type == WindowType.BROWSER and
              isinstance(old_window, BrowserWindowData) and
              isinstance(new_window, BrowserWindowData)):

            for field in self.tracked_fields[WindowType.BROWSER]:
                old_value = getattr(old_window, field)
                new_value = getattr(new_window, field)

                if isinstance(old_value, (list, dict, set)):
                    if str(old_value) != str(new_value):
                        differences.append(FieldDiff(field, old_value, new_value))
                elif old_value != new_value:
                    differences.append(FieldDiff(field, old_value, new_value))

        return differences

    def _display_window_rich(self, window: BaseWindowData,
                            previous_window: Optional[BaseWindowData]):
        """Display window information using rich formatting

        Args:
            window: Current window data
            previous_window: Previous window data or None
        """
        # Clear the screen
        console.clear()

        # Create the layout
        layout = Layout()
        layout.split(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )

        # Header
        header = Panel(
            f"[bold blue]Window Monitor[/bold blue] - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            style="bold white on blue"
        )

        # Main content - window information
        table = Table(title=f"Active Window: {window.title}", box=box.ROUNDED)

        # Add columns
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")

        # Add basic properties
        table.add_row("HWND", str(window.hwnd))
        table.add_row("Title", window.title)
        table.add_row("Type", window.window_type.name)
        table.add_row("Class", window.class_name)
        table.add_row("State", window.state.name)
        table.add_row("Position", str(window.position))
        table.add_row("Size", str(window.size))

        if window.process.path:
            table.add_row("Process", window.process.path)

        if window.monitor:
            table.add_row("Monitor", f"{window.monitor.device} ({'Primary' if window.monitor.is_primary else 'Secondary'})")

        # Add type-specific properties
        if window.window_type == WindowType.EXPLORER and isinstance(window, ExplorerWindowData):
            table.add_row("Location", window.location_path)
            table.add_row("View Mode", window.view_mode.name)

            if window.selected_files:
                table.add_row("Selected Files", ", ".join(window.selected_files))

            if window.focused_file:
                table.add_row("Focused File", window.focused_file)

        elif window.window_type == WindowType.BROWSER and isinstance(window, BrowserWindowData):
            table.add_row("URL", window.url or "Unknown")
            table.add_row("Browser Type", window.browser_type or "Unknown")

            if window.tab_count:
                table.add_row("Tab Count", str(window.tab_count))

        # Footer
        footer = Panel(
            "[bold]Press Ctrl+C to exit[/bold]",
            style="bold white on blue"
        )

        # Assign panels to layout
        layout["header"].update(header)
        layout["main"].update(table)
        layout["footer"].update(footer)

        # Print the layout
        console.print(layout)

        # Print changes if any
        if previous_window and window.hwnd == previous_window.hwnd and self.changes:
            changes_table = Table(title="Recent Changes", box=box.SIMPLE)
            changes_table.add_column("Field", style="yellow")
            changes_table.add_column("From", style="red")
            changes_table.add_column("To", style="green")

            for change in reversed(self.changes[-5:]):  # Show last 5 changes
                changes_table.add_row(
                    change.field_name,
                    str(change.old_value),
                    str(change.new_value)
                )

            console.print(changes_table)


def parse_arguments():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(description="Active Window Monitor")

    parser.add_argument("--interval", type=float, default=0.25,
                        help="Refresh interval in seconds (default: 0.25)")
    parser.add_argument("--log", action="store_true",
                        help="Log window changes to file")
    parser.add_argument("--log-file", type=str, default="window_monitor.log",
                        help="Log file path (default: window_monitor.log)")
    parser.add_argument("--debug", action="store_true",
                        help="Enable debug logging")

    return parser.parse_args()


def log_window_callback(window: BaseWindowData, previous_window: Optional[BaseWindowData]):
    """Callback that logs window changes to console and file

    Args:
        window: Current window data
        previous_window: Previous window data or None
    """
    # Display window information
    if HAS_RICH:
        # Use rich for display
        console.clear()
        table = Table(title=f"Active Window: {window.title}")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")

        # Add basic properties
        for key, value in window.to_dict().items():
            if isinstance(value, dict):
                table.add_row(key, str(value))
            else:
                table.add_row(key, str(value))

        console.print(table)
    else:
        # Use simple display
        display_window_info(window)

    # Log to file
    logger.info(f"Window changed: {window.title} ({window.hwnd})")
    if previous_window and window.hwnd != previous_window.hwnd:
        logger.info(f"Previous window: {previous_window.title} ({previous_window.hwnd})")


def main():
    """Main function"""
    args = parse_arguments()

    # Set up logging based on arguments
    logger.remove()  # Remove existing handlers

    # Configure console logging
    if args.debug:
        logger.add(sys.stderr, level="DEBUG", filter=DuplicateFilter())
    else:
        logger.add(sys.stderr, level="INFO", filter=DuplicateFilter())

    # Configure file logging if requested
    if args.log:
        logger.add(
            args.log_file,
            level="INFO",
            rotation="1 MB",   # Rotate after 1MB
            compression="zip", # Compress old logs
            filter=DuplicateFilter()
        )

    # Create and configure monitor
    monitor = WindowMonitor()
    monitor.refresh_interval = args.interval

    if args.log:
        # Use logging callback
        monitor.start(callback=log_window_callback)
    else:
        # Use default callback
        monitor.start()

    try:
        # Set up terminal for better display
        if os.name == 'nt':  # Windows
            try:
                os.system('mode con: cols=120 lines=40')
                os.system('color 0F')  # Black background, white text
            except:
                pass

        print("Monitoring active windows. Press Ctrl+C to exit.")

        # Keep main thread alive
        while True:
            time.sleep(0.5)

    except KeyboardInterrupt:
        print("\nMonitoring stopped.")
    finally:
        monitor.stop()


if __name__ == "__main__":
    main()
