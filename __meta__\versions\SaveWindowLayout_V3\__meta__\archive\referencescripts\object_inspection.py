"""
object_inspection.py - Utilities for inspecting window and COM objects

This module provides functions for inspecting and exploring COM objects
and their properties, which is useful for debugging and discovering
the structure and capabilities of Windows Shell objects.
"""

# Standard imports
import os
import sys
import traceback
import pprint

# Win32 API imports
import win32com.client
import win32gui

# Optional dependencies for enhanced printing
try:
    from ppretty import ppretty
    HAS_PPRETTY = True
except ImportError:
    HAS_PPRETTY = False


def print_tree(obj, prefix="", visited=None):
    """Print a tree of an object's attributes, avoiding circular references

    Args:
        obj: The object to inspect
        prefix: String prefix for indentation
        visited: Set of visited object ids to avoid infinite recursion
    """
    if visited is None:
        visited = set()

    for item in dir(obj):
        # Skip private/dunder methods
        if item.startswith("__"):
            continue

        try:
            attr = getattr(obj, item)

            # Skip if already visited (avoid circular references)
            if id(attr) in visited:
                continue

            # Add to visited set
            visited.add(id(attr))

            # Print differently based on type
            if callable(attr):
                print(f"{prefix}|-- {item}()")
            elif not isinstance(attr, type):  # exclude classes to avoid deep recursion
                print(f"{prefix}|-- {item}")
                print_tree(attr, prefix + "    ", visited)
        except Exception as e:
            # Print error but continue
            print(f"{prefix}|-- {item} [Error: {str(e)}]")
            continue


def print_object_details(obj, max_depth=2, width=80):
    """Print detailed information about an object using ppretty if available

    Args:
        obj: The object to inspect
        max_depth: Maximum recursion depth
        width: Output width
    """
    if HAS_PPRETTY:
        result = ppretty(obj,
                         indent='  ',
                         depth=max_depth,
                         width=width,
                         seq_length=100,
                         show_protected=True,
                         show_private=False,
                         show_static=True,
                         show_properties=True,
                         show_address=False)
        print(result)
    else:
        # Fallback to standard pretty printing
        print("ppretty not available, using standard pprint:")
        pprint.pprint(dir(obj), width=width, indent=2)


def save_object_details(obj, filename, max_depth=5, width=100):
    """Save detailed object information to a file

    Args:
        obj: The object to inspect
        filename: Path where to save the output
        max_depth: Maximum recursion depth
        width: Output width
    """
    if HAS_PPRETTY:
        result = ppretty(obj,
                         indent='  ',
                         depth=max_depth,
                         width=width,
                         seq_length=5000,
                         show_protected=True,
                         show_private=False,
                         show_static=True,
                         show_properties=True,
                         show_address=False)

        with open(filename, 'w') as f:
            f.write(result)
        print(f"Object details saved to {filename}")
    else:
        print("ppretty not available, cannot save detailed object information")


def get_folder_view_constants():
    """Return mapping of view mode constants for Explorer windows

    Returns:
        Dictionary mapping view mode values to their names
    """
    return {
        -1: 'FVM_AUTO',
        1: 'FVM_ICON',
        2: 'FVM_SMALLICON',
        3: 'FVM_LIST',
        4: 'FVM_DETAILS',
        5: 'FVM_THUMBNAIL',
        6: 'FVM_TILE',
        7: 'FVM_THUMBSTRIP',
        8: 'FVM_CONTENT',
    }


def inspect_shell_application():
    """Inspect and print details about the Shell.Application object"""
    shell = win32com.client.Dispatch("Shell.Application")

    print("Shell.Application properties and methods:")
    print_tree(shell)

    # Get available windows
    windows = shell.Windows()
    print(f"\nFound {windows.Count} shell windows")

    # Look at first window if available
    if windows.Count > 0:
        window = windows.Item(0)
        print("\nFirst window properties:")
        print_tree(window, prefix="  ")

        if hasattr(window, 'Document'):
            print("\nDocument properties:")
            print_tree(window.Document, prefix="  ")


def inspect_active_window_shell():
    """Inspect the shell object for the active window if it's an Explorer window"""
    # Get active window
    hwnd = win32gui.GetForegroundWindow()
    class_name = win32gui.GetClassName(hwnd)
    title = win32gui.GetWindowText(hwnd)

    print(f"Active window: {title} (class: {class_name}, hwnd: {hwnd})")

    # If it's an Explorer window, inspect shell objects
    if class_name == 'CabinetWClass':
        shell = win32com.client.Dispatch("Shell.Application")
        windows = shell.Windows()

        # Find matching window
        for i in range(windows.Count):
            window = windows.Item(i)
            if window.HWND == hwnd:
                print("Found matching Shell window")
                print("\nShell window properties:")
                print_tree(window, prefix="  ")

                # Save details to file if ppretty is available
                if HAS_PPRETTY:
                    save_object_details(window, "shell_window_details.txt")

                return

        print("No matching Shell window found")
    else:
        print("Not an Explorer window")


# Example usage
if __name__ == "__main__":
    print("Object Inspection Utilities")
    print("==========================")
    print("1. Inspect Shell.Application")
    print("2. Inspect active window shell object")
    print("3. Save active window details to file")

    choice = input("Enter your choice (1-3): ")

    if choice == '1':
        inspect_shell_application()
    elif choice == '2':
        inspect_active_window_shell()
    elif choice == '3':
        hwnd = win32gui.GetForegroundWindow()
        title = win32gui.GetWindowText(hwnd)
        filename = f"window_{hwnd}_{title[:20]}.txt"

        # Replace characters that aren't allowed in filenames
        for char in '\\/:*?"<>|':
            filename = filename.replace(char, '_')

        inspect_active_window_shell()
        print(f"Details saved to {filename}")
    else:
        print("Invalid choice")