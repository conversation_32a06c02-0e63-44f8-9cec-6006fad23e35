@echo off
setlocal

:: <PERSON><PERSON><PERSON> to run the advanced window monitor with virtual environment activated

:: Determine script locations
set SCRIPT_DIR=%~dp0
set VENV_DIR=%SCRIPT_DIR%venv
set MONITOR_SCRIPT=%SCRIPT_DIR%active_window_monitor.py

:: Check if virtual environment exists
if not exist "%VENV_DIR%" (
    echo Virtual environment not found. Creating...
    call "%SCRIPT_DIR%utils\py_venv_init.bat"
)

:: Check for required and optional packages
echo Checking for dependencies...

:: Check for loguru (required)
"%VENV_DIR%\Scripts\python" -c "import loguru" >nul 2>&1
if errorlevel 1 (
    echo Installing loguru for logging...
    "%VENV_DIR%\Scripts\pip" install loguru
)

:: Check for Rich package (optional)
"%VENV_DIR%\Scripts\python" -c "import rich" >nul 2>&1
if errorlevel 1 (
    echo Installing Rich for enhanced display...
    "%VENV_DIR%\Scripts\pip" install rich
)

:: Check for Colorama (optional fallback if rich is not available)
"%VENV_DIR%\Scripts\python" -c "import colorama" >nul 2>&1
if errorlevel 1 (
    echo Installing Colorama for basic color output...
    "%VENV_DIR%\Scripts\pip" install colorama
)

:: Run the window monitor
echo Starting advanced window monitor... (Press Ctrl+C to exit)
echo.

:: Get command line arguments
set args=%*

:: Pass all arguments to the script
"%VENV_DIR%\Scripts\python" "%MONITOR_SCRIPT%" %args%

endlocal