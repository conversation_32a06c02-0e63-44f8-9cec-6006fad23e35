{"name": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Utils_Todo\\Py_WindowTiler\\SaveWindowLayout_V3\\referencescripts\\../layouts", "creation_time": "2025-03-26T17:21:52.726205", "save_time": "2025-03-26T17:21:52.730607", "monitors": [{"device": "\\\\.\\DISPLAY1", "work_area": [0, 0, 3840, 2112], "monitor_area": [0, 0, 3840, 2160], "is_primary": true}], "windows": [{"hwnd": 9443416, "title": "Perplexity - Google Chrome", "class": "Chrome_WidgetWin_1", "visibility": 1, "controls_state": 1, "position": [2132, 132], "size": [1701, 1980], "placement": [0, 1, [-32000, -32000], [-1, -1], [2132, 132, 3833, 2112]], "process_id": 15548, "process_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 16975968, "title": "SaveWindowLayout_02_Wip_6.py - SaveWindowLayout_V3 - <PERSON>ursor", "class": "Chrome_WidgetWin_1", "visibility": 1, "controls_state": 1, "position": [16, 55], "size": [1408, 1977], "placement": [0, 1, [-1, -1], [-1, -1], [16, 55, 1424, 2032]], "process_id": 27868, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_cursor\\exe\\Cursor.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 22809400, "title": "window-dep - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [2043, 736], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [2043, 736, 3592, 1587]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 3416238, "title": "SaveWindowLayout_V3 - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [1939, 632], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [1939, 632, 3488, 1483]], "process_id": 2824, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 4065456, "title": "Sourcetree", "class": "HwndWrapper[SourceTree.exe;;1349a858-99e1-4abc-8f2c-ba71076549a2]", "visibility": 1, "controls_state": 1, "position": [0, 1239], "size": [3840, 872], "placement": [0, 1, [-1, -1], [-1, -1], [0, 1239, 3840, 2111]], "process_id": 17992, "process_path": "C:\\Users\\<USER>\\AppData\\Local\\SourceTree\\app-3.4.19\\SourceTree.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 6821358, "title": "C:\\Windows\\system32\\cmd.exe", "class": "CASCADIA_HOSTING_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [70, 78], "size": [2631, 1509], "placement": [0, 1, [-1, -1], [-1, -1], [70, 78, 2701, 1587]], "process_id": 17532, "process_path": "C:\\Program Files\\WindowsApps\\Microsoft.WindowsTerminal_1.21.10351.0_x64__8wekyb3d8bbwe\\WindowsTerminal.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 6426254, "title": "SaveWindowLayout_V3 - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Utils_Todo\\Py_WindowTiler\\SaveWindowLayout_V3\\referencescripts\\window_explorer.py - Sublime Text", "class": "PX_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [2146, 55], "size": [1701, 1980], "placement": [0, 1, [-1, -1], [-1, -1], [2146, 55, 3847, 2035]], "process_id": 1904, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_sublimetext\\exe\\sublime_text.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 9177752, "title": "referencescripts - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [1991, 684], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [1991, 684, 3540, 1535]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 16584652, "title": "Python - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__SpeechToText\\src\\batch_transcription_nor.yaml - Sublime Text", "class": "PX_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [2296, 0], "size": [1552, 852], "placement": [0, 1, [-1, -1], [-1, -1], [2296, 0, 3848, 852]], "process_id": 1904, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_sublimetext\\exe\\sublime_text.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 14094252, "title": "py: content:\"win32g\" -  ├─ Defaults: DSK - C: - Everything (1.5a) 1.5.0.1383a (x64)", "class": "EVERYTHING_(1.5a)", "visibility": 1, "controls_state": 3, "position": [-8, -8], "size": [3856, 2128], "placement": [2, 3, [-1, -1], [-1, -1], [-7, 1239, 3847, 2118]], "process_id": 12016, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_everything\\exe\\Everything64.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 24908006, "title": "SaveWindowLayout_V3 - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [249, 583], "size": [776, 851], "placement": [0, 1, [-1, -1], [-1, -1], [249, 583, 1025, 1434]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 5440382, "title": "history - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [1965, 658], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [1965, 658, 3514, 1509]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 5642296, "title": "transcribe - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [379, 713], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [379, 713, 1928, 1564]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 11273780, "title": "app_mpvplayer - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_mpvplayer\\user\\observations.md - Sublime Text", "class": "PX_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [2146, 55], "size": [1701, 1980], "placement": [0, 1, [-1, -1], [-1, -1], [2146, 55, 3847, 2035]], "process_id": 1904, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_sublimetext\\exe\\sublime_text.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 15604096, "title": "app_mpvplayer - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [1939, 632], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [1939, 632, 3488, 1483]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 5444460, "title": "src - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [1913, 606], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [1913, 606, 3462, 1457]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 13962126, "title": "(9) Bambulab 3D Printer - YouTube — Firefox Developer Edition", "class": "MozillaWindowClass", "visibility": 1, "controls_state": 1, "position": [617, 55], "size": [1535, 1996], "placement": [0, 1, [-1, -1], [-1, -1], [617, 55, 2152, 2051]], "process_id": 25744, "process_path": "C:\\Program Files\\Firefox Developer Edition\\firefox.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 9244118, "title": "D:\\NOT_BACKED_UP\\transcribe\\batch_transcription_en.yaml • - Sublime Text", "class": "PX_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [2297, 0], "size": [1550, 851], "placement": [0, 1, [-1, -1], [-1, -1], [2297, 0, 3847, 851]], "process_id": 1904, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_sublimetext\\exe\\sublime_text.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 10031428, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__SpeechToText\\venv\\Scripts\\python.exe", "class": "CASCADIA_HOSTING_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [1807, 577], "size": [1422, 958], "placement": [0, 1, [-1, -1], [-1, -1], [1807, 577, 3229, 1535]], "process_id": 17532, "process_path": "C:\\Program Files\\WindowsApps\\Microsoft.WindowsTerminal_1.21.10351.0_x64__8wekyb3d8bbwe\\WindowsTerminal.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 2037750, "title": "py__YoutubePlaylistExporter - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [431, 765], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [431, 765, 1980, 1616]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 7148246, "title": "c_template_runner_sysinstr_d_interactive_optimizer - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [353, 687], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [353, 687, 1902, 1538]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 9970174, "title": "seg0 - File Explorer", "class": "CabinetWClass", "visibility": 1, "controls_state": 1, "position": [301, 635], "size": [1549, 851], "placement": [0, 1, [-1, -1], [-1, -1], [301, 635, 1850, 1486]], "process_id": 2880, "process_path": "C:\\Windows\\explorer.exe", "monitor": "\\\\.\\DISPLAY1", "type": "NORMAL_FOLDER"}, {"hwnd": 15405304, "title": "<!ext:url;lnk sort:dm> empty: sfda: - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python - Everything (1.5a) 1.5.0.1383a (x64)", "class": "EVERYTHING_(1.5a)", "visibility": 1, "controls_state": 1, "position": [0, 1233], "size": [3840, 879], "placement": [0, 1, [-32000, -32000], [-1, -1], [0, 1233, 3840, 2112]], "process_id": 12016, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_everything\\exe\\Everything64.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 2172240, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__ProjectGenerator\\src\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py - Sublime Text", "class": "PX_WINDOW_CLASS", "visibility": 1, "controls_state": 1, "position": [2297, 0], "size": [1550, 851], "placement": [0, 1, [-1, -1], [-1, -1], [2297, 0, 3847, 851]], "process_id": 1904, "process_path": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_sublimetext\\exe\\sublime_text.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 7474124, "title": "Microsoft Visual C++ Runtime Library", "class": "#32770", "visibility": 1, "controls_state": 1, "position": [1715, 939], "size": [427, 296], "placement": [0, 1, [-1, -1], [-1, -1], [1715, 939, 2142, 1235]], "process_id": 12760, "process_path": "C:\\Program Files\\Adobe\\Adobe Creative Cloud Experience\\libs\\node.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}, {"hwnd": 7086826, "title": "Settings", "class": "ApplicationFrameWindow", "visibility": 1, "controls_state": 1, "position": [2292, 0], "size": [769, 1780], "placement": [0, 1, [-1, -1], [-1, -1], [2292, 0, 3061, 1780]], "process_id": 13528, "process_path": "C:\\Windows\\System32\\ApplicationFrameHost.exe", "monitor": "\\\\.\\DISPLAY1", "type": "UNSPECIFIED"}]}