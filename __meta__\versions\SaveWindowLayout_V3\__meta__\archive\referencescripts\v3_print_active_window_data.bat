@echo off
setlocal enabledelayedexpansion

:: Set the title
title "Window Information Monitor"

:: Get the directory of this batch file
set "SCRIPT_DIR=%~dp0"
set "SCRIPT_DIR=!SCRIPT_DIR:~0,-1!"
set "ROOT_DIR=!SCRIPT_DIR!\..\"

:: Ensure virtual environment exists and is activated
if not exist "!ROOT_DIR!\venv\" (
    echo Virtual environment not found. Please run utils\py_venv_init.bat first.
    pause
    exit /b 1
)

:: Activate the virtual environment
call "!ROOT_DIR!\venv\Scripts\activate.bat"

:: Run the Python script
python "!SCRIPT_DIR!\v3_print_active_window_data.py"

:: Deactivate the virtual environment
call deactivate

:: Pause to see any errors if not launched from terminal
pause