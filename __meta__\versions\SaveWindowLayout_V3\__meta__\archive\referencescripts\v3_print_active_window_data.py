"""
Window Data Collection Utility

This script provides functionality to collect and display information about active windows.
It defines a WindowInfo class to represent window data and continuously monitors window changes.
"""

# Import modules
import os
import sys
import time
import random
import ctypes
import urllib.parse
# Error handling
import traceback
import pywintypes
# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process
# Module for coloring the prompt
from ansimarkup import ansiprint


class WindowInfo:
    """Class representing a window with all its relevant information."""

    def __init__(self, hwnd=None):
        """Initialize with a window handle or the foreground window if None."""
        self.hwnd = hwnd if hwnd is not None else win32gui.GetForegroundWindow()
        self.refresh()

    def refresh(self):
        """Update all window information."""
        if not self.hwnd or not win32gui.IsWindow(self.hwnd):
            return False

        # Get the current monitor
        monitor_handle = win32api.MonitorFromWindow(self.hwnd, win32con.MONITOR_DEFAULTTONEAREST)
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        self.monitor = monitor_info["Device"]

        # Get window placement, position and size
        self.placement = win32gui.GetWindowPlacement(self.hwnd)
        rect = win32gui.GetWindowRect(self.hwnd)
        self.position = (rect[0], rect[1])
        self.size = (rect[2] - rect[0], rect[3] - rect[1])
        self.controls_state = self.placement[1]

        # Get general window properties
        self.visibility = win32gui.IsWindowVisible(self.hwnd)
        self.title = win32gui.GetWindowText(self.hwnd)
        self.class_name = win32gui.GetClassName(self.hwnd)

        # Get process information
        self.process_path = None
        self.process_id = None
        thread_id, process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        self.process_id = process_id

        # Try to get process path
        try:
            process_query = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
            process_handle = ctypes.windll.kernel32.OpenProcess(process_query, False, process_id)
            if process_handle:
                self.process_path = win32process.GetModuleFileNameEx(process_handle, 0)
                ctypes.windll.kernel32.CloseHandle(process_handle)
        except (pywintypes.error, AttributeError):
            pass

        return True

    def focus(self):
        """Bring window to front and focus it."""
        win32gui.BringWindowToTop(self.hwnd)
        win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)
        win32gui.SetForegroundWindow(self.hwnd)

    def hide(self):
        """Hide the window."""
        win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)

    def show(self):
        """Show the window."""
        win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)

    def move(self, x, y):
        """Move window to specified coordinates."""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            int(x), int(y),
            self.size[0], self.size[1],
            0
        )
        self.refresh()

    def resize(self, width, height):
        """Resize window to specified dimensions."""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            self.position[0], self.position[1],
            int(width), int(height),
            0
        )
        self.refresh()

    def minimize(self):
        """Minimize the window."""
        win32gui.ShowWindow(self.hwnd, win32con.SW_MINIMIZE)
        self.refresh()

    def maximize(self):
        """Maximize the window."""
        win32gui.ShowWindow(self.hwnd, win32con.SW_MAXIMIZE)
        self.refresh()

    def center(self, screen_width=None, screen_height=None):
        """Center window on screen."""
        if screen_width is None:
            screen_width = win32api.GetSystemMetrics(0)
        if screen_height is None:
            screen_height = win32api.GetSystemMetrics(1)

        x = screen_width // 2 - self.size[0] // 2
        y = screen_height // 2 - self.size[1] // 2
        self.move(x, y)

    def destroy(self):
        """Destroy/close the window."""
        win32gui.DestroyWindow(self.hwnd)

    def to_dict(self):
        """Return window information as a dictionary."""
        return {
            "monitor": self.monitor,
            "title": self.title,
            "class": self.class_name,
            "hwnd": self.hwnd,
            "visibility": self.visibility,
            "controls_state": self.controls_state,
            "position": self.position,
            "size": self.size,
            "placement": self.placement,
            "process_path": self.process_path,
            "process_id": self.process_id
        }


def print_window_info(window_info, cmd_hwnd=None):
    """Print formatted window information with color."""
    # Enable ANSI escapes and use a random color
    os.system('color')

    # Define colors to exclude (dark colors for better readability)
    dark_colors = [0] + list(range(16, 22)) + list(range(52, 55)) + \
                  list(range(58, 64)) + list(range(232, 241))

    # Select a random color that's not in the dark_colors list
    color_int = random.randint(15, 255)
    while color_int in dark_colors:
        color_int = random.randint(15, 255)

    # Define color print function
    color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")

    # Get terminal width for separator line
    if cmd_hwnd:
        cmd_rect = win32gui.GetWindowRect(cmd_hwnd)
        line_width = int((cmd_rect[2] - cmd_rect[0]) * 0.1)
    else:
        line_width = 100

    # Print separator
    color_print("-" * line_width)

    # Print window info dictionary with right-justified keys
    window_dict = window_info.to_dict()
    keys = [str(k) for k in window_dict.keys()]
    values = [str(v) for v in window_dict.values()]
    max_key_length = max(len(key) for key in keys)

    for key, value in zip(keys, values):
        color_print(key.rjust(max_key_length + 20) + " : " + value)


def main():
    """Main function to continuously monitor and display active window info."""
    # Resize terminal window for better visibility
    cmd_hwnd = win32gui.GetForegroundWindow()
    win32gui.MoveWindow(cmd_hwnd, 160, 120, 1600, 720, True)

    # Set window title
    os.system('TITLE "Window Information Monitor"')

    # Store the previous window data
    previous_info = None

    print("Monitoring active windows. Press Ctrl+C to exit...\n")

    try:
        while True:
            # Get current foreground window
            current_info = WindowInfo()

            # Check if window exists and has changed
            if current_info.hwnd and (
                previous_info is None or
                current_info.hwnd != previous_info.hwnd or
                current_info.position != previous_info.position or
                current_info.size != previous_info.size
            ):
                # Update terminal title with window info
                os.system(f'TITLE "{current_info.monitor}   |   Size:{current_info.size}   |   Pos:{current_info.position}"')

                # Print the window information
                print_window_info(current_info, cmd_hwnd)

                # Update previous info
                previous_info = current_info

            # Wait before checking again
            time.sleep(0.15)

    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


if __name__ == "__main__":
    main()