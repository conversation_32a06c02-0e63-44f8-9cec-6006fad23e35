"""
window_base.py - Core window management classes and functionality

This module provides the base Window class and essential functionality for
working with Windows window handles (hwnd), properties, and actions.
"""

# Standard imports
import os
import sys
import time
import ctypes
import urllib.parse
import traceback
from enum import Enum

# Import loguru for improved logging
from loguru import logger

# Configure logger
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add a stderr handler with INFO level

# Win32 API imports
import win32con
import win32gui
import win32api
import win32process
import pywintypes

# Window type enumeration
class WindowType(Enum):
    """
    Enumeration class to represent the different types of windows.
    - SPECIAL_FOLDER: explorer window with mapped path to CSIDL constant.
    - NORMAL_FOLDER: explorer window with a retrievable path.
    - UNSPECIFIED: window with a process other than explorer.
    - UNLINKED: window with title and class but no process.
    - UNKNOWN: any remaining windows not matched with a type.
    """
    SPECIAL_FOLDER = 1, 'SPECIAL_FOLDER'
    NORMAL_FOLDER = 2, 'NORMAL_FOLDER'
    UNSPECIFIED = 3, 'UNSPECIFIED'
    UNLINKED = 4, 'UNLINKED'
    UNKNOWN = 5, 'UNKNOWN'

class Window:
    """Base class representing a window with all common properties and methods"""

    def __init__(self, hwnd=None):
        """Initialize a Window object for the given hwnd or active window"""
        self.hwnd = hwnd or win32gui.GetForegroundWindow()

        # Window identification
        self.title = ""
        self.class_name = ""

        # Window state
        self.visibility = False
        self.controls_state = 0

        # Window position/size
        self.position = (0, 0)
        self.size = (0, 0)
        self.placement = None

        # Process information
        self.process_id = None
        self.process_path = None

        # Monitor information
        self.monitor = None

        # Window type
        self.type = None

        # Load window data
        self.refresh()

    def refresh(self):
        """Update all window properties"""
        # Skip if invalid handle
        if not self.hwnd or not win32gui.IsWindow(self.hwnd):
            return False

        try:
            # Basic window info
            self.title = win32gui.GetWindowText(self.hwnd)
            self.class_name = win32gui.GetClassName(self.hwnd)
            self.visibility = win32gui.IsWindowVisible(self.hwnd)

            # Position and size
            self.placement = win32gui.GetWindowPlacement(self.hwnd)
            rect = win32gui.GetWindowRect(self.hwnd)
            self.position = (rect[0], rect[1])
            self.size = (rect[2] - rect[0], rect[3] - rect[1])
            self.controls_state = self.placement[1]

            # Process info
            thread_id, self.process_id = win32process.GetWindowThreadProcessId(self.hwnd)
            query_flags = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
            process_handle = ctypes.windll.kernel32.OpenProcess(query_flags, False, self.process_id)
            if process_handle:
                try:
                    self.process_path = win32process.GetModuleFileNameEx(process_handle, 0)
                    ctypes.windll.kernel32.CloseHandle(process_handle)
                    self.type = WindowType.UNSPECIFIED
                except Exception as e:
                    logger.debug(f"Could not get process path for {self.hwnd}: {e}")
                    self.process_path = None
            else:
                self.type = WindowType.UNLINKED

            # Determine window type if it's a CabinetWClass (Explorer window)
            if self.class_name == 'CabinetWClass':
                self.type = WindowType.NORMAL_FOLDER

            # Monitor info
            try:
                monitor_handle = win32api.MonitorFromWindow(self.hwnd, win32con.MONITOR_DEFAULTTONEAREST)
                monitor_info = win32api.GetMonitorInfo(monitor_handle)
                self.monitor = monitor_info["Device"]
            except Exception as e:
                logger.debug(f"Could not get monitor info for {self.hwnd}: {e}")
                self.monitor = "Unknown"

            return True
        except Exception as e:
            logger.error(f"Error refreshing window data for {self.hwnd}: {e}")
            return False

    def to_dict(self):
        """Convert window properties to a dictionary"""
        return {
            "hwnd": self.hwnd,
            "title": self.title,
            "class": self.class_name,
            "visibility": self.visibility,
            "controls_state": self.controls_state,
            "position": self.position,
            "size": self.size,
            "placement": self.placement,
            "process_id": self.process_id,
            "process_path": self.process_path,
            "monitor": self.monitor,
            "type": self.type.name if self.type else "UNKNOWN"
        }

    def move(self, x, y):
        """Move window to specified position"""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            int(x), int(y),
            self.size[0], self.size[1],
            win32con.SWP_NOSIZE
        )
        self.refresh()

    def resize(self, width, height):
        """Resize window to specified dimensions"""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            self.position[0], self.position[1],
            int(width), int(height),
            win32con.SWP_NOMOVE
        )
        self.refresh()

    def focus(self):
        """Bring window to foreground and focus it"""
        win32gui.BringWindowToTop(self.hwnd)
        win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)
        win32gui.SetForegroundWindow(self.hwnd)

    def hide(self):
        """Hide the window"""
        win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
        self.refresh()

    def show(self):
        """Show the window"""
        win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)
        self.refresh()

    def minimize(self):
        """Minimize the window"""
        win32gui.ShowWindow(self.hwnd, win32con.SW_MINIMIZE)
        self.refresh()

    def maximize(self):
        """Maximize the window"""
        win32gui.ShowWindow(self.hwnd, win32con.SW_MAXIMIZE)
        self.refresh()

    def center(self, screen_width=None, screen_height=None):
        """Center window on screen or on provided dimensions"""
        if screen_width is None:
            screen_width = win32api.GetSystemMetrics(0)
        if screen_height is None:
            screen_height = win32api.GetSystemMetrics(1)

        x = screen_width // 2 - self.size[0] // 2
        y = screen_height // 2 - self.size[1] // 2
        self.move(x, y)

    def destroy(self):
        """Destroy/close the window"""
        win32gui.DestroyWindow(self.hwnd)

    def __eq__(self, other):
        """Compare window objects by their properties"""
        if not isinstance(other, Window):
            return False

        # We don't compare hwnd directly as we want to detect property changes
        for key in ["title", "position", "size", "visibility", "controls_state", "process_id"]:
            if getattr(self, key) != getattr(other, key):
                return False
        return True

    def __str__(self):
        """String representation of the window"""
        return f"Window(hwnd={self.hwnd}, title='{self.title}', class='{self.class_name}')"


def get_active_window():
    """Get the currently active window"""
    return Window(win32gui.GetForegroundWindow())


def get_all_windows():
    """Get all visible windows on the system"""
    windows = []

    def enum_windows_callback(hwnd, _):
        if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
            windows.append(Window(hwnd))
        return True

    win32gui.EnumWindows(enum_windows_callback, None)
    return windows


# Example usage
if __name__ == "__main__":
    active_window = get_active_window()
    print(f"Active window: {active_window}")
    print("Properties:")
    for key, value in active_window.to_dict().items():
        print(f"  {key}: {value}")