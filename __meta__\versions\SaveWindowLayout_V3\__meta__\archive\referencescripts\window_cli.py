"""
window_cli.py - Command-line interface for window management utilities

This module provides a flexible command-line interface for interacting with
window management functionality, supporting both interactive menus and
command-line arguments.
"""

# Standard imports
import os
import sys
import argparse
import json
from typing import Dict, List, Callable, Any, Optional, Union

# Import loguru for logging
from loguru import logger

# Import our window modules
from window_base import get_all_windows, get_active_window
from window_explorer import get_explorer_windows
from window_utility import DuplicateWindowCloser
from window_layout_manager import WindowLayoutManager

# Optional rich formatting for enhanced CLI
try:
    from rich.console import Console
    from rich.prompt import Prompt
    from rich.table import Table
    from rich import box
    HAS_RICH = True
    console = Console()
except ImportError:
    HAS_RICH = False
    console = None

# Optional inquirer for interactive prompts
try:
    import inquirer3
    from inquirer3.themes import load_theme_from_dict
    HAS_INQUIRER = True
except ImportError:
    HAS_INQUIRER = False

# Set up logging
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add a stderr handler with INFO level


# ----------------------------------------
# Menu actions implementation
# ----------------------------------------

def list_all_windows():
    """Display information about all windows"""
    windows = get_all_windows()
    _display_windows("All Windows", windows)


def list_explorer_windows():
    """Display information about explorer windows"""
    windows = get_explorer_windows()
    _display_windows("Explorer Windows", windows)


def list_other_windows():
    """Display information about non-explorer windows"""
    all_windows = get_all_windows()
    explorer_windows = get_explorer_windows()
    explorer_hwnds = [w.hwnd for w in explorer_windows]
    other_windows = [w for w in all_windows if w.hwnd not in explorer_hwnds]
    _display_windows("Other Windows", other_windows)


def _display_windows(title: str, windows: List[Any]):
    """Helper function to display windows in a formatted way"""
    if not windows:
        print(f"No {title.lower()} found.")
        return

    print(f"\n{title}: {len(windows)} found")

    if HAS_RICH:
        # With rich, we can create a nice table that auto-adjusts
        table = Table(title=title, box=box.ASCII, expand=True)
        table.add_column("Handle", style="dim", no_wrap=True)
        table.add_column("Title", style="bold", ratio=4)  # Title gets more space
        table.add_column("Class", ratio=2)
        table.add_column("Position", no_wrap=True)
        table.add_column("Size", no_wrap=True)

        for window in windows:
            handle = str(window.hwnd)
            # No need to truncate with rich tables - they handle wrapping
            title = window.title
            position = f"({window.position[0]}, {window.position[1]})"
            size = f"{window.size[0]}x{window.size[1]}"
            table.add_row(handle, title, window.class_name, position, size)

        console.print(table)
        console.print("[dim]If text is truncated, try increasing your terminal width or maximizing the window.[/dim]")
    else:
        # For non-rich mode, we need to calculate terminal width and adjust columns
        try:
            # Try to get terminal size
            import shutil
            terminal_width = shutil.get_terminal_size().columns
        except Exception:
            # Fall back to a reasonable default
            terminal_width = 120

        # Calculate column widths based on terminal width
        # Reserve fixed space for Handle, Position and Size columns
        handle_width = 10
        position_width = 20
        size_width = 15

        # Calculate remaining space for Title and Class
        remaining_width = terminal_width - handle_width - position_width - size_width - 5  # 5 for separators

        # Divide remaining space between Title (more) and Class (less)
        class_width = min(30, remaining_width // 3)
        title_width = remaining_width - class_width

        # Ensure minimum widths
        title_width = max(20, title_width)
        class_width = max(10, class_width)

        # Create the format string
        format_str = f"{{:<{handle_width}}} {{:<{title_width}}} {{:<{class_width}}} {{:<{position_width}}} {{:<{size_width}}}"

        # Print header
        print("\n" + format_str.format("Handle", "Title", "Class", "Position", "Size"))
        print("-" * min(terminal_width, handle_width + title_width + class_width + position_width + size_width + 4))

        for window in windows:
            # Truncate title and class to fit in columns with ellipsis
            truncated_title = window.title
            if len(truncated_title) > title_width - 3:
                truncated_title = truncated_title[:title_width-3] + '...'

            truncated_class = window.class_name
            if len(truncated_class) > class_width - 3:
                truncated_class = truncated_class[:class_width-3] + '...'

            position = f"({window.position[0]}, {window.position[1]})"
            size = f"{window.size[0]}x{window.size[1]}"

            print(format_str.format(
                window.hwnd, truncated_title, truncated_class, position, size))

        # Add a note about terminal width
        print(f"\nNote: Your terminal width is {terminal_width} characters. For better display, increase terminal width.")


def close_all_duplicate_windows():
    """Close all types of duplicate windows"""
    closed = DuplicateWindowCloser.close_duplicate_windows(window_type="all")
    logger.info(f"Closed {len(closed)} duplicate windows")


def close_duplicate_explorer_windows():
    """Close duplicate explorer windows only"""
    closed = DuplicateWindowCloser.close_duplicate_explorer_windows()
    logger.info(f"Closed {len(closed)} duplicate explorer windows")


def close_duplicate_other_windows():
    """Close duplicate non-explorer windows"""
    closed = DuplicateWindowCloser.close_duplicate_windows(window_type="other")
    logger.info(f"Closed {len(closed)} duplicate non-explorer windows")


def export_all_windows():
    """Save layout of all open windows"""
    manager = WindowLayoutManager()
    filename = manager.save_layout("AllWindows")
    logger.info(f"Saved layout to {filename}")
    if HAS_RICH:
        console.print(f"[bold green]Layout saved to:[/] {filename}")
    else:
        print(f"Layout saved to: {filename}")


def export_explorer_windows():
    """Save layout of explorer windows only"""
    manager = WindowLayoutManager()
    filename = manager.save_layout("ExplorerWindows", include_explorer=True)
    logger.info(f"Saved explorer layout to {filename}")
    if HAS_RICH:
        console.print(f"[bold green]Explorer layout saved to:[/] {filename}")
    else:
        print(f"Explorer layout saved to: {filename}")


def export_other_windows():
    """Save layout of non-explorer windows"""
    # We need a custom implementation here
    all_windows = get_all_windows()
    explorer_windows = get_explorer_windows()
    explorer_hwnds = [w.hwnd for w in explorer_windows]
    other_windows = [w for w in all_windows if w.hwnd not in explorer_hwnds]

    layout = {
        "name": "OtherWindows",
        "windows": [w.to_dict() for w in other_windows]
    }

    filename = "OtherWindows.layout.json"
    with open(filename, "w") as f:
        json.dump(layout, f, indent=2)

    logger.info(f"Saved other windows layout to {filename}")
    if HAS_RICH:
        console.print(f"[bold green]Other windows layout saved to:[/] {filename}")
    else:
        print(f"Other windows layout saved to: {filename}")


def import_all_windows():
    """Load and apply saved layout for all windows"""
    manager = WindowLayoutManager()
    layouts = manager.get_available_layouts()

    if not layouts:
        logger.warning("No layouts found")
        print("No layouts found")
        return

    layout_path = _select_layout(layouts)
    if not layout_path:
        return

    layout = manager.load_layout(layout_path)
    result = manager.apply_layout(layout, restore_explorer=True)

    if result:
        logger.info(f"Applied layout: {layout.name}")
        if HAS_RICH:
            console.print(f"[bold green]Applied layout:[/] {layout.name}")
        else:
            print(f"Applied layout: {layout.name}")
    else:
        logger.error(f"Failed to apply layout: {layout.name}")
        if HAS_RICH:
            console.print(f"[bold red]Failed to apply layout:[/] {layout.name}")
        else:
            print(f"Failed to apply layout: {layout.name}")


def import_explorer_windows():
    """Load and apply saved layout for explorer windows only"""
    manager = WindowLayoutManager()
    layouts = manager.get_available_layouts()

    if not layouts:
        logger.warning("No layouts found")
        print("No layouts found")
        return

    layout_path = _select_layout(layouts)
    if not layout_path:
        return

    layout = manager.load_layout(layout_path)

    # Filter to only explorer windows
    explorer_windows = [w for w in layout.windows
                      if w.get("class") == "CabinetWClass"]
    layout.windows = explorer_windows

    result = manager.apply_layout(layout, restore_explorer=True)

    if result:
        logger.info(f"Applied explorer layout: {layout.name}")
        if HAS_RICH:
            console.print(f"[bold green]Applied explorer layout:[/] {layout.name}")
        else:
            print(f"Applied explorer layout: {layout.name}")
    else:
        logger.error(f"Failed to apply explorer layout: {layout.name}")
        if HAS_RICH:
            console.print(f"[bold red]Failed to apply explorer layout:[/] {layout.name}")
        else:
            print(f"Failed to apply explorer layout: {layout.name}")


def import_other_windows():
    """Load and apply saved layout for non-explorer windows"""
    manager = WindowLayoutManager()
    layouts = manager.get_available_layouts()

    if not layouts:
        logger.warning("No layouts found")
        print("No layouts found")
        return

    layout_path = _select_layout(layouts)
    if not layout_path:
        return

    layout = manager.load_layout(layout_path)

    # Filter to only non-explorer windows
    other_windows = [w for w in layout.windows
                    if w.get("class") != "CabinetWClass"]
    layout.windows = other_windows

    result = manager.apply_layout(layout, restore_explorer=False)

    if result:
        logger.info(f"Applied other windows layout: {layout.name}")
        if HAS_RICH:
            console.print(f"[bold green]Applied other windows layout:[/] {layout.name}")
        else:
            print(f"Applied other windows layout: {layout.name}")
    else:
        logger.error(f"Failed to apply other windows layout: {layout.name}")
        if HAS_RICH:
            console.print(f"[bold red]Failed to apply other windows layout:[/] {layout.name}")
        else:
            print(f"Failed to apply other windows layout: {layout.name}")


def _select_layout(layouts: List[str]) -> Optional[str]:
    """Helper function to let user select a layout from the available list"""
    print("\nAvailable layouts:")
    for i, layout in enumerate(layouts):
        print(f"{i+1}: {layout}")

    try:
        if HAS_RICH:
            choice = Prompt.ask("Select layout number", default="1")
            index = int(choice) - 1
        else:
            choice = input("\nSelect layout number: ")
            index = int(choice) - 1

        if 0 <= index < len(layouts):
            return layouts[index]
        else:
            print("Invalid selection")
            return None
    except ValueError:
        print("Invalid input")
        return None


def exit_program():
    """Exit the program"""
    print("Exiting the window manager.")
    sys.exit(0)


# ----------------------------------------
# CLI with inquirer.py (interactive menu)
# ----------------------------------------

# Custom theme for inquirer
custom_theme_dict = {
    "Question": {
        "mark_color": "bold_blue",
        "brackets_color": "gray75",
        "default_color": "default",
    },
    "Checkbox": {
        "selection_color": "bold_blue",
        "selection_icon": ">",
        "selected_icon": "[X]",
        "selected_color": "bold_blue",
        "unselected_color": "gray60",
        "unselected_icon": "[ ]",
        "locked_option_color": "gray40",
    },
    "List": {
        "selection_color": "bold_blue",
        "selection_cursor": ">",
        "unselected_color": "gray60",
    }
}


def run_interactive_menu():
    """Run the interactive menu using inquirer if available"""
    if not HAS_INQUIRER:
        print("The inquirer3 package is required for interactive menus.")
        print("Please install it with: pip install inquirer3")
        run_simple_menu()
        return

    # Load custom theme
    try:
        custom_theme = load_theme_from_dict(custom_theme_dict)
    except Exception:
        custom_theme = None

    # Menu actions dictionary
    menu_actions = {
        "list": {
            "All Windows": list_all_windows,
            "Explorer Windows": list_explorer_windows,
            "Other Windows": list_other_windows
        },
        "close_duplicates": {
            "All Duplicate Windows": close_all_duplicate_windows,
            "Duplicate Explorer Windows": close_duplicate_explorer_windows,
            "Duplicate Other Windows": close_duplicate_other_windows
        },
        "export": {
            "All Windows": export_all_windows,
            "Explorer Windows": export_explorer_windows,
            "Other Windows": export_other_windows
        },
        "import": {
            "All Windows": import_all_windows,
            "Explorer Windows": import_explorer_windows,
            "Other Windows": import_other_windows
        }
    }

    def ask_menu_question(menu_name, options):
        """Generic function to ask a menu question using inquirer"""
        choices = [(f"{i+1}. {option}", option) for i, option in enumerate(options.keys())]
        questions = [
            inquirer3.List(
                'choice',
                message=f"{menu_name}: Choose an option",
                choices=choices,
                carousel=True
            )
        ]
        answers = inquirer3.prompt(questions, theme=custom_theme)
        if not answers:  # User pressed Ctrl+C
            return None

        selected_option = answers['choice']
        selected_name = selected_option.split('. ')[1] if '. ' in selected_option else selected_option
        return options.get(selected_name)

    def execute_menu_choice(menu_name, options):
        """Executes the chosen action from a menu"""
        while True:
            try:
                choice_func = ask_menu_question(menu_name, options)
                if choice_func is None:  # User cancelled
                    break

                if callable(choice_func):
                    choice_func()

                if menu_name != "Main Menu":  # Don't break for main menu
                    break
            except (KeyboardInterrupt, EOFError):
                print(f"\nReturning to previous menu.")
                break

    # Main menu options
    main_options = {
        "List Windows": lambda: execute_menu_choice("List Windows Menu", menu_actions["list"]),
        "Close Duplicate Windows": lambda: execute_menu_choice("Close Duplicates Menu", menu_actions["close_duplicates"]),
        "Export Layout": lambda: execute_menu_choice("Export Layout Menu", menu_actions["export"]),
        "Import Layout": lambda: execute_menu_choice("Import Layout Menu", menu_actions["import"]),
        "Exit": exit_program
    }

    # Run the main menu
    execute_menu_choice("Main Menu", main_options)


# ----------------------------------------
# Simple menu (fallback without inquirer)
# ----------------------------------------

def run_simple_menu():
    """Run a simple text-based menu as fallback"""
    while True:
        print("\nWindow Manager Main Menu")
        print("========================")
        print("1. List Windows")
        print("2. Close Duplicate Windows")
        print("3. Export Layout")
        print("4. Import Layout")
        print("5. Exit")

        try:
            choice = input("\nEnter your choice (1-5): ")

            if choice == "1":
                list_submenu([
                    ("All Windows", list_all_windows),
                    ("Explorer Windows", list_explorer_windows),
                    ("Other Windows", list_other_windows)
                ], "List Windows")
            elif choice == "2":
                list_submenu([
                    ("All Duplicate Windows", close_all_duplicate_windows),
                    ("Duplicate Explorer Windows", close_duplicate_explorer_windows),
                    ("Duplicate Other Windows", close_duplicate_other_windows)
                ], "Close Duplicate Windows")
            elif choice == "3":
                list_submenu([
                    ("All Windows", export_all_windows),
                    ("Explorer Windows", export_explorer_windows),
                    ("Other Windows", export_other_windows)
                ], "Export Layout")
            elif choice == "4":
                list_submenu([
                    ("All Windows", import_all_windows),
                    ("Explorer Windows", import_explorer_windows),
                    ("Other Windows", import_other_windows)
                ], "Import Layout")
            elif choice == "5":
                exit_program()
            else:
                print("Invalid choice. Please try again.")
        except KeyboardInterrupt:
            print("\nExiting...")
            break


def list_submenu(options, title):
    """Display a submenu with the given options"""
    while True:
        print(f"\n{title} Menu")
        print("=" * (len(title) + 5))

        for i, (name, _) in enumerate(options):
            print(f"{i+1}. {name}")
        print(f"{len(options)+1}. Back to Main Menu")

        try:
            choice = input(f"\nEnter your choice (1-{len(options)+1}): ")

            if choice.isdigit() and 1 <= int(choice) <= len(options):
                options[int(choice)-1][1]()  # Execute the selected function
            elif choice == str(len(options)+1):
                break
            else:
                print("Invalid choice. Please try again.")
        except KeyboardInterrupt:
            print("\nReturning to main menu...")
            break


# ----------------------------------------
# Command-line argument parser
# ----------------------------------------

def parse_arguments():
    """Parse command-line arguments for non-interactive usage"""
    parser = argparse.ArgumentParser(description="Window and layout management utility.")

    # Window listing options
    list_group = parser.add_argument_group("Window Listing")
    list_group.add_argument('--list-all', action='store_true', help="List all windows")
    list_group.add_argument('--list-explorer', action='store_true', help="List Explorer windows")
    list_group.add_argument('--list-other', action='store_true', help="List non-Explorer windows")

    # Duplicate window management
    dup_group = parser.add_argument_group("Duplicate Window Management")
    dup_group.add_argument('--close-duplicates', action='store_true', help="Close all duplicate windows")
    dup_group.add_argument('--close-duplicate-explorer', action='store_true', help="Close duplicate Explorer windows")
    dup_group.add_argument('--close-duplicate-other', action='store_true', help="Close duplicate non-Explorer windows")

    # Layout management
    layout_group = parser.add_argument_group("Layout Management")
    layout_group.add_argument('--save-layout', action='store_true', help="Save the current layout")
    layout_group.add_argument('--save-explorer-layout', action='store_true', help="Save Explorer layout")
    layout_group.add_argument('--save-other-layout', action='store_true', help="Save non-Explorer layout")
    layout_group.add_argument('--apply-layout', action='store_true', help="Apply a saved layout")
    layout_group.add_argument('--apply-explorer-layout', action='store_true', help="Apply a saved Explorer layout")
    layout_group.add_argument('--apply-other-layout', action='store_true', help="Apply a saved non-Explorer layout")

    # Other options
    parser.add_argument('--interactive', action='store_true', help="Run in interactive mode")
    parser.add_argument('--debug', action='store_true', help="Enable debug logging")

    return parser.parse_args()


def display_summary(args):
    """Display a summary of the arguments"""
    if not HAS_RICH:
        return

    table = Table(title="Configuration Summary", show_header=True, header_style="bold blue", box=box.ASCII)
    table.add_column("Operation", style="dim", width=30)
    table.add_column("Value", style="bold cyan")

    # Add rows for each argument
    for arg, value in vars(args).items():
        if value:
            table.add_row(arg.replace('_', ' ').title(), "Yes")

    console.print(table)


def handle_cli_args(args):
    """Handle command-line arguments and execute appropriate actions"""
    # Set up logging level
    if args.debug:
        logger.remove()  # Remove existing handlers
        logger.add(sys.stderr, level="DEBUG")

    # Display configuration summary if rich is available
    if HAS_RICH:
        display_summary(args)

    # Perform actions based on arguments
    if args.list_all:
        list_all_windows()

    if args.list_explorer:
        list_explorer_windows()

    if args.list_other:
        list_other_windows()

    if args.close_duplicates:
        close_all_duplicate_windows()

    if args.close_duplicate_explorer:
        close_duplicate_explorer_windows()

    if args.close_duplicate_other:
        close_duplicate_other_windows()

    if args.save_layout:
        export_all_windows()

    if args.save_explorer_layout:
        export_explorer_windows()

    if args.save_other_layout:
        export_other_windows()

    if args.apply_layout:
        import_all_windows()

    if args.apply_explorer_layout:
        import_explorer_windows()

    if args.apply_other_layout:
        import_other_windows()


# ----------------------------------------
# Main function
# ----------------------------------------

def main():
    """Main function to run the CLI"""
    # Parse command-line arguments
    args = parse_arguments()

    # If any action arguments were specified, handle them
    has_actions = any([getattr(args, arg) for arg in vars(args) if arg != 'interactive' and arg != 'debug'])

    if has_actions:
        handle_cli_args(args)
    elif args.interactive:
        # Run the interactive menu
        if HAS_INQUIRER:
            run_interactive_menu()
        else:
            run_simple_menu()
    else:
        # Default to interactive menu if no arguments specified
        if HAS_INQUIRER:
            run_interactive_menu()
        else:
            run_simple_menu()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nExiting due to user interrupt.")
        sys.exit(0)
    except Exception as e:
        logger.exception(f"An unexpected error occurred: {e}")
        print(f"An error occurred: {e}")
        sys.exit(1)