"""
window_explorer.py - Explorer window specific functionality

This module extends the base Window class with specialized functionality for
Windows Explorer windows, including folder view settings and file operations.
"""

# Standard imports
import os
import sys
import re
import urllib.parse

# Win32 API imports
import win32com.client
import win32com.shell.shellcon as shellcon
from win32com.shell import shell
import win32gui

# Import our base window class
from window_base import Window, WindowType

class ExplorerWindow(Window):
    """Class representing a Windows Explorer window with folder view operations"""

    def __init__(self, hwnd=None):
        """Initialize an ExplorerWindow object"""
        super().__init__(hwnd)

        # Explorer-specific properties
        self.location_path = ""
        self.location_url = ""
        self.create_command = ""

        # View settings
        self.icon_size = None
        self.view_mode = None
        self.sort_column = None
        self.group_by = None

        # File selection
        self.selected_files = []
        self.all_files = []
        self.focused_file = None

        # Shell object for this window
        self.shell_window = None

        # If this is an Explorer window, get the explorer-specific data
        if self.is_explorer():
            self.refresh_explorer_data()

    def is_explorer(self):
        """Check if this is a Windows Explorer window"""
        return self.class_name == 'CabinetWClass' and self.process_path and 'explorer.exe' in self.process_path.lower()

    def refresh_explorer_data(self):
        """Refresh Explorer-specific window data"""
        try:
            # Get shell windows
            shell_app = win32com.client.Dispatch('Shell.Application')
            shell_windows = shell_app.Windows()

            # Find our window
            for window in shell_windows:
                if window.HWND == self.hwnd:
                    self.shell_window = window

                    # Get location info
                    self.location_path = window.Document.Folder.Self.Path if hasattr(window, 'Document') else ""
                    self.location_url = window.LocationURL if hasattr(window, 'LocationURL') else ""

                    # Get view settings if available
                    if hasattr(window, 'Document'):
                        self.icon_size = window.Document.IconSize if hasattr(window.Document, 'IconSize') else None
                        self.view_mode = window.Document.CurrentViewMode if hasattr(window.Document, 'CurrentViewMode') else None
                        self.sort_column = window.Document.SortColumns if hasattr(window.Document, 'SortColumns') else None
                        self.group_by = window.Document.GroupBy if hasattr(window.Document, 'GroupBy') else None

                    # Get file selection if available
                    if hasattr(window, 'Document') and hasattr(window.Document, 'SelectedItems'):
                        self.selected_files = [item.Name for item in window.Document.SelectedItems()]

                    if hasattr(window, 'Document') and hasattr(window.Document, 'Folder') and hasattr(window.Document.Folder, 'Items'):
                        self.all_files = [item.Name for item in window.Document.Folder.Items()]

                    if hasattr(window, 'Document') and hasattr(window.Document, 'FocusedItem'):
                        try:
                            self.focused_file = window.Document.FocusedItem.Name
                        except:
                            self.focused_file = None

                    # Set the right window type
                    if self.is_special_folder():
                        self.type = WindowType.SPECIAL_FOLDER
                        self.create_command = self.get_special_folder_command()
                    else:
                        self.type = WindowType.NORMAL_FOLDER
                        self.create_command = f"explorer \"{self.location_path}\""

                    return True

            return False
        except Exception as e:
            print(f"Error refreshing Explorer data: {e}")
            return False

    def is_special_folder(self):
        """Check if this is a special folder by checking if path contains GUID"""
        if not self.location_path:
            return False

        special_folders = self.get_special_folders_mapping()
        return self.title in special_folders

    def get_special_folder_command(self):
        """Get the command to create this special folder window"""
        special_folders = self.get_special_folders_mapping()

        if self.title in special_folders:
            path = special_folders[self.title]
            # Check if the path is a GUID
            if re.search(r'::{[\w-]*}', path) or not os.path.exists(path):
                return f"explorer shell:{path}"
            else:
                return f"explorer \"{path}\""

        return None

    def get_special_folders_mapping(self):
        """Get mapping of special folder names to paths"""
        try:
            shell_app = win32com.client.Dispatch('Shell.Application')

            # Get CSIDL constants
            csidl_constants = [getattr(shellcon, const) for const in dir(shellcon)
                              if const.startswith('CSIDL_')]

            # Get namespaces for each constant
            csidl_namespaces = [shell_app.Namespace(const) for const in csidl_constants]
            valid_namespaces = [ns for ns in csidl_namespaces if hasattr(ns, 'Self')]

            # Get name and path for each namespace
            special_folders = {ns.Self.Name: ns.Self.Path for ns in valid_namespaces
                              if hasattr(ns, 'Self') and hasattr(ns.Self, 'Name') and hasattr(ns.Self, 'Path')}

            return special_folders
        except Exception as e:
            print(f"Error getting special folders: {e}")
            return {}

    def select_files(self, file_names):
        """Select files in the Explorer window by name"""
        if not self.shell_window or not hasattr(self.shell_window, 'Document'):
            return False

        try:
            matching_files = [item for item in self.shell_window.Document.Folder.Items()
                             if item.Name in file_names]

            for file_obj in matching_files:
                self.shell_window.Document.SelectItem(file_obj, 1)

            return True
        except Exception as e:
            print(f"Error selecting files: {e}")
            return False

    def focus_file(self, file_name):
        """Set focus on a specific file in the Explorer window"""
        if not self.shell_window or not hasattr(self.shell_window, 'Document'):
            return False

        try:
            matching_files = [item for item in self.shell_window.Document.Folder.Items()
                             if item.Name == file_name]

            if matching_files:
                self.shell_window.Document.SelectItem(matching_files[0], 16)
                return True

            return False
        except Exception as e:
            print(f"Error focusing file: {e}")
            return False

    def set_view_mode(self, mode):
        """Set the view mode for the Explorer window

        Mode values:
        1: Icons
        2: Small icons
        3: List
        4: Details
        5: Thumbnails
        6: Tiles
        7: Thumbstrip
        8: Content
        """
        if not self.shell_window or not hasattr(self.shell_window, 'Document'):
            return False

        try:
            self.shell_window.Document.CurrentViewMode = mode
            return True
        except Exception as e:
            print(f"Error setting view mode: {e}")
            return False

    def set_icon_size(self, size):
        """Set the icon size for the Explorer window"""
        if not self.shell_window or not hasattr(self.shell_window, 'Document'):
            return False

        try:
            self.shell_window.Document.IconSize = size
            return True
        except Exception as e:
            print(f"Error setting icon size: {e}")
            return False

    def to_dict(self):
        """Convert window properties to a dictionary, including Explorer-specific properties"""
        base_dict = super().to_dict()

        explorer_dict = {
            "location_path": self.location_path,
            "location_url": self.location_url,
            "create_command": self.create_command,
            "icon_size": self.icon_size,
            "view_mode": self.view_mode,
            "sort_column": self.sort_column,
            "group_by": self.group_by,
            "selected_files": self.selected_files,
            "focused_file": self.focused_file,
            "file_count": len(self.all_files) if self.all_files else 0
        }

        return {**base_dict, **explorer_dict}

    def __str__(self):
        """String representation of the Explorer window"""
        return f"ExplorerWindow(hwnd={self.hwnd}, title='{self.title}', path='{self.location_path}')"


def get_explorer_windows():
    """Get all Explorer windows on the system"""
    explorer_windows = []

    def enum_windows_callback(hwnd, _):
        if win32gui.IsWindowVisible(hwnd):
            try:
                class_name = win32gui.GetClassName(hwnd)
                if class_name == 'CabinetWClass':
                    explorer_windows.append(ExplorerWindow(hwnd))
            except:
                pass
        return True

    win32gui.EnumWindows(enum_windows_callback, None)
    return explorer_windows


def create_explorer_window(path):
    """Create a new Explorer window at the specified path"""
    try:
        shell_app = win32com.client.Dispatch('Shell.Application')
        shell_app.Explore(path)
        return True
    except Exception as e:
        print(f"Error creating Explorer window: {e}")
        return False


# Example usage
if __name__ == "__main__":
    explorer_windows = get_explorer_windows()

    if explorer_windows:
        print(f"Found {len(explorer_windows)} Explorer windows:")
        for i, window in enumerate(explorer_windows):
            print(f"\n{i+1}. {window}")
            print(f"   Path: {window.location_path}")
            print(f"   View mode: {window.view_mode}")
            print(f"   Files: {len(window.all_files)}")
            print(f"   Selected: {window.selected_files}")
    else:
        print("No Explorer windows found.")
