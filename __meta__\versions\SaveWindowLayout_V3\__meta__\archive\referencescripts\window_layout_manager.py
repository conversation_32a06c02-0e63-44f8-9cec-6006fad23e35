"""
window_layout_manager.py - Window layout management functionality

This module provides classes and functions for saving, restoring,
and managing window layouts across multiple monitors.

Features:
* WindowLayout class for capturing, saving, and restoring window layouts
* Supports multiple monitors and window types
* Robust error handling for window positioning and monitor enumeration
* Layout file persistence with multiple directory support
* CLI and interactive mode for managing layouts
* Debug logging with loguru

Usage:
* Save current window layout:   python window_layout_manager.py --save layout_name.json
* Apply saved layout:           python window_layout_manager.py --apply layout_name.json
* List all saved layouts:       python window_layout_manager.py --list
* Enable debug logging:         python window_layout_manager.py --debug (add to any command)
* Interactive mode:             python window_layout_manager.py
"""

# Standard imports
import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Union
import datetime  # Import datetime module correctly

# Import loguru for improved logging
from loguru import logger

# Configure logger
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add a stderr handler with INFO level

# Win32 API imports
import win32gui
import win32api
import win32con
import win32process

# Import from internal modules
from window_base import Window, WindowType, get_all_windows
from window_explorer import ExplorerWindow, get_explorer_windows


class WindowLayout:
    """Handles capturing, saving, and restoring window layouts"""

    def __init__(self, name: str = "", layouts_dir: str = None):
        """Initialize a layout with the given name

        Args:
            name: Name of the layout
            layouts_dir: Directory to store layout files
        """
        # Set layout properties
        self.name = name or f"Layout_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.creation_time = datetime.datetime.now().isoformat()

        # Set layouts directory (default to layouts folder in script directory)
        if layouts_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.layouts_dir = os.path.join(script_dir, "layouts")
        else:
            self.layouts_dir = layouts_dir

        # Make sure layouts directory exists
        os.makedirs(self.layouts_dir, exist_ok=True)

        # Initialize empty collections
        self.monitors = []
        self.windows = []

        # Get monitor information
        self.monitors = self._get_monitor_info()

    def _get_monitor_info(self) -> List[Dict[str, Any]]:
        """Get information about all monitors in the system

        Returns:
            List of dictionaries with monitor information
        """
        monitors = []

        try:
            # Directly get monitor handles - PyWin32 handles the callback internally
            monitor_handles = win32api.EnumDisplayMonitors()

            logger.debug(f"Found {len(monitor_handles)} monitors")

            # Process each monitor handle
            for i, monitor_handle in enumerate(monitor_handles):
                try:
                    # Get monitor info directly from handle
                    monitor_info = win32api.GetMonitorInfo(monitor_handle[0])

                    # Extract monitor information
                    monitor_data = {
                        "device": monitor_info["Device"],
                        "work_area": monitor_info["Work"],
                        "monitor_area": monitor_info["Monitor"],
                        "is_primary": bool(monitor_info["Flags"] & win32con.MONITORINFOF_PRIMARY)
                    }

                    monitors.append(monitor_data)
                    logger.debug(f"Monitor {i+1}: {monitor_info['Device']} - Primary: {monitor_data['is_primary']}")

                except Exception as e:
                    # Log and continue to next monitor on error
                    logger.debug(f"Error getting info for monitor {i+1}: {e}")
                    continue

        except Exception as e:
            # Fall back to a single monitor if enumeration fails
            logger.warning(f"Error enumerating monitors: {e}")

            # Add a default primary monitor entry for fallback
            try:
                # Get primary monitor info from desktop window
                desktop = win32gui.GetDesktopWindow()
                desktop_rect = win32gui.GetWindowRect(desktop)
                monitors.append({
                    "device": "DEFAULT",
                    "work_area": desktop_rect,
                    "monitor_area": desktop_rect,
                    "is_primary": True
                })
                logger.info("Using desktop window information as fallback for monitor data")
            except Exception as e:
                # Last resort fallback with standard resolution values
                logger.warning(f"Fallback to desktop window failed: {e}")
                monitors.append({
                    "device": "FALLBACK",
                    "work_area": (0, 0, 1920, 1080),
                    "monitor_area": (0, 0, 1920, 1080),
                    "is_primary": True
                })
                logger.info("Using hardcoded fallback values for monitor data")

        logger.debug(f"Collected information for {len(monitors)} monitors")
        return monitors

    def capture_windows(self) -> List[Dict[str, Any]]:
        """Capture information about all visible windows

        Returns:
            List of dictionaries with window information
        """
        windows_data = []

        try:
            # Get all windows
            all_windows = get_all_windows()
            logger.debug(f"Found {len(all_windows)} windows")

            # Process each window
            for window in all_windows:
                # Skip certain windows
                if self._should_skip_window(window):
                    continue

                # Get window information
                window_data = window.to_dict()

                # Add to collection
                windows_data.append(window_data)

            # Store windows in instance
            self.windows = windows_data
            logger.debug(f"Captured {len(windows_data)} windows")

            return windows_data
        except Exception as e:
            logger.error(f"Error capturing windows: {e}")
            return []

    def _should_skip_window(self, window: Window) -> bool:
        """Check if a window should be skipped

        Args:
            window: Window object

        Returns:
            True if the window should be skipped, False otherwise
        """
        # Skip windows with empty titles
        if not window.title:
            return True

        # Skip certain window classes
        skip_classes = [
            'Shell_TrayWnd',  # Taskbar
            'Progman',        # Desktop
            'WorkerW',        # Desktop
            'DV2ControlHost', # Start menu
            'Windows.UI.Core.CoreWindow'  # Modern UI
        ]
        if window.class_name in skip_classes:
            return True

        # Skip small windows (likely controls/overlays)
        if window.size[0] < 50 or window.size[1] < 50:
            return True

        # Skip invisible windows
        if not window.visibility:
            return True

        return False

    def save_to_file(self, filename: str = "", include_all: bool = False) -> str:
        """Save the layout to a file

        Args:
            filename: Path to save the layout file, or empty to generate
                     a filename based on the layout name
            include_all: Include all windows in the layout

        Returns:
            Path to the saved file
        """
        # Generate filename if not provided
        if not filename:
            # Create filename based on layout name and timestamp
            timestamp = int(time.time())
            filename = f"{self.name}_{timestamp}.json" if self.name else f"layout_{timestamp}.json"

            # Ensure the layouts directory exists
            os.makedirs(self.layouts_dir, exist_ok=True)

            # Use the layouts directory
            filename = os.path.join(self.layouts_dir, filename)
        elif not os.path.isabs(filename):
            # If it's a relative path but doesn't explicitly include the layouts directory,
            # and the file doesn't exist in the current directory, use the layouts directory
            if not os.path.exists(filename) and not filename.startswith(self.layouts_dir):
                os.makedirs(self.layouts_dir, exist_ok=True)
                filename = os.path.join(self.layouts_dir, filename)

        logger.debug(f"Saving layout to {filename}")

        # Create layout data dictionary
        layout_data = {
            "name": self.name,
            "creation_time": datetime.datetime.now().isoformat(),
            "save_time": datetime.datetime.now().isoformat(),
            "monitors": self.monitors,
            "windows": self.windows if include_all else self.windows[:10]  # Limit to 10 windows for saving
        }

        # Write to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(layout_data, f, indent=2)

        logger.info(f"Saved layout with {len(self.windows)} windows to {filename}")
        return filename

    @staticmethod
    def load_from_file(filename: str) -> Dict[str, Any]:
        """Load a layout from a file

        Args:
            filename: Path to the layout file

        Returns:
            Layout data dictionary or empty dict if loading failed
        """
        try:
            if not os.path.exists(filename):
                logger.error(f"Layout file does not exist: {filename}")
                return {}

            with open(filename, 'r', encoding='utf-8') as f:
                layout_data = json.load(f)

            logger.debug(f"Loaded layout from {filename} with {len(layout_data.get('windows', []))} windows")
            return layout_data
        except Exception as e:
            logger.error(f"Error loading layout from {filename}: {e}")
            return {}

    def apply_layout(self, layout_file: str) -> int:
        """Apply a window layout from a saved file

        Args:
            layout_file: Path to the layout file

        Returns:
            int: Number of windows successfully restored
        """
        layout_data = self.load_from_file(layout_file)
        if not layout_data:
            logger.error(f"Failed to load layout from {layout_file}")
            return 0

        windows = layout_data.get("windows", [])
        if not windows:
            logger.warning(f"No windows found in layout {layout_file}")
            return 0

        logger.info(f"Applying layout with {len(windows)} windows from {layout_file}")

        # Track successfully restored windows
        restored_count = 0

        # Process each window in the layout
        for window in windows:
            hwnd = window.get("hwnd")

            # Skip windows with no handle
            if not hwnd:
                logger.warning(f"Window has no handle, skipping: {window.get('title', 'Unknown')}")
                continue

            # Check if window still exists
            if not win32gui.IsWindow(hwnd):
                logger.warning(f"Window {hwnd} no longer exists, trying to find by title")

                # Try to find window by title
                title = window.get("title")
                if title:
                    # Find window by title (partial match)
                    new_hwnd = win32gui.FindWindow(None, title)
                    if new_hwnd and win32gui.IsWindow(new_hwnd):
                        logger.info(f"Found window with title '{title}', using handle {new_hwnd}")
                        hwnd = new_hwnd
                    else:
                        # Try to find window by partial title
                        def enum_windows_callback(handle, results):
                            window_title = win32gui.GetWindowText(handle)
                            if title in window_title and win32gui.IsWindowVisible(handle):
                                results.append(handle)
                            return True

                        found_windows = []
                        win32gui.EnumWindows(enum_windows_callback, found_windows)

                        if found_windows:
                            hwnd = found_windows[0]
                            logger.info(f"Found window with partial title match '{title}', using handle {hwnd}")
                        else:
                            logger.warning(f"Could not find window with title '{title}', skipping")
                            continue
                else:
                    logger.warning("No title to search for, skipping window")
                    continue

            # Get window position and size from arrays
            try:
                pos = window.get("position", [0, 0])
                size = window.get("size", [800, 600])
                position = (pos[0], pos[1])
                dimensions = (size[0], size[1])

                # Get window state
                state = win32con.SW_SHOWNORMAL
                if window.get("controls_state") == win32con.SW_MAXIMIZE:
                    state = win32con.SW_MAXIMIZE
                elif window.get("controls_state") == win32con.SW_MINIMIZE:
                    state = win32con.SW_MINIMIZE

                logger.debug(f"Restoring window '{window.get('title')}' to position {position}, size {dimensions}, state {state}")

                # Restore the window
                if self.restore_window(hwnd, position, dimensions, state):
                    restored_count += 1
                    logger.debug(f"Successfully restored window '{window.get('title')}'")
                else:
                    logger.warning(f"Failed to restore window '{window.get('title')}'")
            except Exception as e:
                logger.error(f"Error processing window '{window.get('title')}': {e}")
                continue

        logger.info(f"Successfully restored {restored_count} out of {len(windows)} windows")
        return restored_count

    def restore_window(self, hwnd: int, position: Tuple[int, int], size: Tuple[int, int], state: int = win32con.SW_SHOWNORMAL) -> bool:
        """Restore a window to the specified position, size and state

        Args:
            hwnd: Window handle
            position: (x, y) position tuple
            size: (width, height) size tuple
            state: Window state (minimized, maximized, normal)

        Returns:
            bool: True if successful, False otherwise
        """
        if not win32gui.IsWindow(hwnd):
            logger.warning(f"Window {hwnd} no longer exists, cannot restore")
            return False

        try:
            # Set window position and size
            logger.debug(f"Restoring window {hwnd} to position {position} and size {size}")

            # First make sure the window is not minimized
            if win32gui.IsIconic(hwnd):
                logger.debug(f"Window {hwnd} is minimized, restoring first")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)

            # Position and size the window
            try:
                x, y = position
                width, height = size

                # Ensure coordinates are reasonable
                if x < -8000 or x > 8000 or y < -8000 or y > 8000:
                    logger.warning(f"Window position {position} seems invalid, using default (0,0)")
                    x, y = 0, 0

                if width <= 0 or width > 8000 or height <= 0 or height > 8000:
                    logger.warning(f"Window size {size} seems invalid, using default (800,600)")
                    width, height = 800, 600

                win32gui.MoveWindow(hwnd, x, y, width, height, True)
                logger.debug(f"Window {hwnd} positioned at {(x, y)} with size {(width, height)}")
            except Exception as e:
                logger.error(f"Error positioning window {hwnd}: {e}")

            # Set the window state (minimized, maximized, normal)
            try:
                win32gui.ShowWindow(hwnd, state)
                logger.debug(f"Window {hwnd} state set to {state}")
            except Exception as e:
                logger.error(f"Error setting window {hwnd} state: {e}")

            return True
        except Exception as e:
            logger.error(f"Failed to restore window {hwnd}: {e}")
            return False


class WindowLayoutManager:
    """Class for managing window layouts"""

    def __init__(self, layouts_dir: str = ""):
        """Initialize the window layout manager

        Args:
            layouts_dir: Directory to store layout files
        """
        self.layouts_dir = layouts_dir or os.path.join(os.path.dirname(os.path.abspath(__file__)), "layouts")
        os.makedirs(self.layouts_dir, exist_ok=True)

    def save_layout(self, name: str = "", include_explorer: bool = True) -> str:
        """Save the current window layout

        Args:
            name: Optional name for the layout
            include_explorer: Include special handling for Explorer windows

        Returns:
            Path to the saved layout file
        """
        layout = WindowLayout(name)
        layout.capture_windows()

        filename = os.path.join(self.layouts_dir, f"{layout.name}.layout.json")
        layout.save_to_file(filename)

        return filename

    def get_available_layouts(self) -> List[str]:
        """Get a list of available layout files

        Returns:
            List of layout filenames
        """
        layouts = []

        for file in os.listdir(self.layouts_dir):
            if file.endswith(".layout.json"):
                layouts.append(file)

        return layouts

    def load_layout(self, filename: str) -> WindowLayout:
        """Load a layout from file

        Args:
            filename: Name of the layout file

        Returns:
            WindowLayout object with the loaded layout
        """
        if not os.path.isabs(filename):
            filename = os.path.join(self.layouts_dir, filename)

        return WindowLayout.load_from_file(filename)

    def apply_layout(self, layout_file: str) -> int:
        """Apply a window layout from a saved file

        Args:
            layout_file: Path to the layout file

        Returns:
            int: Number of windows successfully restored
        """
        layout_data = self.load_from_file(layout_file)
        if not layout_data:
            logger.error(f"Failed to load layout from {layout_file}")
            return 0

        windows = layout_data.get("windows", [])
        if not windows:
            logger.warning(f"No windows found in layout {layout_file}")
            return 0

        logger.info(f"Applying layout with {len(windows)} windows from {layout_file}")

        # Track successfully restored windows
        restored_count = 0

        # Process each window in the layout
        for window in windows:
            hwnd = window.get("hwnd")

            # Skip windows with no handle
            if not hwnd:
                logger.warning(f"Window has no handle, skipping: {window.get('title', 'Unknown')}")
                continue

            # Check if window still exists
            if not win32gui.IsWindow(hwnd):
                logger.warning(f"Window {hwnd} no longer exists, trying to find by title")

                # Try to find window by title
                title = window.get("title")
                if title:
                    # Find window by title (partial match)
                    new_hwnd = win32gui.FindWindow(None, title)
                    if new_hwnd and win32gui.IsWindow(new_hwnd):
                        logger.info(f"Found window with title '{title}', using handle {new_hwnd}")
                        hwnd = new_hwnd
                    else:
                        # Try to find window by partial title
                        def enum_windows_callback(handle, results):
                            window_title = win32gui.GetWindowText(handle)
                            if title in window_title and win32gui.IsWindowVisible(handle):
                                results.append(handle)
                            return True

                        found_windows = []
                        win32gui.EnumWindows(enum_windows_callback, found_windows)

                        if found_windows:
                            hwnd = found_windows[0]
                            logger.info(f"Found window with partial title match '{title}', using handle {hwnd}")
                        else:
                            logger.warning(f"Could not find window with title '{title}', skipping")
                            continue
                else:
                    logger.warning("No title to search for, skipping window")
                    continue

            # Get window position and size from arrays
            try:
                pos = window.get("position", [0, 0])
                size = window.get("size", [800, 600])
                position = (pos[0], pos[1])
                dimensions = (size[0], size[1])

                # Get window state
                state = win32con.SW_SHOWNORMAL
                if window.get("controls_state") == win32con.SW_MAXIMIZE:
                    state = win32con.SW_MAXIMIZE
                elif window.get("controls_state") == win32con.SW_MINIMIZE:
                    state = win32con.SW_MINIMIZE

                logger.debug(f"Restoring window '{window.get('title')}' to position {position}, size {dimensions}, state {state}")

                # Restore the window
                if self.restore_window(hwnd, position, dimensions, state):
                    restored_count += 1
                    logger.debug(f"Successfully restored window '{window.get('title')}'")
                else:
                    logger.warning(f"Failed to restore window '{window.get('title')}'")
            except Exception as e:
                logger.error(f"Error processing window '{window.get('title')}': {e}")
                continue

        logger.info(f"Successfully restored {restored_count} out of {len(windows)} windows")
        return restored_count

    def restore_window(self, hwnd: int, position: Tuple[int, int], size: Tuple[int, int], state: int = win32con.SW_SHOWNORMAL) -> bool:
        """Restore a window to the specified position, size and state

        Args:
            hwnd: Window handle
            position: (x, y) position tuple
            size: (width, height) size tuple
            state: Window state (minimized, maximized, normal)

        Returns:
            bool: True if successful, False otherwise
        """
        if not win32gui.IsWindow(hwnd):
            logger.warning(f"Window {hwnd} no longer exists, cannot restore")
            return False

        try:
            # Set window position and size
            logger.debug(f"Restoring window {hwnd} to position {position} and size {size}")

            # First make sure the window is not minimized
            if win32gui.IsIconic(hwnd):
                logger.debug(f"Window {hwnd} is minimized, restoring first")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)

            # Position and size the window
            try:
                x, y = position
                width, height = size

                # Ensure coordinates are reasonable
                if x < -8000 or x > 8000 or y < -8000 or y > 8000:
                    logger.warning(f"Window position {position} seems invalid, using default (0,0)")
                    x, y = 0, 0

                if width <= 0 or width > 8000 or height <= 0 or height > 8000:
                    logger.warning(f"Window size {size} seems invalid, using default (800,600)")
                    width, height = 800, 600

                win32gui.MoveWindow(hwnd, x, y, width, height, True)
                logger.debug(f"Window {hwnd} positioned at {(x, y)} with size {(width, height)}")
            except Exception as e:
                logger.error(f"Error positioning window {hwnd}: {e}")

            # Set the window state (minimized, maximized, normal)
            try:
                win32gui.ShowWindow(hwnd, state)
                logger.debug(f"Window {hwnd} state set to {state}")
            except Exception as e:
                logger.error(f"Error setting window {hwnd} state: {e}")

            return True
        except Exception as e:
            logger.error(f"Failed to restore window {hwnd}: {e}")
            return False


# Example usage
if __name__ == "__main__":
    import argparse

    # Configure argument parser
    parser = argparse.ArgumentParser(description="Window Layout Manager")
    parser.add_argument("--save", type=str, help="Save current window layout to specified file")
    parser.add_argument("--apply", type=str, help="Apply window layout from specified file")
    parser.add_argument("--list", action="store_true", help="List all saved layouts")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set debug level if requested
    if args.debug:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
        logger.debug("Debug logging enabled")

    # Create window layout manager
    layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../layouts")
    os.makedirs(layout_dir, exist_ok=True)

    layout_manager = WindowLayout(layouts_dir=layout_dir)

    if args.save:
        # Save current window layout
        logger.info(f"Saving current window layout to {args.save}")
        windows = layout_manager.capture_windows()
        layout_manager.save_to_file(args.save, include_all=True)
        logger.info(f"Saved layout with {len(windows)} windows to {args.save}")

    elif args.apply:
        # Apply a saved window layout
        if os.path.exists(args.apply):
            layout_file = args.apply
        else:
            # Try to find in layouts directory
            layout_file = os.path.join(layout_dir, args.apply)
            if not os.path.exists(layout_file) and not layout_file.endswith(".json"):
                layout_file = f"{layout_file}.json"

        if os.path.exists(layout_file):
            logger.info(f"Applying window layout from {layout_file}")
            restored = layout_manager.apply_layout(layout_file)
            logger.info(f"Restored {restored} windows")
        else:
            logger.error(f"Layout file not found: {args.apply}")

    elif args.list:
        # List all saved layouts
        # Try both the local layouts directory and the parent layouts directory
        local_layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "layouts")
        parent_layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../layouts")

        all_layout_files = []

        # Get files from local layouts directory
        if os.path.exists(local_layout_dir):
            local_files = [os.path.join(local_layout_dir, f) for f in os.listdir(local_layout_dir) if f.endswith(".json")]
            all_layout_files.extend(local_files)

        # Get files from parent layouts directory
        if os.path.exists(parent_layout_dir):
            parent_files = [os.path.join(parent_layout_dir, f) for f in os.listdir(parent_layout_dir) if f.endswith(".json")]
            all_layout_files.extend(parent_files)

        if not all_layout_files:
            print("No saved layouts found")
        else:
            print("Saved layouts:")
            for i, layout_path in enumerate(all_layout_files):
                # Try to get window count
                try:
                    layout_data = WindowLayout.load_from_file(layout_path)
                    window_count = len(layout_data.get("windows", []))

                    # Get creation date
                    timestamp = os.path.getmtime(layout_path)
                    date_str = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

                    # Get relative path for display
                    layout_file = os.path.basename(layout_path)
                    folder = os.path.basename(os.path.dirname(layout_path))

                    print(f"{i+1}. {folder}/{layout_file} - {window_count} windows, created {date_str}")
                except Exception as e:
                    layout_file = os.path.basename(layout_path)
                    print(f"{i+1}. {layout_file} - Error: {e}")
    else:
        # Interactive mode
        print("Window Layout Manager")
        print("1. Save current layout")
        print("2. Apply saved layout")
        print("3. List saved layouts")
        print("4. Exit")

        choice = input("Enter your choice (1-4): ")

        if choice == "1":
            layout_name = input("Enter layout name: ")
            if not layout_name.endswith(".json"):
                layout_name = f"{layout_name}.json"

            layout_path = os.path.join(layout_dir, layout_name)
            windows = layout_manager.capture_windows()
            layout_manager.save_to_file(layout_path)
            print(f"Saved layout with {len(windows)} windows to {layout_path}")

        elif choice == "2":
            # Try both the local layouts directory and the parent layouts directory
            local_layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "layouts")
            parent_layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../layouts")

            all_layout_files = []

            # Get files from local layouts directory
            if os.path.exists(local_layout_dir):
                local_files = [os.path.join(local_layout_dir, f) for f in os.listdir(local_layout_dir) if f.endswith(".json")]
                all_layout_files.extend(local_files)

            # Get files from parent layouts directory
            if os.path.exists(parent_layout_dir):
                parent_files = [os.path.join(parent_layout_dir, f) for f in os.listdir(parent_layout_dir) if f.endswith(".json")]
                all_layout_files.extend(parent_files)

            if not all_layout_files:
                print("No saved layouts found")
            else:
                print("Saved layouts:")
                for i, layout_path in enumerate(all_layout_files):
                    # Get relative path for display
                    layout_file = os.path.basename(layout_path)
                    folder = os.path.basename(os.path.dirname(layout_path))
                    print(f"{i+1}. {folder}/{layout_file}")

                index = int(input("Enter layout number to apply: ")) - 1
                if 0 <= index < len(all_layout_files):
                    layout_path = all_layout_files[index]
                    restored = layout_manager.apply_layout(layout_path)
                    print(f"Restored {restored} windows")
                else:
                    print("Invalid layout number")

        elif choice == "3":
            # Try both the local layouts directory and the parent layouts directory
            local_layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "layouts")
            parent_layout_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../layouts")

            all_layout_files = []

            # Get files from local layouts directory
            if os.path.exists(local_layout_dir):
                local_files = [os.path.join(local_layout_dir, f) for f in os.listdir(local_layout_dir) if f.endswith(".json")]
                all_layout_files.extend(local_files)

            # Get files from parent layouts directory
            if os.path.exists(parent_layout_dir):
                parent_files = [os.path.join(parent_layout_dir, f) for f in os.listdir(parent_layout_dir) if f.endswith(".json")]
                all_layout_files.extend(parent_files)

            if not all_layout_files:
                print("No saved layouts found")
            else:
                print("Saved layouts:")
                for i, layout_path in enumerate(all_layout_files):
                    # Try to get window count
                    try:
                        layout_data = WindowLayout.load_from_file(layout_path)
                        window_count = len(layout_data.get("windows", []))

                        # Get creation date
                        timestamp = os.path.getmtime(layout_path)
                        date_str = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

                        # Get relative path for display
                        layout_file = os.path.basename(layout_path)
                        folder = os.path.basename(os.path.dirname(layout_path))

                        print(f"{i+1}. {folder}/{layout_file} - {window_count} windows, created {date_str}")
                    except Exception as e:
                        layout_file = os.path.basename(layout_path)
                        print(f"{i+1}. {layout_file} - Error: {e}")

        elif choice == "4":
            print("Exiting...")
            sys.exit(0)

        else:
            print("Invalid choice")