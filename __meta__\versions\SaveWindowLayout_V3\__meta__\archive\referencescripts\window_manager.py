"""
window_manager.py - Main window management application

This script provides the main functionality for managing windows, capturing
window layouts, and applying saved layouts. It leverages the window type
definitions and data structures from window_types.py.
"""

# Standard imports
import os
import sys
import time
import json
import argparse
from typing import Dict, List, Optional, Any, Tuple, Union

# Setup loguru for logging
from loguru import logger

# Configure loguru
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add a stderr handler with INFO level

# Local imports
from window_types import (
    WindowType, WindowState, ExplorerViewMode,
    BaseWindowData, ExplorerWindowData, BrowserWindowData,
    WindowCollection, WindowDataFactory
)


class WindowManager:
    """Main class for window management operations"""

    def __init__(self, layouts_dir: str = ""):
        """Initialize the window manager

        Args:
            layouts_dir: Directory to store layout files
        """
        self.collection = WindowCollection()
        self.layouts_dir = layouts_dir or os.path.join(os.path.dirname(os.path.abspath(__file__)), "layouts")
        os.makedirs(self.layouts_dir, exist_ok=True)

    def refresh_windows(self, visible_only: bool = True) -> None:
        """Refresh the collection of windows

        Args:
            visible_only: Only include visible windows
        """
        try:
            logger.debug("Starting window collection refresh")
            self.collection.collect_windows(visible_only=visible_only)
            logger.info(f"Collected {len(self.collection.windows)} windows")
        except Exception as e:
            logger.error(f"Error refreshing windows: {e}")

    def get_windows_by_type(self, window_type: WindowType) -> Dict[int, BaseWindowData]:
        """Get windows of a specific type

        Args:
            window_type: Type of windows to retrieve

        Returns:
            Dictionary of window handles to window data
        """
        return self.collection.get_windows_by_type(window_type)

    def get_active_window(self) -> Optional[BaseWindowData]:
        """Get the currently active window

        Returns:
            Active window data or None if not found
        """
        return self.collection.get_active_window()

    def save_layout(self, name: str = "", include_all: bool = True) -> str:
        """Save the current window layout

        Args:
            name: Optional name for the layout
            include_all: Whether to include all window types

        Returns:
            Path to the saved layout file
        """
        if not name:
            name = f"Layout_{time.strftime('%Y%m%d_%H%M%S')}"

        # Make sure we have fresh window data
        self.refresh_windows()

        # Filter windows if needed
        if not include_all:
            # Exclude system windows and certain types
            exclude_types = [WindowType.SYSTEM, WindowType.UNKNOWN]
            self.collection.windows = {
                hwnd: data for hwnd, data in self.collection.windows.items()
                if data.window_type not in exclude_types
            }

        # Save to file
        filename = os.path.join(self.layouts_dir, f"{name}.layout.json")
        self.collection.to_json(filename)

        logger.info(f"Saved layout to {filename}")
        return filename

    def load_layout(self, filename: str) -> Optional[WindowCollection]:
        """Load a layout from file

        Args:
            filename: Name of the layout file

        Returns:
            WindowCollection object with the loaded layout
        """
        if not os.path.isabs(filename):
            filename = os.path.join(self.layouts_dir, filename)

        if not os.path.exists(filename):
            logger.error(f"Layout file does not exist: {filename}")
            return None

        try:
            collection = WindowCollection.from_file(filename)
            logger.info(f"Loaded layout from {filename} with {len(collection.windows)} windows")
            return collection
        except Exception as e:
            logger.error(f"Error loading layout: {e}")
            return None

    def get_available_layouts(self) -> List[str]:
        """Get list of available layout files

        Returns:
            List of layout filenames
        """
        layouts = []
        for file in os.listdir(self.layouts_dir):
            if file.endswith(".layout.json"):
                layouts.append(file)

        return layouts

    def apply_layout(self, layout: Union[str, WindowCollection],
                     restore_explorer: bool = True,
                     restore_browsers: bool = True,
                     restore_applications: bool = True) -> bool:
        """Apply a window layout

        Args:
            layout: Layout file name or WindowCollection object
            restore_explorer: Whether to restore Explorer windows
            restore_browsers: Whether to restore browser windows
            restore_applications: Whether to restore application windows

        Returns:
            True if successful, False otherwise
        """
        # Load layout if string is provided
        if isinstance(layout, str):
            loaded_layout = self.load_layout(layout)
            if not loaded_layout:
                return False
            layout = loaded_layout

        # Get current windows to avoid duplicates
        self.refresh_windows()
        current_windows = self.collection

        # Track success
        success = True

        # Apply each window in the layout
        for hwnd, window_data in layout.windows.items():
            try:
                # Skip if we shouldn't restore this type
                if (window_data.window_type == WindowType.EXPLORER and not restore_explorer or
                    window_data.window_type == WindowType.BROWSER and not restore_browsers or
                    window_data.window_type not in [WindowType.EXPLORER, WindowType.BROWSER]
                    and not restore_applications):
                    continue

                # Skip system windows
                if window_data.window_type == WindowType.SYSTEM:
                    continue

                # Apply the window position and state
                if self._apply_window_data(window_data):
                    logger.info(f"Applied layout for window: {window_data.title}")
                else:
                    logger.warning(f"Failed to apply layout for window: {window_data.title}")
                    success = False
            except Exception as e:
                logger.error(f"Error applying layout for window {window_data.title}: {e}")
                success = False

        return success

    def _apply_window_data(self, window_data: BaseWindowData) -> bool:
        """Apply window data to a window

        Args:
            window_data: Window data to apply

        Returns:
            True if successful, False otherwise
        """
        # Try to find an existing window
        existing_window = self._find_matching_window(window_data)

        # If found, apply position and state
        if existing_window:
            return self._apply_to_existing_window(existing_window, window_data)
        else:
            # Otherwise, try to create a new window
            return self._create_new_window(window_data)

    def _find_matching_window(self, window_data: BaseWindowData) -> Optional[int]:
        """Find an existing window matching the layout data

        Args:
            window_data: Window data to match

        Returns:
            Window handle if found, None otherwise
        """
        # First try to find by exact handle match
        if window_data.hwnd in self.collection.windows:
            return window_data.hwnd

        # For Explorer windows, match by path
        if (window_data.window_type == WindowType.EXPLORER and
            isinstance(window_data, ExplorerWindowData) and
            window_data.location_path):

            explorer_windows = self.get_windows_by_type(WindowType.EXPLORER)
            for hwnd, data in explorer_windows.items():
                if (isinstance(data, ExplorerWindowData) and
                    data.location_path == window_data.location_path):
                    return hwnd

        # For browser windows, match by URL
        if (window_data.window_type == WindowType.BROWSER and
            isinstance(window_data, BrowserWindowData) and
            window_data.url):

            browser_windows = self.get_windows_by_type(WindowType.BROWSER)
            for hwnd, data in browser_windows.items():
                if (isinstance(data, BrowserWindowData) and
                    data.url == window_data.url):
                    return hwnd

        # For other windows, match by title and class
        for hwnd, data in self.collection.windows.items():
            if (data.window_type == window_data.window_type and
                data.class_name == window_data.class_name and
                data.title == window_data.title):
                return hwnd

        return None

    def _apply_to_existing_window(self, hwnd: int, window_data: BaseWindowData) -> bool:
        """Apply window data to an existing window

        Args:
            hwnd: Handle of existing window
            window_data: Window data to apply

        Returns:
            True if successful, False otherwise
        """
        import win32gui
        import win32con

        try:
            # Apply position and size
            if window_data.state == WindowState.NORMAL:
                # Normal window - set position and size
                win32gui.SetWindowPos(
                    hwnd,
                    0,  # No z-order change
                    window_data.position.x,
                    window_data.position.y,
                    window_data.size.width,
                    window_data.size.height,
                    win32con.SWP_NOACTIVATE
                )
            elif window_data.state == WindowState.MAXIMIZED:
                # Maximized window
                win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
            elif window_data.state == WindowState.MINIMIZED:
                # Minimized window
                win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)

            # Set always on top if needed
            if window_data.always_on_top:
                win32gui.SetWindowPos(
                    hwnd,
                    win32con.HWND_TOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
                )

            # Handle Explorer-specific settings
            if (window_data.window_type == WindowType.EXPLORER and
                isinstance(window_data, ExplorerWindowData) and
                isinstance(self.collection.windows.get(hwnd), ExplorerWindowData)):

                self._apply_explorer_settings(hwnd, window_data)

            return True
        except Exception as e:
            logger.error(f"Error applying to existing window: {e}")
            return False

    def _create_new_window(self, window_data: BaseWindowData) -> bool:
        """Create a new window based on the layout data

        Args:
            window_data: Window data to apply

        Returns:
            True if successful, False otherwise
        """
        # Only Explorer and Browser windows can be created
        if window_data.window_type == WindowType.EXPLORER:
            return self._create_explorer_window(window_data)
        elif window_data.window_type == WindowType.BROWSER:
            return self._create_browser_window(window_data)
        else:
            logger.warning(f"Cannot create window of type {window_data.window_type.name}")
            return False

    def _create_explorer_window(self, window_data: ExplorerWindowData) -> bool:
        """Create a new Explorer window

        Args:
            window_data: Explorer window data

        Returns:
            True if successful, False otherwise
        """
        import subprocess

        try:
            # Use the create command if available
            if window_data.create_command:
                subprocess.Popen(window_data.create_command, shell=True)
                return True

            # Otherwise use the location path
            elif window_data.location_path:
                if window_data.is_special_folder:
                    command = f"explorer shell:{window_data.location_path}"
                else:
                    command = f"explorer \"{window_data.location_path}\""
                subprocess.Popen(command, shell=True)
                return True

            else:
                logger.warning("Cannot create Explorer window without path or command")
                return False
        except Exception as e:
            logger.error(f"Error creating Explorer window: {e}")
            return False

    def _create_browser_window(self, window_data: BrowserWindowData) -> bool:
        """Create a new browser window

        Args:
            window_data: Browser window data

        Returns:
            True if successful, False otherwise
        """
        import subprocess

        try:
            # Use the create command if available
            if window_data.create_command:
                subprocess.Popen(window_data.create_command, shell=True)
                return True

            # Otherwise use the URL
            elif window_data.url and window_data.browser_type:
                browser_cmd = None
                if window_data.browser_type == "chrome":
                    browser_cmd = "chrome"
                elif window_data.browser_type == "firefox":
                    browser_cmd = "firefox"
                elif window_data.browser_type == "edge":
                    browser_cmd = "msedge"

                if browser_cmd:
                    command = f"{browser_cmd} \"{window_data.url}\""
                    subprocess.Popen(command, shell=True)
                    return True

            logger.warning("Cannot create browser window without URL or command")
            return False
        except Exception as e:
            logger.error(f"Error creating browser window: {e}")
            return False

    def _apply_explorer_settings(self, hwnd: int, window_data: ExplorerWindowData) -> bool:
        """Apply Explorer-specific settings

        Args:
            hwnd: Window handle
            window_data: Explorer window data

        Returns:
            True if successful, False otherwise
        """
        import win32com.client

        try:
            # Get Shell.Application and find the window
            shell_app = win32com.client.Dispatch("Shell.Application")
            shell_windows = shell_app.Windows()

            for i in range(shell_windows.Count):
                window = shell_windows.Item(i)
                if window.HWND == hwnd:
                    # Set view mode if specified
                    if hasattr(window, 'Document') and hasattr(window.Document, 'CurrentViewMode'):
                        window.Document.CurrentViewMode = window_data.view_mode.value

                    # Set icon size if specified
                    if (window_data.icon_size and
                        hasattr(window, 'Document') and
                        hasattr(window.Document, 'IconSize')):
                        window.Document.IconSize = window_data.icon_size

                    # Select files if specified
                    if (window_data.selected_files and
                        hasattr(window, 'Document') and
                        hasattr(window.Document, 'Folder') and
                        hasattr(window.Document.Folder, 'Items')):

                        for item in window.Document.Folder.Items():
                            if item.Name in window_data.selected_files:
                                window.Document.SelectItem(item, 1)  # 1 = select

                    return True

            return False
        except Exception as e:
            logger.error(f"Error applying Explorer settings: {e}")
            return False


def display_window_info(window_data: BaseWindowData) -> None:
    """Display information about a window

    Args:
        window_data: Window data to display
    """
    print(f"\nWindow Information")
    print(f"=================")
    print(f"Title: {window_data.title}")
    print(f"Type: {window_data.window_type.name}")
    print(f"HWND: {window_data.hwnd}")
    print(f"Class: {window_data.class_name}")
    print(f"State: {window_data.state.name}")
    print(f"Position: {window_data.position}")
    print(f"Size: {window_data.size}")
    print(f"Process: {window_data.process.path}")

    if window_data.monitor:
        print(f"Monitor: {window_data.monitor.device}")
        print(f"  Primary: {'Yes' if window_data.monitor.is_primary else 'No'}")

    if window_data.window_type == WindowType.EXPLORER and isinstance(window_data, ExplorerWindowData):
        print(f"\nExplorer Information")
        print(f"===================")
        print(f"Path: {window_data.location_path}")
        print(f"URL: {window_data.location_url}")
        print(f"View Mode: {window_data.view_mode.name}")
        if window_data.selected_files:
            print(f"Selected Files: {', '.join(window_data.selected_files)}")
        if window_data.focused_file:
            print(f"Focused File: {window_data.focused_file}")
        print(f"Special Folder: {'Yes' if window_data.is_special_folder else 'No'}")

    elif window_data.window_type == WindowType.BROWSER and isinstance(window_data, BrowserWindowData):
        print(f"\nBrowser Information")
        print(f"==================")
        print(f"URL: {window_data.url}")
        print(f"Browser Type: {window_data.browser_type}")
        if window_data.tab_count:
            print(f"Tab Count: {window_data.tab_count}")


def monitor_active_window() -> None:
    """Monitor and display information about the active window"""
    manager = WindowManager()

    print("Monitoring active window. Press Ctrl+C to exit.")
    print("Changes will be logged when you switch windows.\n")

    last_hwnd = None

    try:
        while True:
            # Get active window
            manager.refresh_windows()
            active_window = manager.get_active_window()

            if active_window and active_window.hwnd != last_hwnd:
                # Display info for new active window
                display_window_info(active_window)
                last_hwnd = active_window.hwnd

            time.sleep(0.5)
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments

    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Window Management Utility")

    # General options
    parser.add_argument("--monitor", action="store_true",
                      help="Monitor active window")
    parser.add_argument("--debug", action="store_true",
                      help="Enable debug logging")

    # Window listing options
    list_group = parser.add_argument_group("Window Listing")
    list_group.add_argument("--list-all", action="store_true",
                          help="List all windows")
    list_group.add_argument("--list-explorer", action="store_true",
                          help="List Explorer windows")
    list_group.add_argument("--list-browsers", action="store_true",
                          help="List browser windows")

    # Layout options
    layout_group = parser.add_argument_group("Layout Management")
    layout_group.add_argument("--save-layout", metavar="NAME",
                            help="Save current layout with optional name")
    layout_group.add_argument("--list-layouts", action="store_true",
                            help="List available layouts")
    layout_group.add_argument("--apply-layout", metavar="NAME",
                            help="Apply a saved layout")

    return parser.parse_args()


def list_windows(manager: WindowManager, window_type: Optional[WindowType] = None) -> None:
    """List windows of the specified type

    Args:
        manager: WindowManager instance
        window_type: Type of windows to list, or None for all
    """
    manager.refresh_windows()

    if window_type:
        windows = manager.get_windows_by_type(window_type)
        print(f"\n{window_type.name} Windows:")
    else:
        windows = manager.collection.windows
        print("\nAll Windows:")

    print(f"Found {len(windows)} windows")

    if not windows:
        return

    print("\n{:<10} {:<40} {:<20} {:<20}".format(
        "HWND", "Title", "Position", "Size"))
    print("-" * 90)

    for hwnd, data in windows.items():
        title = data.title[:37] + "..." if len(data.title) > 37 else data.title
        position = str(data.position)
        size = str(data.size)

        print("{:<10} {:<40} {:<20} {:<20}".format(
            hwnd, title, position, size))


def main() -> None:
    """Main function"""
    args = parse_arguments()

    # Set up logging level
    if args.debug:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")

    # Create window manager
    manager = WindowManager()

    # Handle arguments
    if args.monitor:
        monitor_active_window()
    elif args.list_all:
        list_windows(manager)
    elif args.list_explorer:
        list_windows(manager, WindowType.EXPLORER)
    elif args.list_browsers:
        list_windows(manager, WindowType.BROWSER)
    elif args.save_layout:
        filename = manager.save_layout(args.save_layout)
        print(f"Layout saved to: {filename}")
    elif args.list_layouts:
        layouts = manager.get_available_layouts()
        if layouts:
            print("\nAvailable layouts:")
            for i, layout in enumerate(layouts):
                print(f"{i+1}. {layout}")
        else:
            print("No layouts found")
    elif args.apply_layout:
        success = manager.apply_layout(args.apply_layout)
        if success:
            print(f"Layout applied successfully")
        else:
            print(f"Failed to apply layout")
    else:
        # Default action: show active window
        manager.refresh_windows()
        active_window = manager.get_active_window()
        if active_window:
            display_window_info(active_window)
        else:
            print("No active window found")


if __name__ == "__main__":
    main()
