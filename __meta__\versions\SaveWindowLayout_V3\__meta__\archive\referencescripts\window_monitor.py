"""
window_monitor.py - Window monitoring and display functionality

This module provides classes and functions for monitoring window changes
and displaying window information in a formatted way.
"""

# Standard imports
import os
import sys
import time
import random
import threading

# Win32 API imports
import win32gui
import win32api
import win32con

# Terminal color formatting
from ansimarkup import ansiprint

# Import our window classes
from window_base import Window, get_active_window
from window_explorer import ExplorerWindow

# Colors to avoid (too dark to read)
DARK_COLORS = [0] + list(range(16, 22)) + list(range(52, 55)) + \
              list(range(58, 64)) + list(range(232, 241))


def format_window_info(window, cmd_hwnd=None, color=None):
    """Format window information for display

    Args:
        window: Window object to display
        cmd_hwnd: Handle to the command window (for separator width calculation)
        color: Color to use for display (if None, a random color will be chosen)

    Returns:
        List of formatted strings
    """
    # Enable ANSI escapes
    os.system('color')

    # Select a random color if not provided
    if color is None:
        color = random.randint(15, 255)
        while color in DARK_COLORS:
            color = random.randint(15, 255)

    # Create color print function
    def color_print(text):
        return f"<fg {color}>{text}</fg {color}>"

    # Get window data
    window_data = window.to_dict()

    # Calculate separator width based on terminal width
    if cmd_hwnd:
        cmd_rect = win32gui.GetWindowRect(cmd_hwnd)
        line_width = int((cmd_rect[2] - cmd_rect[0]) * 0.1)
    else:
        line_width = 100

    # Format keys and values
    keys = [str(k) for k in window_data.keys()]
    values = [str(v) for v in window_data.values()]
    max_key_length = max(len(key) for key in keys)

    # Build formatted strings
    lines = []

    # Add separator
    lines.append(color_print('-' * line_width))

    # Add data lines
    for key, value in zip(keys, values):
        lines.append(color_print(key.rjust(max_key_length + 20) + " : " + value))

    return lines


def print_window_info(window, cmd_hwnd=None):
    """Print formatted window information with color"""
    lines = format_window_info(window, cmd_hwnd)
    for line in lines:
        ansiprint(line)


def set_terminal_title(window):
    """Set the terminal title based on window information"""
    os.system(f'TITLE "{window.monitor}   |   Size:{window.size}   |   Pos:{window.position}"')


class WindowMonitor:
    """Class for monitoring window changes"""

    def __init__(self, callback=None, interval=0.15, monitor_explorer=False):
        """Initialize the window monitor

        Args:
            callback: Function to call when window changes (defaults to printing info)
            interval: Polling interval in seconds
            monitor_explorer: Whether to monitor Explorer-specific properties
        """
        self.callback = callback or self.default_callback
        self.interval = interval
        self.monitor_explorer = monitor_explorer
        self.running = False
        self.thread = None
        self.previous_window = None
        self.cmd_hwnd = win32gui.GetForegroundWindow()

        # Resize terminal window for better visibility
        win32gui.MoveWindow(self.cmd_hwnd, 160, 120, 1600, 720, True)

    def start(self):
        """Start monitoring window changes"""
        if self.running:
            return

        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop)
        self.thread.daemon = True
        self.thread.start()

        print("Monitoring active windows. Press Ctrl+C to exit...\n")

    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Get current window
                current_window = ExplorerWindow() if self.monitor_explorer else get_active_window()

                # Check if window changed
                if (self.previous_window is None or
                    current_window.hwnd != self.previous_window.hwnd or
                    current_window.position != self.previous_window.position or
                    current_window.size != self.previous_window.size):

                    # Call the callback function
                    self.callback(current_window, self.previous_window)

                    # Update previous window
                    self.previous_window = current_window

                # Wait before checking again
                time.sleep(self.interval)
            except Exception as e:
                print(f"Error in monitor loop: {e}")

    def stop(self):
        """Stop monitoring window changes"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1.0)

    def default_callback(self, window, previous_window):
        """Default callback function to print window info"""
        # Update terminal title with window info
        set_terminal_title(window)

        # Print the window information
        print_window_info(window, self.cmd_hwnd)


def resize_terminal(width=1600, height=720, x=160, y=120):
    """Resize the terminal window"""
    cmd_hwnd = win32gui.GetForegroundWindow()
    win32gui.MoveWindow(cmd_hwnd, x, y, width, height, True)


def main():
    """Main function"""
    try:
        # Resize terminal window
        resize_terminal()

        # Set terminal title
        os.system('TITLE "Window Information Monitor"')

        # Create and start window monitor
        monitor = WindowMonitor(monitor_explorer=True)
        monitor.start()

        # Keep main thread alive
        while True:
            time.sleep(0.5)

    except KeyboardInterrupt:
        print("\nMonitoring stopped.")
    finally:
        if 'monitor' in locals():
            monitor.stop()


if __name__ == "__main__":
    main()