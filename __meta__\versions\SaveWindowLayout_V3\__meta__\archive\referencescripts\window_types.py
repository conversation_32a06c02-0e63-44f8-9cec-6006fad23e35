"""
window_types.py - Comprehensive window type classification and data structures

This module defines a hierarchy of window types and data structures for storing
window information consistently across the application. It serves as the foundation
for all window management operations.
"""

# Standard imports
import os
import sys
import re
import time
import urllib.parse
import json
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple, Union, Set
from dataclasses import dataclass, field, asdict

# Win32 API imports
import win32api
import win32gui
import win32con
import win32process
import win32com.client
import pywintypes
from win32com.shell import shell, shellcon

# Setup loguru for logging
from loguru import logger

# Configure loguru
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add a stderr handler with INFO level

# ----------------------------------------
# Enumerations for window classification
# ----------------------------------------

class WindowType(Enum):
    """Enumeration of window types for classification"""
    UNKNOWN = auto()
    SYSTEM = auto()        # System windows like taskbar, desktop
    EXPLORER = auto()      # File Explorer windows
    BROWSER = auto()       # Web browsers
    OFFICE = auto()        # Office applications (Word, Excel, etc.)
    MEDIA = auto()         # Media players and viewers
    UTILITY = auto()       # Utilities like calculators, notepads
    DEVELOPMENT = auto()   # Development tools like IDEs
    GAME = auto()          # Games
    COMMUNICATION = auto() # Communication tools like email, chat
    VIRTUAL_DESKTOP = auto() # Virtual desktop applications


class WindowState(Enum):
    """Enumeration of window states"""
    NORMAL = 1      # SW_SHOWNORMAL
    MINIMIZED = 2   # SW_SHOWMINIMIZED
    MAXIMIZED = 3   # SW_SHOWMAXIMIZED
    HIDDEN = 0      # SW_HIDE
    INACTIVE = 4    # SW_SHOWNOACTIVATE
    RESTORED = 9    # SW_RESTORE


class ExplorerViewMode(Enum):
    """Enumeration of Explorer view modes"""
    AUTO = -1
    ICONS = 1
    SMALL_ICONS = 2
    LIST = 3
    DETAILS = 4
    THUMBNAILS = 5
    TILES = 6
    CONTENT = 7
    FILMSTRIP = 8


# ----------------------------------------
# Data classes for window information
# ----------------------------------------

@dataclass
class Position:
    """Represents a window position"""
    x: int = 0
    y: int = 0

    def __str__(self) -> str:
        return f"({self.x}, {self.y})"

    @classmethod
    def from_tuple(cls, pos_tuple: Tuple[int, int]) -> 'Position':
        """Create from a tuple of (x, y)"""
        return cls(pos_tuple[0], pos_tuple[1])

    def to_tuple(self) -> Tuple[int, int]:
        """Convert to tuple"""
        return (self.x, self.y)


@dataclass
class Size:
    """Represents window dimensions"""
    width: int = 0
    height: int = 0

    def __str__(self) -> str:
        return f"{self.width}x{self.height}"

    @classmethod
    def from_tuple(cls, size_tuple: Tuple[int, int]) -> 'Size':
        """Create from a tuple of (width, height)"""
        return cls(size_tuple[0], size_tuple[1])

    def to_tuple(self) -> Tuple[int, int]:
        """Convert to tuple"""
        return (self.width, self.height)


@dataclass
class Rect:
    """Represents a window rectangle"""
    left: int = 0
    top: int = 0
    right: int = 0
    bottom: int = 0

    @property
    def width(self) -> int:
        """Width of the rectangle"""
        return self.right - self.left

    @property
    def height(self) -> int:
        """Height of the rectangle"""
        return self.bottom - self.top

    @property
    def position(self) -> Position:
        """Top-left position"""
        return Position(self.left, self.top)

    @property
    def size(self) -> Size:
        """Size of the rectangle"""
        return Size(self.width, self.height)

    @classmethod
    def from_tuple(cls, rect_tuple: Tuple[int, int, int, int]) -> 'Rect':
        """Create from a tuple of (left, top, right, bottom)"""
        return cls(rect_tuple[0], rect_tuple[1], rect_tuple[2], rect_tuple[3])

    def to_tuple(self) -> Tuple[int, int, int, int]:
        """Convert to tuple"""
        return (self.left, self.top, self.right, self.bottom)


@dataclass
class Monitor:
    """Information about a monitor/display"""
    handle: int = 0
    device: str = ""
    is_primary: bool = False
    work_area: Rect = field(default_factory=Rect)  # Excludes taskbar
    display_area: Rect = field(default_factory=Rect)  # Full monitor area

    @classmethod
    def from_handle(cls, monitor_handle: int) -> 'Monitor':
        """Create from a monitor handle"""
        try:
            info = win32api.GetMonitorInfo(monitor_handle)
            return cls(
                handle=monitor_handle,
                device=info["Device"],
                is_primary=bool(info["Flags"] & win32con.MONITORINFOF_PRIMARY),
                work_area=Rect.from_tuple(info["Work"]),
                display_area=Rect.from_tuple(info["Monitor"])
            )
        except Exception as e:
            logger.error(f"Error getting monitor info: {e}")
            return cls(handle=monitor_handle)


@dataclass
class ProcessInfo:
    """Information about a process"""
    id: int = 0
    path: str = ""
    name: str = ""
    command_line: str = ""

    @property
    def exe_name(self) -> str:
        """Get the executable name without path"""
        return os.path.basename(self.path) if self.path else ""

    @classmethod
    def from_pid(cls, pid: int) -> 'ProcessInfo':
        """Create from a process ID"""
        if not pid:
            return cls()

        try:
            # Open process to get executable path
            process_handle = win32api.OpenProcess(
                win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ,
                False, pid
            )
            if process_handle:
                try:
                    path = win32process.GetModuleFileNameEx(process_handle, 0)
                    name = os.path.basename(path)
                    return cls(id=pid, path=path, name=name)
                finally:
                    win32api.CloseHandle(process_handle)
        except Exception as e:
            logger.debug(f"Error getting process info for PID {pid}: {e}")

        return cls(id=pid)


@dataclass
class BaseWindowData:
    """Base data structure for window information"""
    hwnd: int = 0
    title: str = ""
    class_name: str = ""
    window_type: WindowType = WindowType.UNKNOWN
    state: WindowState = WindowState.NORMAL
    position: Position = field(default_factory=Position)
    size: Size = field(default_factory=Size)
    process: ProcessInfo = field(default_factory=ProcessInfo)
    monitor: Optional[Monitor] = None
    visible: bool = True
    always_on_top: bool = False
    z_order: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        result = asdict(self)
        # Convert enum values to strings for better readability in JSON
        result["window_type"] = self.window_type.name
        result["state"] = self.state.name
        # Convert complex objects that asdict doesn't handle well
        if self.monitor:
            result["monitor"] = {
                "device": self.monitor.device,
                "is_primary": self.monitor.is_primary,
                "work_area": self.monitor.work_area.to_tuple(),
                "display_area": self.monitor.display_area.to_tuple()
            }
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseWindowData':
        """Create from a dictionary"""
        # Convert string enum values back to enum types
        if "window_type" in data and isinstance(data["window_type"], str):
            data["window_type"] = WindowType[data["window_type"]]
        if "state" in data and isinstance(data["state"], str):
            data["state"] = WindowState[data["state"]]

        # Handle complex nested objects
        if "monitor" in data and data["monitor"]:
            monitor_data = data.pop("monitor")
            data["monitor"] = Monitor(
                device=monitor_data.get("device", ""),
                is_primary=monitor_data.get("is_primary", False),
                work_area=Rect.from_tuple(monitor_data.get("work_area", (0, 0, 0, 0))),
                display_area=Rect.from_tuple(monitor_data.get("display_area", (0, 0, 0, 0)))
            )

        # Convert position and size if they're in tuple format
        if "position" in data and isinstance(data["position"], (list, tuple)):
            data["position"] = Position.from_tuple(tuple(data["position"]))
        if "size" in data and isinstance(data["size"], (list, tuple)):
            data["size"] = Size.from_tuple(tuple(data["size"]))

        return cls(**data)


@dataclass
class ExplorerWindowData(BaseWindowData):
    """Data structure for Explorer window information"""
    location_path: str = ""
    location_url: str = ""
    view_mode: ExplorerViewMode = ExplorerViewMode.DETAILS
    icon_size: int = 0
    sort_column: str = ""
    group_by: str = ""
    selected_files: List[str] = field(default_factory=list)
    focused_file: Optional[str] = None
    file_count: int = 0
    create_command: str = ""
    is_special_folder: bool = False
    folder_type: str = ""

    def __post_init__(self):
        """Post initialization hooks"""
        # Set window type to EXPLORER
        self.window_type = WindowType.EXPLORER

        # Generate create command if not provided
        if not self.create_command and self.location_path:
            if self.is_special_folder:
                self.create_command = f"explorer shell:{self.location_path}"
            else:
                self.create_command = f"explorer \"{self.location_path}\""

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        result = super().to_dict()
        # Convert Explorer-specific enum values
        result["view_mode"] = self.view_mode.name
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExplorerWindowData':
        """Create from a dictionary"""
        # Convert view mode from string to enum if needed
        if "view_mode" in data and isinstance(data["view_mode"], str):
            data["view_mode"] = ExplorerViewMode[data["view_mode"]]

        # Create instance with base class conversion first
        base_data = BaseWindowData.from_dict(data)

        # Create ExplorerWindowData using the base data attributes
        explorer_data = cls(
            hwnd=base_data.hwnd,
            title=base_data.title,
            class_name=base_data.class_name,
            window_type=WindowType.EXPLORER,  # Override to ensure correct type
            state=base_data.state,
            position=base_data.position,
            size=base_data.size,
            process=base_data.process,
            monitor=base_data.monitor,
            visible=base_data.visible,
            always_on_top=base_data.always_on_top,
            z_order=base_data.z_order
        )

        # Add Explorer-specific attributes from data
        for key in [
            "location_path", "location_url", "icon_size", "sort_column",
            "group_by", "selected_files", "focused_file", "file_count",
            "create_command", "is_special_folder", "folder_type"
        ]:
            if key in data:
                setattr(explorer_data, key, data[key])

        return explorer_data


@dataclass
class BrowserWindowData(BaseWindowData):
    """Data structure for web browser window information"""
    url: str = ""
    browser_type: str = ""  # chrome, firefox, edge, etc.
    tab_count: int = 0
    create_command: str = ""

    def __post_init__(self):
        """Post initialization hooks"""
        # Set window type to BROWSER
        self.window_type = WindowType.BROWSER

        # Generate create command if not provided
        if not self.create_command and self.url:
            if self.browser_type == "chrome":
                self.create_command = f"chrome \"{self.url}\""
            elif self.browser_type == "firefox":
                self.create_command = f"firefox \"{self.url}\""
            elif self.browser_type == "edge":
                self.create_command = f"msedge \"{self.url}\""
            elif self.process.exe_name:
                self.create_command = f"\"{self.process.path}\" \"{self.url}\""


@dataclass
class ApplicationWindowData(BaseWindowData):
    """Data structure for general application window information"""
    app_id: str = ""
    app_version: str = ""
    command_line: str = ""
    is_uwp: bool = False

    def __post_init__(self):
        """Post initialization hooks"""
        # Generate command line if not provided
        if not self.command_line and self.process.path:
            self.command_line = f"\"{self.process.path}\""


# ----------------------------------------
# Window data factory and type detection
# ----------------------------------------

class WindowDataFactory:
    """Factory for creating appropriate window data objects based on window type"""

    # Mapping of class names to window types
    CLASS_TO_TYPE = {
        "CabinetWClass": WindowType.EXPLORER,
        "ExploreWClass": WindowType.EXPLORER,
        "Progman": WindowType.SYSTEM,
        "WorkerW": WindowType.SYSTEM,
        "Shell_TrayWnd": WindowType.SYSTEM,
        "Shell_SecondaryTrayWnd": WindowType.SYSTEM,
        "Chrome_WidgetWin_1": WindowType.BROWSER,
        "MozillaWindowClass": WindowType.BROWSER,
        "IEFrame": WindowType.BROWSER,
        "ApplicationFrameWindow": WindowType.UNKNOWN,  # Need to check process for UWP apps
    }

    # Mapping of executable names to window types
    EXE_TO_TYPE = {
        "explorer.exe": WindowType.SYSTEM,  # Default, will be overridden for Explorer windows
        "chrome.exe": WindowType.BROWSER,
        "firefox.exe": WindowType.BROWSER,
        "msedge.exe": WindowType.BROWSER,
        "brave.exe": WindowType.BROWSER,
        "iexplore.exe": WindowType.BROWSER,
        "winword.exe": WindowType.OFFICE,
        "excel.exe": WindowType.OFFICE,
        "powerpnt.exe": WindowType.OFFICE,
        "outlook.exe": WindowType.OFFICE,
        "onenote.exe": WindowType.OFFICE,
        "code.exe": WindowType.DEVELOPMENT,
        "devenv.exe": WindowType.DEVELOPMENT,
        "pycharm64.exe": WindowType.DEVELOPMENT,
        "notepad.exe": WindowType.UTILITY,
        "mspaint.exe": WindowType.UTILITY,
        "calc.exe": WindowType.UTILITY,
        "mpc-hc64.exe": WindowType.MEDIA,
        "vlc.exe": WindowType.MEDIA,
        "wmplayer.exe": WindowType.MEDIA,
        "slack.exe": WindowType.COMMUNICATION,
        "teams.exe": WindowType.COMMUNICATION,
        "discord.exe": WindowType.COMMUNICATION,
        "skype.exe": WindowType.COMMUNICATION,
    }

    # Browser executable names
    BROWSER_EXES = {
        "chrome.exe": "chrome",
        "firefox.exe": "firefox",
        "msedge.exe": "edge",
        "brave.exe": "brave",
        "iexplore.exe": "ie",
        "opera.exe": "opera"
    }

    @classmethod
    def detect_window_type(cls, hwnd: int, class_name: str, process_path: str) -> WindowType:
        """Detect the window type based on class and process information"""
        # Check class name first (more specific)
        if class_name in cls.CLASS_TO_TYPE:
            return cls.CLASS_TO_TYPE[class_name]

        # Check process executable
        exe_name = os.path.basename(process_path).lower() if process_path else ""
        if exe_name in cls.EXE_TO_TYPE:
            # Special case for explorer.exe
            if exe_name == "explorer.exe" and class_name == "CabinetWClass":
                return WindowType.EXPLORER
            return cls.EXE_TO_TYPE[exe_name]

        # Default to UNKNOWN
        return WindowType.UNKNOWN

    @classmethod
    def create_window_data(cls, hwnd: int) -> Union[BaseWindowData, ExplorerWindowData, BrowserWindowData, ApplicationWindowData]:
        """Create the appropriate window data object for the given window handle"""
        try:
            # Get basic window info
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)

            # Get process info
            try:
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                process_info = ProcessInfo.from_pid(pid)
            except Exception as e:
                logger.debug(f"Error getting process info for hwnd {hwnd}: {e}")
                process_info = ProcessInfo()

            # Get window position and size
            try:
                rect = win32gui.GetWindowRect(hwnd)
                pos = Position(rect[0], rect[1])
                size = Size(rect[2] - rect[0], rect[3] - rect[1])
            except Exception as e:
                logger.debug(f"Error getting window rect for hwnd {hwnd}: {e}")
                pos = Position()
                size = Size()

            # Get window state
            try:
                placement = win32gui.GetWindowPlacement(hwnd)
                if placement[1] == win32con.SW_SHOWMAXIMIZED:
                    state = WindowState.MAXIMIZED
                elif placement[1] == win32con.SW_SHOWMINIMIZED:
                    state = WindowState.MINIMIZED
                else:
                    state = WindowState.NORMAL
            except Exception as e:
                logger.debug(f"Error getting window placement for hwnd {hwnd}: {e}")
                state = WindowState.NORMAL

            # Check visibility
            visible = win32gui.IsWindowVisible(hwnd)

            # Check if always on top
            ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            always_on_top = bool(ex_style & win32con.WS_EX_TOPMOST)

            # Get monitor info
            try:
                monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
                monitor = Monitor.from_handle(monitor_handle)
            except Exception as e:
                logger.debug(f"Error getting monitor info for hwnd {hwnd}: {e}")
                monitor = None

            # Detect window type
            window_type = cls.detect_window_type(hwnd, class_name, process_info.path)

            # Create the appropriate data object based on window type
            if window_type == WindowType.EXPLORER:
                return cls._create_explorer_window_data(
                    hwnd, title, class_name, state, pos, size,
                    process_info, monitor, visible, always_on_top
                )
            elif window_type == WindowType.BROWSER:
                return cls._create_browser_window_data(
                    hwnd, title, class_name, state, pos, size,
                    process_info, monitor, visible, always_on_top
                )
            else:
                # Create a base window data object for other types
                return BaseWindowData(
                    hwnd=hwnd,
                    title=title,
                    class_name=class_name,
                    window_type=window_type,
                    state=state,
                    position=pos,
                    size=size,
                    process=process_info,
                    monitor=monitor,
                    visible=visible,
                    always_on_top=always_on_top
                )

        except Exception as e:
            logger.debug(f"Error creating window data for hwnd {hwnd}: {e}")
            return BaseWindowData(hwnd=hwnd)

    @classmethod
    def _create_explorer_window_data(
        cls, hwnd: int, title: str, class_name: str,
        state: WindowState, pos: Position, size: Size,
        process_info: ProcessInfo, monitor: Optional[Monitor],
        visible: bool, always_on_top: bool
    ) -> ExplorerWindowData:
        """Create Explorer-specific window data"""
        # Initialize default values
        location_path = ""
        location_url = ""
        view_mode = ExplorerViewMode.DETAILS
        selected_files = []
        focused_file = None
        is_special_folder = False
        create_command = ""

        try:
            # Try to get Shell.Application and locate the window
            try:
                shell_app = win32com.client.Dispatch("Shell.Application")
                shell_windows = shell_app.Windows()
            except Exception as com_error:
                # Log at debug level to avoid filling logs with COM errors
                logger.debug(f"COM error with Shell.Application: {com_error}")
                # Skip further COM operations and return basic data
                raise ValueError("Could not access Shell.Application")

            # Find matching window
            try:
                window_found = False
                for i in range(shell_windows.Count):
                    try:
                        window = shell_windows.Item(i)

                        # Make sure HWND property exists
                        if not hasattr(window, 'HWND'):
                            continue

                        if window.HWND == hwnd:
                            window_found = True
                            # Get location info
                            try:
                                if hasattr(window, 'LocationURL') and window.LocationURL:
                                    location_url = window.LocationURL
                                    location_path = urllib.parse.unquote(location_url.replace('file:///', ''))
                            except Exception as e:
                                logger.debug(f"Error getting LocationURL: {e}")

                            # Get view mode
                            try:
                                if hasattr(window, 'Document') and hasattr(window.Document, 'CurrentViewMode'):
                                    view_mode_val = window.Document.CurrentViewMode
                                    view_mode = next((vm for vm in ExplorerViewMode if vm.value == view_mode_val),
                                                    ExplorerViewMode.DETAILS)
                            except Exception as e:
                                logger.debug(f"Error getting view mode: {e}")

                            # Get selected files
                            try:
                                if hasattr(window, 'Document') and hasattr(window.Document, 'SelectedItems'):
                                    selected_files = [item.Name for item in window.Document.SelectedItems()]
                            except Exception as e:
                                logger.debug(f"Error getting selected files: {e}")

                            # Get focused file
                            try:
                                if hasattr(window, 'Document') and hasattr(window.Document, 'FocusedItem'):
                                    focused_file = window.Document.FocusedItem.Name
                            except Exception as e:
                                logger.debug(f"Error getting focused file: {e}")

                            break
                    except Exception as item_error:
                        logger.debug(f"Error accessing shell window {i}: {item_error}")
                        continue
            except Exception as enum_error:
                logger.debug(f"Error enumerating shell windows: {enum_error}")
                raise ValueError("Could not enumerate shell windows")

            # Determine if it's a special folder
            is_special_folder = ":" in location_path or not os.path.exists(location_path)

            # Create command
            if location_path:
                if is_special_folder:
                    create_command = f"explorer shell:{location_path}"
                else:
                    create_command = f"explorer \"{location_path}\""

        except Exception as e:
            # Log at debug level to avoid filling logs with errors
            logger.debug(f"Error creating Explorer window data: {e}")
            # Fall back to basic Explorer window data - handled below
            pass

        # Create and return the window data object (with whatever info we gathered)
        return ExplorerWindowData(
            hwnd=hwnd,
            title=title,
            class_name=class_name,
            window_type=WindowType.EXPLORER,
            state=state,
            position=pos,
            size=size,
            process=process_info,
            monitor=monitor,
            visible=visible,
            always_on_top=always_on_top,
            location_path=location_path,
            location_url=location_url,
            view_mode=view_mode,
            selected_files=selected_files,
            focused_file=focused_file,
            create_command=create_command,
            is_special_folder=is_special_folder
        )

    @classmethod
    def _create_browser_window_data(
        cls, hwnd: int, title: str, class_name: str,
        state: WindowState, pos: Position, size: Size,
        process_info: ProcessInfo, monitor: Optional[Monitor],
        visible: bool, always_on_top: bool
    ) -> BrowserWindowData:
        """Create browser-specific window data"""
        # Extract URL from title (common pattern in browsers)
        url = ""
        browser_type = ""

        # Determine browser type from process
        exe_name = os.path.basename(process_info.path).lower() if process_info.path else ""
        if exe_name in cls.BROWSER_EXES:
            browser_type = cls.BROWSER_EXES[exe_name]

        # Try to extract URL from title
        # Common patterns like "Page Title - Browser Name" or "Page Title | Browser Name"
        if browser_type:
            # Remove browser name from title
            clean_title = re.sub(r'\s[-|]\s.*$', '', title)
            if clean_title != title:
                # Just use cleaned title, actual URL extraction would require automation
                pass

        return BrowserWindowData(
            hwnd=hwnd,
            title=title,
            class_name=class_name,
            window_type=WindowType.BROWSER,
            state=state,
            position=pos,
            size=size,
            process=process_info,
            monitor=monitor,
            visible=visible,
            always_on_top=always_on_top,
            url=url,
            browser_type=browser_type
        )


# ----------------------------------------
# Window data collection and management
# ----------------------------------------

class WindowCollection:
    """Class for collecting and managing window data"""

    def __init__(self):
        """Initialize window collection"""
        self.windows: Dict[int, BaseWindowData] = {}
        self.collection_time = time.time()

    def collect_windows(self, visible_only: bool = True) -> None:
        """Collect window data for all windows"""
        self.windows = {}
        self.collection_time = time.time()

        def enum_callback(hwnd, _):
            """Callback for EnumWindows"""
            # Skip invisible windows if requested
            if visible_only and not win32gui.IsWindowVisible(hwnd):
                return True

            # Skip windows with empty titles if requested
            if not win32gui.GetWindowText(hwnd):
                return True

            # Create window data
            window_data = WindowDataFactory.create_window_data(hwnd)
            self.windows[hwnd] = window_data
            return True

        win32gui.EnumWindows(enum_callback, None)

    def get_windows_by_type(self, window_type: WindowType) -> Dict[int, BaseWindowData]:
        """Get windows of a specific type"""
        return {hwnd: data for hwnd, data in self.windows.items()
                if data.window_type == window_type}

    def get_window_by_hwnd(self, hwnd: int) -> Optional[BaseWindowData]:
        """Get window data by handle"""
        return self.windows.get(hwnd)

    def get_active_window(self) -> Optional[BaseWindowData]:
        """Get the currently active window"""
        active_hwnd = win32gui.GetForegroundWindow()
        return self.get_window_by_hwnd(active_hwnd)

    def to_dict(self) -> Dict[str, Any]:
        """Convert collection to dictionary for serialization"""
        return {
            "collection_time": self.collection_time,
            "windows": {str(hwnd): data.to_dict() for hwnd, data in self.windows.items()}
        }

    def to_json(self, file_path: Optional[str] = None) -> Optional[str]:
        """Convert collection to JSON string or save to file"""
        data = self.to_dict()
        json_str = json.dumps(data, indent=2)

        if file_path:
            with open(file_path, 'w') as f:
                f.write(json_str)

        return json_str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WindowCollection':
        """Create collection from dictionary"""
        collection = cls()
        collection.collection_time = data.get("collection_time", time.time())

        windows_data = data.get("windows", {})
        for hwnd_str, window_data in windows_data.items():
            hwnd = int(hwnd_str)

            # Determine window type and create appropriate object
            window_type_str = window_data.get("window_type", "UNKNOWN")
            window_type = WindowType[window_type_str] if isinstance(window_type_str, str) else window_type_str

            if window_type == WindowType.EXPLORER:
                window = ExplorerWindowData.from_dict(window_data)
            elif window_type == WindowType.BROWSER:
                window = BrowserWindowData.from_dict(window_data)
            else:
                window = BaseWindowData.from_dict(window_data)

            collection.windows[hwnd] = window

        return collection

    @classmethod
    def from_json(cls, json_str: str) -> 'WindowCollection':
        """Create collection from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)

    @classmethod
    def from_file(cls, file_path: str) -> 'WindowCollection':
        """Load collection from file"""
        with open(file_path, 'r') as f:
            json_str = f.read()
        return cls.from_json(json_str)


# Example usage
if __name__ == "__main__":
    # Create a window collection
    collection = WindowCollection()

    # Collect window data
    print("Collecting window data...")
    collection.collect_windows()

    # Print window counts by type
    print("\nWindow counts by type:")
    for window_type in WindowType:
        windows = collection.get_windows_by_type(window_type)
        if windows:
            print(f"{window_type.name}: {len(windows)}")

    # Print active window info
    active_window = collection.get_active_window()
    if active_window:
        print("\nActive window:")
        print(f"Title: {active_window.title}")
        print(f"Type: {active_window.window_type.name}")
        print(f"Position: {active_window.position}")
        print(f"Size: {active_window.size}")

        if active_window.window_type == WindowType.EXPLORER and isinstance(active_window, ExplorerWindowData):
            print(f"Path: {active_window.location_path}")
            print(f"View mode: {active_window.view_mode.name}")

    # Save to file
    collection.to_json("windows.json")
    print("\nSaved window data to windows.json")
