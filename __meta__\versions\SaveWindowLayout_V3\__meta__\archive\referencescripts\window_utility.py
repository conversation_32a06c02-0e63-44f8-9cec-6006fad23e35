"""
window_utility.py - Utility functions for window management

This module provides specialized utility functions that enhance the core window
management capabilities provided by window_base.py and window_explorer.py, such as
closing duplicate windows and other maintenance operations.
"""

# Standard imports
import os
import sys
import urllib.parse

# Win32 API imports
import win32gui
import win32com.client
import win32con
import win32api

# Import from our other modules
from window_base import get_all_windows
from window_explorer import ExplorerWindow, get_explorer_windows


class DuplicateWindowCloser:
    """Class for finding and closing duplicate windows"""

    @staticmethod
    def get_special_folder_path(title):
        """
        Determines the path of special folders based on their title.

        Args:
            title: The title of the Explorer window.

        Returns:
            A string representing the path, or None if it cannot be determined.
        """
        title_lower = title.lower()

        special_folders = {
            "this pc": "::{20D04FE0-3AEA-1069-A2D8-08002B30309D}",  # This PC
            "quick access": "::{679F85CB-0220-4080-B29B-5540CC05AAB6}",  # Quick Access
            "desktop": "::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}",  # Desktop
            "downloads": "::{374DE290-123F-4565-9164-39C4925E467B}",  # Downloads
            "documents": "::{A8CDFF1C-4878-43be-B5FD-F8091C1C60D0}",  # Documents
            "music": "::{1CF1260C-4DD0-4ebb-811F-33C572699FDE}",  # Music
            "pictures": "::{3ADD1653-EB32-4cb0-BBD7-DFA0ABB5ACCA}",  # Pictures
            "videos": "::{A0953C92-50DC-43bf-BE83-3742FED03C9C}",  # Videos
            "network": "::{F02C1A0D-BE21-4350-88B0-7367FC96EF3C}",  # Network
            "control panel": "::{26EE0668-A00A-44D7-9371-BEB064C98683}",  # Control Panel
            "recycle bin": "::{645FF040-5081-101B-9F08-00AA002F954E}",  # Recycle Bin
        }

        for folder_name, clsid in special_folders.items():
            if folder_name in title_lower:
                return clsid

        # If no specific match, check if it's a drive
        if title_lower.endswith(")") and ":" in title_lower:
            drive_letter = title_lower[title_lower.rfind("(") + 1 : title_lower.rfind(")")]
            if (
                len(drive_letter) == 2
                and drive_letter[0].isalpha()
                and drive_letter[1] == ":"
            ):
                return drive_letter + "\\"

        return None

    @staticmethod
    def get_explorer_window_info(window):
        """
        Gets information about an Explorer window.

        Args:
            window: A Shell window object.

        Returns:
            A dictionary with window information, or None if not an Explorer window.
        """
        hwnd = window.HWND
        title = win32gui.GetWindowText(hwnd)

        # Use LocationURL if available, otherwise fall back to parsing the title
        try:
            location_url = window.LocationURL
            if location_url:
                path = urllib.parse.unquote(location_url).lstrip("/").replace("\\", "/")
            else:
                path = DuplicateWindowCloser.get_special_folder_path(title)
        except Exception:
            path = DuplicateWindowCloser.get_special_folder_path(title)

        if not path:
            return None

        placement = win32gui.GetWindowPlacement(hwnd)
        pos = [placement[4][0], placement[4][1]]
        size = [placement[4][2] - pos[0], placement[4][3] - pos[1]]
        class_name = win32gui.GetClassName(hwnd)
        always_on_top = bool(
            win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) & win32con.WS_EX_TOPMOST
        )

        if not win32gui.IsWindowVisible(hwnd) or size[0] <= 1 or size[1] <= 1:
            return None

        return {
            "hwnd": hwnd,
            "window_state": placement[1],
            "position": pos,
            "size": size,
            "title": title,
            "path": path,
            "class_name": class_name,
            "always_on_top": always_on_top,
            "type": "explorer",
        }

    @classmethod
    def get_shell_windows(cls):
        """Retrieves all open Explorer windows using Shell.Application."""
        try:
            shell = win32com.client.Dispatch("Shell.Application")
            windows = [
                info
                for window in shell.Windows()
                if (info := cls.get_explorer_window_info(window))
            ]
            return windows
        except Exception as e:
            print(f"Error getting shell windows: {e}")
            return []

    @classmethod
    def close_duplicate_explorer_windows(cls):
        """
        Closes duplicate Explorer windows.

        Returns:
            List of closed window information dictionaries
        """
        windows = cls.get_shell_windows()
        total_windows = len(windows)
        paths = {}
        duplicates = []

        for window in windows:
            path = window["path"]
            if path in paths:
                duplicates.append(window)
            else:
                paths[path] = window

        closed_windows_info = []
        for window in duplicates:
            try:
                win32gui.PostMessage(window["hwnd"], win32con.WM_CLOSE, 0, 0)
                closed_windows_info.append(window)
                print(f"Closed duplicate explorer window: {window['title']} at {window['path']}")
            except Exception as e:
                print(f"Failed to close window: {window['title']} at {window['path']} with error: {e}")

        num_duplicates_closed = len(closed_windows_info)
        print(f"From a total of {total_windows} windows: closed {num_duplicates_closed} duplicate(s).")
        return closed_windows_info

    @classmethod
    def close_duplicate_windows(cls, window_type="all"):
        """
        Closes duplicate windows of a specified type.

        Args:
            window_type: Type of windows to check ('all', 'explorer', or 'other')

        Returns:
            List of closed window information dictionaries
        """
        if window_type == "explorer":
            return cls.close_duplicate_explorer_windows()

        # Get all windows
        all_windows = get_all_windows()
        windows_by_path = {}
        closed_windows = []

        for window in all_windows:
            # For explorer windows, use path as the key
            if window.class_name == "CabinetWClass" and window_type in ["all", "explorer"]:
                # Create ExplorerWindow to get the path
                ex_window = ExplorerWindow(window.hwnd)
                if ex_window.location_path:
                    key = f"explorer:{ex_window.location_path}"
                    if key in windows_by_path:
                        try:
                            win32gui.PostMessage(window.hwnd, win32con.WM_CLOSE, 0, 0)
                            closed_windows.append(window.to_dict())
                            print(f"Closed duplicate explorer window: {window.title}")
                        except Exception as e:
                            print(f"Failed to close window: {window.title} with error: {e}")
                    else:
                        windows_by_path[key] = window

            # For other windows, use process_path and title as the key
            elif window_type in ["all", "other"] and window.process_path:
                key = f"{window.process_path}:{window.title}"
                if key in windows_by_path:
                    try:
                        win32gui.PostMessage(window.hwnd, win32con.WM_CLOSE, 0, 0)
                        closed_windows.append(window.to_dict())
                        print(f"Closed duplicate window: {window.title}")
                    except Exception as e:
                        print(f"Failed to close window: {window.title} with error: {e}")
                else:
                    windows_by_path[key] = window

        print(f"Closed {len(closed_windows)} duplicate windows.")
        return closed_windows


def improve_explorer_columns(hwnd=None):
    """
    Automatically resizes all columns in the current Explorer window to fit their content.

    Args:
        hwnd: Window handle of Explorer window (if None, uses active window)
    """
    try:
        if not hwnd:
            hwnd = win32gui.GetForegroundWindow()

        # Check if it's an Explorer window
        if win32gui.GetClassName(hwnd) != "CabinetWClass":
            print("Not an Explorer window")
            return False

        # Simulate Ctrl+NumpadPlus keystroke (auto-size all columns)
        win32gui.SetFocus(hwnd)
        win32api.keybd_event(0x11, 0, 0, 0)  # Ctrl down
        win32api.keybd_event(0x6D, 0, 0, 0)  # NumpadPlus
        win32api.keybd_event(0x11, 0, 0x0002, 0)  # Ctrl up
        return True
    except Exception as e:
        print(f"Error resizing columns: {e}")
        return False


def find_window_by_title_or_class(title_pattern=None, class_name=None, exact_match=False):
    """
    Find windows matching the given title pattern and/or class name.

    Args:
        title_pattern: String or regex pattern to match against window titles
        class_name: Class name to match
        exact_match: Whether to require an exact match for the title

    Returns:
        List of Window objects matching the criteria
    """
    matching_windows = []

    def callback(hwnd, _):
        if not win32gui.IsWindowVisible(hwnd):
            return True

        window_title = win32gui.GetWindowText(hwnd)
        window_class = win32gui.GetClassName(hwnd)

        title_matches = False
        if title_pattern is None:
            title_matches = True
        elif exact_match and window_title == title_pattern:
            title_matches = True
        elif not exact_match and title_pattern.lower() in window_title.lower():
            title_matches = True

        class_matches = class_name is None or window_class == class_name

        if title_matches and class_matches:
            from window_base import Window
            matching_windows.append(Window(hwnd))
        return True

    win32gui.EnumWindows(callback, None)
    return matching_windows


# Example usage
if __name__ == "__main__":
    print("Window Utility Functions")
    print("=======================")
    print("1. Close duplicate Explorer windows")
    print("2. Improve Explorer columns")
    print("3. Find window by title")

    choice = input("Choose an option (1-3): ")

    if choice == "1":
        DuplicateWindowCloser.close_duplicate_explorer_windows()
    elif choice == "2":
        improve_explorer_columns()
        print("Improved Explorer columns for the active window")
    elif choice == "3":
        title = input("Enter window title to search for: ")
        windows = find_window_by_title_or_class(title_pattern=title)
        print(f"Found {len(windows)} matching windows:")
        for window in windows:
            print(f"  {window}")
    else:
        print("Invalid choice")