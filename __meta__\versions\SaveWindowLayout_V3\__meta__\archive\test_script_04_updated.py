import time
import win32com.client
import win32gui
import pythoncom

def get_window_progid_clsid(hwnd):
    pythoncom.CoInitialize()
    window = None
    progid = None
    clsid = None
    class_name = win32gui.GetClassName(hwnd)
    if class_name:
        try:
            window = win32com.client.GetClass(class_name)
            progid = window._oleobj_.GetTypeInfo().GetTypeAttr()._ProgID_
            clsid = window._oleobj_.GetTypeInfo().GetTypeAttr().iid
        except AttributeError:
            pass
    pythoncom.CoUninitialize()
    return progid, clsid

def get_foreground_window_info(update_on_hwnd_change=False):
    # Initialize last_hwnd attribute to a default value
    if not hasattr(get_foreground_window_info, 'last_hwnd'):
        get_foreground_window_info.last_hwnd = None
    hwnd = win32gui.GetForegroundWindow()
    if update_on_hwnd_change:
        if hwnd != get_foreground_window_info.last_hwnd:
            get_foreground_window_info.last_hwnd = hwnd
        else:
            return get_foreground_window_info.last_info
    if win32gui.GetWindowText(hwnd) != '':
        progid, clsid = get_window_progid_clsid(hwnd)
        info = {
            'hwnd': hwnd,
            'progid': progid,
            'clsid': clsid,
            'title': win32gui.GetWindowText(hwnd),
            'class': win32gui.GetClassName(hwnd),
        }
        get_foreground_window_info.last_info = info
        get_foreground_window_info.last_hwnd = hwnd
        return info
    return None


# Continous loop
while True:
    info = get_foreground_window_info(update_on_hwnd_change=True)
    if info:
        print(f'HWND: {info["hwnd"]} | ProgID: {info["progid"]} | CLSID: {info["clsid"]} | Title: {info["title"]} | Class: {info["class"]}')
    time.sleep(0.1)
