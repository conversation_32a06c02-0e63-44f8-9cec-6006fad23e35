<!-- ======================================================= -->

the current project is a work in progress utility i'm working on for window management, but it's been a long time since i've opened it and it's **really** messy. i remember that i was testing various methods with the goal of correctly collecting all available and useful parameters, a working example would be the script `\reference_utils\util_print_active_window_data.py` which outputs this data continously:

```
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------
                                 title : SaveWindowLayout_V3 - C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\Py_WindowTiler\SaveWindowLayout_V3\__meta__\chat001.md • - Sublime Text
                                 class : PX_WINDOW_CLASS
                                  hwnd : 6426254
                            visibility : 1
                        controls_state : 3
                              position : (-8, -8)
                                  size : (3856, 2128)
                             placement : (2, 3, (-1, -1), (-1, -1), (736, 41, 2285, 2044))
                          process_path : C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe
                            process_id : 1904
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------
                                 title :
                                 class : ForegroundStaging
                                  hwnd : 8262138
                            visibility : 0
                        controls_state : 1
                              position : (0, 0)
                                  size : (0, 0)
                             placement : (0, 1, (-1, -1), (-1, -1), (0, 0, 0, 0))
                          process_path : C:\Windows\explorer.exe
                            process_id : 2880
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------
                                 title : Task Switching
                                 class : XamlExplorerHostIslandWindow
                                  hwnd : 10489564
                            visibility : 1
                        controls_state : 1
                              position : (0, 0)
                                  size : (3840, 2112)
                             placement : (0, 1, (-1, -1), (-1, -1), (0, 0, 3840, 2112))
                          process_path : C:\Windows\explorer.exe
                            process_id : 2880
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------
                                 title :
                                 class : ForegroundStaging
                                  hwnd : 16975036
                            visibility : 0
                        controls_state : 1
                              position : (0, 0)
                                  size : (0, 0)
                             placement : (0, 1, (-1, -1), (-1, -1), (0, 0, 0, 0))
                          process_path : C:\Windows\explorer.exe
                            process_id : 2880
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------
                                 title : "\\.\DISPLAY1   |   Size:(0, 0)   |   Pos:(0, 0)"
                                 class : CASCADIA_HOSTING_WINDOW_CLASS
                                  hwnd : 13108680
                            visibility : 1
                        controls_state : 1
                              position : (1532, 484)
                                  size : (1600, 720)
                             placement : (0, 1, (-1, -1), (-1, -1), (1532, 484, 3132, 1204))
                          process_path : C:\Program Files\WindowsApps\Microsoft.WindowsTerminal_1.21.10351.0_x64__8wekyb3d8bbwe\WindowsTerminal.exe
                            process_id : 23792
```

i think the script `print_active_window_data.py` was the latest script i worked on, but my workflow when doing this was really messy. i basically used the single file @print_active_window_data.py as a playground, but i remember working on defining a "window-object" with all of the desired window data in (including things such as which monitor, etc).

now i want to pick up this project, but first we need identify all of the valuable snippets and consolidate it into a new script called `v3_print_active_window_data`

<!-- ======================================================= -->

Reorganize the scripts according to your specifications by creating a /referencescripts directory for unused scripts that may still contain valuable information while keeping only fully-extracted scripts in the /archive folder.

<!-- ======================================================= -->

i have removed /referencescripts/*.bat , please start consolidation scripts within referencescripts and name them more correctly (according to their functionality), consolidate useful information without removing potential useful info/fnctionality - do this while ensuring we remove unneccessary duplications
