{"name": "test_layout.json", "timestamp": "2025-03-26 19:11:22.934789", "windows": [{"hwnd": 16975968, "title": "SaveWindowLayout_02_Wip_6.py - SaveWindowLayout_V3 - <PERSON>ursor", "category": "BROWSER", "position": [16, 55], "size": [1408, 1977]}, {"hwnd": 10228130, "title": "Tiling Window Workflow Philosophy - Google Chrome", "category": "BROWSER", "position": [2146, 55], "size": [1701, 1980]}, {"hwnd": 4065456, "title": "Sourcetree", "category": "UNKNOWN", "position": [0, 1239], "size": [3840, 872]}, {"hwnd": 6426254, "title": "SaveWindowLayout_V3 - untitled • - Sublime Text", "category": "DEVELOPMENT", "position": [765, 139], "size": [1701, 1980]}, {"hwnd": 3416238, "title": "src - File Explorer", "category": "EXPLORER", "position": [2339, 494], "size": [1549, 851]}, {"hwnd": 7346906, "title": "C:\\Windows\\system32\\cmd.exe", "category": "UNKNOWN", "position": [148, 156], "size": [1129, 635]}, {"hwnd": 9443416, "title": "Personalized WM Workflow - Google Chrome", "category": "BROWSER", "position": [2146, 55], "size": [1701, 1980]}, {"hwnd": 11273780, "title": "app_mpvplayer - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_mpvplayer\\user\\observations.md - Sublime Text", "category": "DEVELOPMENT", "position": [2146, 55], "size": [1701, 1980]}, {"hwnd": 16584652, "title": "Python - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__ProjectGenerator\\src\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py - Sublime Text", "category": "DEVELOPMENT", "position": [2296, 0], "size": [1552, 852]}, {"hwnd": 5182208, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__ProjectGenerator\\src\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py - Sublime Text", "category": "DEVELOPMENT", "position": [2279, 288], "size": [1552, 852]}, {"hwnd": 22809400, "title": "window-dep - File Explorer", "category": "EXPLORER", "position": [2043, 736], "size": [1549, 851]}, {"hwnd": 9177752, "title": "referencescripts - File Explorer", "category": "EXPLORER", "position": [1991, 684], "size": [1549, 851]}, {"hwnd": 24908006, "title": "SaveWindowLayout_V3 - File Explorer", "category": "EXPLORER", "position": [249, 583], "size": [776, 851]}, {"hwnd": 5440382, "title": "history - File Explorer", "category": "EXPLORER", "position": [1965, 658], "size": [1549, 851]}, {"hwnd": 5642296, "title": "transcribe - File Explorer", "category": "EXPLORER", "position": [379, 713], "size": [1549, 851]}, {"hwnd": 15604096, "title": "app_mpvplayer - File Explorer", "category": "EXPLORER", "position": [1939, 632], "size": [1549, 851]}, {"hwnd": 5444460, "title": "src - File Explorer", "category": "EXPLORER", "position": [1913, 606], "size": [1549, 851]}, {"hwnd": 13962126, "title": "(10) Bambulab 3D Printer - YouTube — Firefox Developer Edition", "category": "BROWSER", "position": [617, 55], "size": [1535, 1996]}, {"hwnd": 9244118, "title": "D:\\NOT_BACKED_UP\\transcribe\\batch_transcription_en.yaml • - Sublime Text", "category": "DEVELOPMENT", "position": [2297, 0], "size": [1550, 851]}, {"hwnd": 10031428, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__SpeechToText\\venv\\Scripts\\python.exe", "category": "UNKNOWN", "position": [1807, 577], "size": [1422, 958]}, {"hwnd": 2037750, "title": "py__YoutubePlaylistExporter - File Explorer", "category": "EXPLORER", "position": [431, 765], "size": [1549, 851]}, {"hwnd": 7148246, "title": "c_template_runner_sysinstr_d_interactive_optimizer - File Explorer", "category": "EXPLORER", "position": [353, 687], "size": [1549, 851]}, {"hwnd": 9970174, "title": "seg0 - File Explorer", "category": "EXPLORER", "position": [301, 635], "size": [1549, 851]}, {"hwnd": 15405304, "title": "<!ext:url;lnk sort:dm> empty: sfda: - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python - Everything (1.5a) 1.5.0.1383a (x64)", "category": "UNKNOWN", "position": [0, 1233], "size": [3840, 879]}, {"hwnd": 2172240, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__ProjectGenerator\\src\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py - Sublime Text", "category": "DEVELOPMENT", "position": [2297, 0], "size": [1550, 851]}, {"hwnd": 7474124, "title": "Microsoft Visual C++ Runtime Library", "category": "UNKNOWN", "position": [1715, 939], "size": [427, 296]}, {"hwnd": 7086826, "title": "Settings", "category": "UNKNOWN", "position": [2292, 0], "size": [769, 1780]}, {"hwnd": 22811410, "title": "Windows Input Experience", "category": "SYSTEM", "position": [0, 0], "size": [3840, 2160]}, {"hwnd": 7542896, "title": "Task Manager", "category": "UNKNOWN", "position": [-32000, -32000], "size": [160, 28]}, {"hwnd": 4793418, "title": "llm_working_singlescripts - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Ai_Utils\\working_systems\\llm_working_singlescripts\\b_llm_sysinstructions_simple\\langchain_sysinstructions_simple_027_tailrhymes.py - Sublime Text", "category": "DEVELOPMENT", "position": [-32000, -32000], "size": [160, 28]}, {"hwnd": 7872684, "title": "__SHELL__ - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__SHELL__\\exe\\LIB\\bat\\py_venv_terminal.bat - Sublime Text", "category": "DEVELOPMENT", "position": [-32000, -32000], "size": [160, 28]}, {"hwnd": 7677262, "title": "<PERSON><PERSON> navn - <PERSON><PERSON>", "category": "UTILITY", "position": [-32000, -32000], "size": [160, 28]}, {"hwnd": 6554000, "title": "Program Manager", "category": "EXPLORER", "position": [0, 0], "size": [3840, 2160]}]}