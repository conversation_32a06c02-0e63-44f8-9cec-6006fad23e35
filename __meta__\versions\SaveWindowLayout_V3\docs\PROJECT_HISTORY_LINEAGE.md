# Window Tiling Manager: Project Lineage

This document traces the evolution of the SaveWindowLayout project, documenting its chronological progression from initial concept to current implementation.

## Project Overview

The SaveWindowLayout project is a utility for managing window layouts on Windows systems, enabling users to save, categorize, and restore window arrangements according to specific workflows and tasks. The project has evolved from a philosophical foundation to a structured, modular implementation with a clear workflow-oriented architecture.

## Phase 1: Foundational Concepts

### 001: Initial Concept & Philosophy

**[2025.03.26-dsk-001-a]**

The project began with a detailed exploration of the workflow philosophy behind personalized window managers. Key aspects include:

-   **User-Centric Adaptability**: Adapting technology to users through context-awareness, customization, and consistent interfaces
-   **Efficient Spatial Utilization**: Optimizing screen space with dynamic tiling, monitor-aware management, and virtual desktops
-   **Streamlined Interaction**: Minimizing friction via keyboard-driven controls, automation, and intelligent window rules
-   **Intentional Focus Management**: Managing attention through z-order control and distraction minimization
-   **Task-Oriented State Management**: Maintaining workflow continuity with persistent layouts and snapshot/restore functionality
-   **Modularity & Extensibility**: Supporting customization through plugins, integration capabilities, and scalable configuration
-   **Transparency & Minimalism**: Reducing visual clutter with minimal UI overhead and clear visual signposting

This philosophical foundation established the core principles that would guide all subsequent development.

### 002: Initial Hierarchical File Structure

**[2025.03.26-dsk-001-b]**

The project's first structural proposal introduced a dynamic hierarchical file organization that naturally mirrored the workflow process:

```
window-manager-project/
├── README.md
├── requirements.txt
├── config/                # User-configurable settings
├── entry_points/          # Clear execution starting points
└── workflow/              # Ordered workflow representation
    ├── 01_detection/      # Window detection stage
    ├── 02_categorization/ # Window categorization stage
    ├── 03_layout/         # Layout application stage
    ├── 04_state_management/ # State persistence stage
    └── 05_interaction/    # User interaction stage
```

This structure emphasized clarity, intuitive organization, and alignment with the natural workflow progression.

### 003: Detailed File Structure Blueprint

**[2025.03.26-dsk-001-c]**

Building on the initial structure, this iteration provided a more comprehensive blueprint with specific files mapped to their intended locations:

```
window-manager-project/
├── README.md
├── requirements.txt
├── config/
│   ├── categories.yaml
│   ├── layouts/
│   └── settings.yaml
├── scripts/
│   ├── save_layout.bat
│   ├── apply_layout.bat
│   ├── window_monitor.bat
│   └── window_manager.bat
└── workflow/
    ├── __init__.py
    ├── 01_detection/
    ├── 02_categorization/
    ├── 03_layout/
    ├── 04_automation/
    ├── 05_state_management/
    └── 06_interaction/
```

This representation served as a comprehensive blueprint for organizing the project's components.

## Phase 2: Structure & Configuration Refinement

### 004: Dynamic Hierarchical Representation

**[2025.03.26-dsk-002-a]**

This entry explored how the file structure could more inherently embody the concepts it represents, focusing on creating a dynamic hierarchical organization that corresponds with the natural order of operations. The discussion emphasized:

-   How directory and file naming can reflect the sequence of operations
-   The importance of clear boundaries between workflow stages
-   How the structure can guide new developers in understanding the system

### 005: Critical Structure Assessment & Workflow Philosophy

**[2025.03.26-dsk-002-b]**

A detailed examination of the most critical aspects for maximizing value in the window manager project, including:

-   The workflow philosophy behind personalized window managers
-   How window categorization enables new workflow possibilities
-   A refined file structure that uses sequential numbering to indicate workflow progression
-   Subtle filename structuring with concise numeric prefixes to create clear hierarchy without being overly explicit

This assessment provided critical insights into optimizing both the architecture and user experience.

### 006: Configuration & Categorization Examples

**[2025.03.26-dsk-002-c]**

A practical demonstration of the configuration system, focusing on the `config/` and `workflow/02_categorization/` components:

-   Example YAML configuration for window categories and rules
-   Example layout definition files for specific workspace arrangements
-   Python code examples showing how configuration drives the categorization system

These examples illustrated how configuration files would enable powerful window categorization.

### 007: Configuration Format Exploration

**[2025.03.26-dsk-002-d, 2025.03.26-dsk-002-e]**

A comparison between YAML and JSON formats for configuration files:

-   YAML version with better support for comments and more readable structure
-   JSON version showing the same structure in a more compact format
-   Detailed category definitions with app names, window title patterns, and layout associations

This exploration highlighted the benefits of human-readable configuration formats, particularly YAML's superior readability and comment support.

### 008: Refined File Structure with Visual Clarity

**[2025.03.26-dsk-002-f]**

A refined file structure that incorporated visual cues for improved intuition:

```
window-manager-project/
├── README.md
├── requirements.txt
├── ⚙️ config/                 # User configuration files
│   ├── settings.yaml
│   ├── categories.yaml
│   └── layouts/
├── 🚀 entrypoints/            # Clearly defined entry points
│   ├── 01_run_monitor.bat
│   ├── 02_save_current_layout.bat
│   ├── 03_apply_layout.bat
│   └── 04_launch_window_manager.bat
└── 🌳 workflow/               # Workflow progression
    ├── __init__.py
    ├── 01_detection/
    ├── 02_categorization/
    ├── 03_layout/
    ├── 04_automation/
    ├── 05_state/
    └── 06_interaction/
```

This structure used emoji symbols, clear folder naming, and numbered files to enhance visual recognition and reinforce the workflow sequence.

## Phase 3: Architecture & Interface Design

### 009: Controller-Based Architecture Exploration

**[2025.03.26-dsk-003-a, 2025.03.26-dsk-003-c]**

An investigation of how controllers could intuitively interface between different workflow stages:

```
workflow/
├── 01_detection/
│   ├── __init__.py
│   ├── interface.py        # Public API definition
│   ├── window.py
│   ├── detector.py
│   ├── explorer.py
│   └── monitor.py
├── 02_categorization/
│   ├── __init__.py
│   ├── interface.py        # Public API definition
│   ├── window_types.py
│   ├── categorizer.py
│   ├── rules.py
│   └── filters.py
```

This structure introduced dedicated interface.py files to clearly define each stage's public API, creating explicit contracts between components.

### 010: TOML Configuration Alternative

**[2025.03.26-dsk-003-b]**

An exploration of using TOML configuration files as an alternative to YAML:

-   Comparison of TOML's syntax and readability to YAML and JSON
-   Integration of TOML configuration with the existing architecture
-   Discussion of the pros and cons of different configuration formats

This entry considered how configuration format choices affect both development and user experience.

### 011: File Naming Approaches Comparison

**[2025.03.26-dsk-003-d]**

A comparison of different approaches to file naming within workflow directories:

```
01_detection/
├── 01_base.py
├── 02_monitor.py
├── 03_inspector.py
└── 04_explorer.py
```

This minimalist approach used numeric prefixes to maintain sequence while keeping filenames concise and descriptive, balancing clarity with simplicity.

### 012: Controller Layer Introduction

**[2025.03.26-dsk-003-e]**

Introduction of a dedicated controllers layer to bridge workflow stages:

```
workflow/
├── __init__.py
├── controllers/            # Bridging controllers
│   ├── __init__.py
│   ├── monitor_controller.py     # Detection ↔ Categorization
│   ├── layout_controller.py      # Categorization ↔ Layout
│   ├── automation_controller.py  # Layout ↔ Automation
│   ├── state_controller.py       # Automation ↔ State
│   └── interaction_controller.py # State ↔ Interaction
├── 01_detection/
// ... existing workflow stages ...
```

This structure formalized the controller concept as explicit bridges between sequential workflow stages, creating a more intuitive flow.

## Phase 4: Code Implementation Strategy

### 013: Inherently Intuitive Code Design

**[2025.03.26-dsk-004-a]**

Detailed guidelines for writing inherently intuitive code within the project structure:

-   **Clear Abstraction with Interfaces**: Using interface.py files to define explicit contracts
-   **Data-Driven Configuration Loading**: Dynamically loading configuration from structured files
-   **Event-Driven Communication**: Controllers acting as event handlers between stages
-   **Descriptive Naming and Docstrings**: Consistent naming conventions for instant recognition
-   **Type Hinting**: Enhanced code readability through Python type annotations
-   **Dependency Injection**: Explicit dependency passing for clearer component relationships

This entry included code examples for interfaces, controllers, and testing approaches, establishing a foundation for implementation.

### 014: Numbered Controllers Structure

**[2025.03.26-dsk-004-b]**

A refined controller structure that uses numbered prefixes to indicate workflow sequence:

```
controllers/
├── 01_monitor_controller.py    # Detection ↔ Categorization
├── 02_layout_controller.py     # Categorization ↔ Layout
├── 03_automation_controller.py # Layout ↔ Automation
├── 04_state_controller.py      # Automation ↔ State
└── 05_interaction_controller.py # State ↔ Interaction
```

This structure reinforced the sequential nature of the workflow through explicit numbering of controller components.

### 015: Comprehensive Project Structure Finalization

**[2025.03.26-dsk-004-c]**

A finalized project structure that transformed the architecture into an explicitly ordered and intuitively segmented hierarchy:

-   Configuration files in a dedicated `config/` directory
-   Clear entry points in `entrypoints/`
-   A workflow-oriented structure with numbered directories and files
-   Controllers that explicitly bridge between stages
-   Additional components for automation, state management, and interaction

This structure was designed to ensure effortless comprehension, easier maintenance, and seamless extensibility.

### 016: Testing Strategy & Code Organization

**[2025.03.26-dsk-004-d]**

A request for guidance on writing code in an "inherently intuitive" manner and establishing an effective testing strategy:

```
tests/
├── unit/                  # Unit tests for individual components
├── integration/           # Integration tests between stages
│   └── test_controllers/  # Tests for controller integration
├── config/                # Configuration validation tests
└── workflows/             # End-to-end workflow tests
```

This testing structure mirrored the application's architecture, ensuring comprehensive coverage at all levels of the system.

## Phase 5: Implementation Examples & Refinement

### 017: Stage-by-Stage Implementation Examples

**[2025.03.26-dsk-005-a]**

Detailed code examples for each stage of the workflow implementation:

1. **Detection**: Window representation and detection functionality
2. **Categorization**: Logic for categorizing windows based on rules
3. **Layout**: Functions for applying layouts to categorized windows
4. **Automation**: Actions for window manipulation and automation
5. **State**: State persistence for saving and loading arrangements
6. **Interaction**: User interfaces for interacting with the system

Additionally, examples of controllers bridging between stages and a high-level orchestrator tying everything together.

### 018: Refined Coding Approach

**[2025.03.26-dsk-005-b]**

Further refinement of the coding approach, focusing on:

-   **Unified Interfaces (Facade Pattern)**: Abstracting complexity behind simple interfaces
-   **Controller Simplicity**: Orchestrating interactions via clean API calls
-   **Dynamic Configuration**: Transparent configuration loading across modules
-   **Event-Driven Architecture**: Clear event triggers between components
-   **Consistent Naming**: Methods that instantly communicate their purpose

This entry also outlined a comprehensive testing strategy with unit, integration, and end-to-end tests.

### 019: Streamlined Implementation Strategy

**[2025.03.26-dsk-005-c]**

A streamlined approach to implementation focusing on five key elements:

1. **Simplified Stage APIs**: Clear, minimal public interfaces
2. **Controller Layer**: Short, explicit bridges between stages
3. **Top-Level Orchestrator**: A clear entry point for the full pipeline
4. **User-Friendly Entry Points**: Minimal, expressive scripts
5. **Simple Testing Approach**: Testing structure mirroring the application

This simplified strategy emphasized clarity, minimal interfaces, and intuitive organization.

## Phase 6: Final Architecture & Implementation

### 020: Intuitive & Dynamic Coding Strategy

**[2025.03.26-dsk-006-a]**

A refined coding strategy designed for systematic building of the application:

-   **Simplified Stage APIs**: Each stage exposing one clear public interface
-   **Controller Layer**: Short, explicit bridges between stages
-   **Top-Level Orchestrator**: Clear entry point orchestrating the full pipeline
-   **User-Friendly Entry Points**: Minimal, expressive CLI scripts
-   **Simple Testing Approach**: Tests structured to mirror application stages

This strategy emphasized maintaining intuitive, maintainable, and developer-friendly code.

### 021: Balanced Coding Approach

**[2025.03.26-dsk-006-b]**

A "best of both worlds" proposition balancing simplicity and flexibility:

-   **Unified Interfaces per Stage**: Each stage folder includes an `interface.py` exposing essential functions
-   **Simple Controllers to Bridge Stages**: Controllers orchestrate interactions between stages
-   **Centralized Config**: Small utility for loading configuration files
-   **Clear, Consistent Naming**: Action-oriented or noun-oriented function names

Additionally, a comprehensive testing strategy with unit tests, controller integration tests, and end-to-end CLI tests.

### 022: Project Restructuring Guide

**[2025.03.26-dsk-007-a]**

A request for guidance on consolidating existing code into the proposed workflow-specific structure:

-   Steps for transitioning from the current implementation
-   Strategy for organizing code into the new file structure
-   Approach for maintaining functionality during restructuring

This entry sought practical advice on implementing the architectural changes discussed in previous entries.

### 023: Workflow Possibilities Explanation

**[2025.03.26-dsk-008-a, 2025.03.26-dsk-008-b]**

Detailed explanation of how window managers create new workflow possibilities:

-   **Categorization Enables Automation**: Windows automatically organized based on categories
-   **Configuration-Driven Automation**: Structured configuration enabling systematic automation
-   **Automatic Workspace Creation**: Windows placed according to predefined layouts
-   **Context-Aware Layouts**: Layouts adapted based on detected application categories

This entry illustrated the practical benefits of the window manager's approach to workflow enhancement.

### 024: Current Implementation Review

**[2025.03.26-dsk-008-c]**

A review of the current implementation state, including:

-   **File Structure**: Organized around a workflow with detection, categorization, and layout
-   **Code Implementation**: Main entry point, window detection, categorization, and control modules
-   **Window Detection**: Comprehensive system using the Win32 API
-   **Window Categorization**: Classification into types like BROWSER, EXPLORER, DOCUMENT
-   **Window Layout**: Management for saving and restoring arrangements
-   **CLI Interface**: User interaction through command-line

This entry provided a snapshot of the existing implementation before restructuring.

### 025: Architecture & Code Strategy Integration

**[2025.03.26-dsk-008-d]**

The final entry combined architectural structure with an elegant coding strategy:

-   **Structural Clarity**: Ordered file hierarchy with intuitive organization
-   **Interface Clarity**: Explicit `.interface.py` files defining public APIs
-   **Flow Clarity**: Focused controllers bridging workflow stages
-   **Usage Clarity**: Simple, intuitive entry points for users

The implementation plan outlined six key steps:

1. Creating foundation structure with numbered directories
2. Implementing clear stage interfaces
3. Creating bridging controllers
4. Implementing a top-level orchestrator
5. Creating user-friendly entry points
6. Setting up a mirrored testing structure

This comprehensive approach ensures a clear, maintainable, and extensible system that aligns perfectly with both the philosophical foundation and the practical requirements of window management.

## Conclusion

The SaveWindowLayout project has evolved from a philosophical exploration of window management workflows to a comprehensive, structured implementation with a clear architecture and coding strategy. The project's development has been guided by principles of clarity, intuition, and modularity, resulting in a design that naturally embodies the window management workflow it seeks to enable.

The final architecture, with its workflow-oriented structure, clear interfaces, and bridging controllers, provides a solid foundation for continued development and extension while maintaining the core focus on enhancing user productivity through intelligent window management.
