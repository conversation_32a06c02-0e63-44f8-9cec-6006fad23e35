# Window Manager

A Python tool for detecting, categorizing, and managing windows in Windows OS.

## Features

-   **Window Detection**: Identify all windows in the system with detailed information
-   **Window Categorization**: Classify windows by type (browser, explorer, document, etc.)
-   **Layout Management**: Save and restore window layouts
-   **Active Window Monitoring**: Track the active window in real-time
-   **Window Search**: Find windows by title, class, or process name
-   **Multi-monitor Support**: Handle window layouts across multiple displays
-   **Global Hotkeys**: Control window management operations with keyboard shortcuts
-   **Command System**: Extensible command-based architecture for window operations
-   **Unified Entry Point**: Single, consistent entry point for all commands and features
-   **Comprehensive Testing**: Robust test suite with unit and integration tests

## Installation

### Requirements

-   Windows 10 or later
-   Python 3.7 or later
-   Required packages (see requirements.txt)

### Steps

1. Clone the repository:

```bash
git clone https://github.com/yourusername/window-manager.git
cd window-manager
```

2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Command Line Interface

The window manager provides a command-line interface with various options through the main.py entry point:

```bash
# List all open windows
python -m src.main --list

# List windows by category (BROWSER, EXPLORER, DOCUMENT, etc.)
python -m src.main --list-by-category BROWSER

# Save the current window layout
python -m src.main --save my_layout

# Apply a saved layout
python -m src.main --apply my_layout

# List all saved layouts
python -m src.main --list-layouts

# Find windows by title
python -m src.main --find "Chrome"

# Monitor the active window (press Ctrl+C to exit)
python -m src.main --monitor

# Run as a background service with hotkey support
python -m src.main --service

# Enable debug logging
python -m src.main --debug
```

> **Note**: The old entry point `src/entrypoints/window_manager_cli.py` is deprecated and will redirect to `src/main.py` with the same arguments.

### Global Hotkeys

When running in service mode, the following hotkeys are available system-wide:

| Hotkey     | Action                     |
| ---------- | -------------------------- |
| Ctrl+Alt+S | Save current window layout |
| Ctrl+Alt+A | Apply saved window layout  |
| Ctrl+Alt+H | Tile windows horizontally  |
| Ctrl+Alt+V | Tile windows vertically    |
| Ctrl+Alt+M | Monitor active window      |

You can customize these hotkeys by editing the preferences file in the config directory.

### Python API

You can also use the Window Manager as a Python library:

```python
from src.workflow.g_controllers import ApplicationController

# Create a controller
controller = ApplicationController()

# Get all windows
windows = controller.command_handler.execute_command("list-windows")

# Save the current layout
controller.command_handler.execute_command("save-layout", {"name": "my_layout"})

# Apply a saved layout
controller.command_handler.execute_command("apply-layout", {"name": "my_layout"})

# Find windows by title
chrome_windows = controller.command_handler.execute_command("find-window", {"title": "Chrome"})
```

## Testing

The Window Manager includes a comprehensive test suite that ensures functionality across all components. The tests are designed to run without interacting with the actual Windows API, using mock objects to simulate system behavior.

### Running Tests

```bash
# Install test dependencies
pip install -r requirements.txt

# Run all tests
pytest

# Run with coverage reporting
python run_tests.py
```

### Test Structure

-   **Unit Tests**: Test individual components in isolation
-   **Integration Tests**: Test components working together
-   **Mock System**: Simulate Windows API without actual system interactions

For more details, see the [testing documentation](tests/README.md).

## Architecture

The Window Manager follows a layered architecture design:

1. **Detection Layer** (`a_detection`): Responsible for detecting and identifying windows.
2. **Categorization Layer** (`b_categorization`): Classifies windows by type.
3. **Layout Layer** (`c_layout`): Handles saving and restoring window layouts.
4. **Automation Layer** (`d_automation`): Provides functions for manipulating windows.
5. **State Layer** (`e_state`): Manages application state and configuration.
6. **Interaction Layer** (`f_interaction`): Handles user interactions and commands.
7. **Controllers** (`g_controllers`): Coordinates between layers.

The `ApplicationController` acts as the central orchestrator, integrating all components and providing a unified command handling system.

## Project Structure

```
window-manager/
├── config/            # Configuration files
│   └── layouts/       # Saved window layouts
├── logs/              # Log files
├── src/               # Source code
│   ├── entrypoints/   # Entry points (CLI, GUI) - largely deprecated
│   ├── utils/         # Utility functions
│   ├── workflow/      # Core workflow components
│   │   ├── a_detection/      # Window detection
│   │   ├── b_categorization/ # Window categorization
│   │   ├── c_layout/         # Layout management
│   │   ├── d_automation/     # Window automation
│   │   ├── e_state/          # Application state management
│   │   ├── f_interaction/    # User interaction (hotkeys, commands)
│   │   └── g_controllers/    # Workflow controllers
│   └── main.py        # Main application entry point
├── tests/             # Test suite
│   ├── unit/          # Unit tests
│   ├── integration/   # Integration tests
│   ├── mocks/         # Mock implementations
│   └── fixtures/      # Test fixtures
└── requirements.txt   # Dependencies
```

## Dependencies

-   pywin32 - Windows API access
-   loguru - Improved logging
-   typing_extensions - Enhanced type hints
-   keyboard - Global hotkey support

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

Before submitting, please ensure:

-   All tests pass
-   New functionality includes tests
-   Code follows the project's style guidelines

## License

This project is licensed under the MIT License - see the LICENSE file for details.
