#!/usr/bin/env python3
"""
Test <PERSON> Sc<PERSON>t

Run this script to execute tests with coverage reporting.
"""

import os
import sys
import subprocess
from pathlib import Path


def run_tests():
    """Run pytest with coverage reporting."""
    print("Running tests with coverage reporting...")

    # Get the directory of this script
    script_dir = Path(__file__).parent.absolute()

    # Change to the script directory
    os.chdir(script_dir)

    # Build the pytest command
    cmd = [
        sys.executable, "-m", "pytest",
        "--cov=src",                      # Measure coverage for src directory
        "--cov-report=term",              # Report coverage to terminal
        "--cov-report=html:coverage_html" # Generate HTML coverage report
    ]

    # Run the tests
    result = subprocess.run(cmd)

    # Print coverage report location
    if result.returncode == 0:
        print("\nTests completed successfully!")

        coverage_dir = script_dir / "coverage_html"
        if coverage_dir.exists():
            print(f"Coverage report generated at: {coverage_dir}")
            print(f"Open {coverage_dir / 'index.html'} in a browser to view the report.")
    else:
        print("\nTests failed. Please check the output above for details.")

    return result.returncode


if __name__ == "__main__":
    sys.exit(run_tests())