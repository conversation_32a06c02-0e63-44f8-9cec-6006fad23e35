#!/usr/bin/env python3
"""
Window Manager CLI

DEPRECATION NOTICE:
This module is deprecated and will be removed in a future version.
Please use src/main.py as the primary entry point, which provides
all the same functionality through the ApplicationController.

Example migration:
- Instead of: python src/entrypoints/window_manager_cli.py --list
- Use: python src/main.py --list

This redirection helps maintain consistent behavior and ensures all
components (like hotkeys, notifications, etc.) are properly initialized.
"""

import os
import sys
import warnings
from pathlib import Path

# Add parent directory to path to allow imports from our package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import the logger
try:
    from loguru import logger
    logger.remove()
    logger.add(sys.stderr, level="INFO")
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

# Issue a deprecation warning
warnings.warn(
    "window_manager_cli.py is deprecated. Use src/main.py instead.",
    DeprecationWarning,
    stacklevel=2
)


def main():
    """Main entry point for the CLI."""
    print("""
DEPRECATION NOTICE:
This CLI entry point (window_manager_cli.py) is deprecated and will be removed in a future version.
Please use src/main.py as the primary entry point, which provides all the same functionality.

Example migration:
- Instead of: python src/entrypoints/window_manager_cli.py --list
- Use: python src/main.py --list
""")

    # Get the args being passed to this script
    args = sys.argv[1:]

    # Construct command to run main.py with the same args
    main_script = os.path.join(parent_dir, "main.py")
    main_cmd = [sys.executable, main_script] + args

    print(f"Redirecting to: {' '.join(main_cmd)}\n")

    # Execute main.py with the same arguments
    os.execv(sys.executable, main_cmd)


if __name__ == "__main__":
    main()