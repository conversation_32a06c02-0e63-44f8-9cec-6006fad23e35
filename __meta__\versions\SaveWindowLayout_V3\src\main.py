"""
Window Manager - Main Application

This is the main entry point for the Window Manager application.
It integrates all components of the system and provides the core functionality.

Usage:
    python -m src.main [options]

Options:
    --save NAME     Save current window layout to specified name
    --apply NAME    Apply window layout from specified name
    --list          List all open windows
    --list-layouts  List all saved layouts
    --monitor       Start monitoring active window (continuous mode)
    --find TITLE    Find windows by title substring
    --service       Run as a background service with hotkey support
    --debug         Enable debug logging
"""

import os
import sys
import argparse
import time
import signal
from pathlib import Path

# Configure the application paths
APP_DIR = Path(__file__).parent.parent
CONFIG_DIR = APP_DIR / "config"
LAYOUTS_DIR = CONFIG_DIR / "layouts"

# Create necessary directories
os.makedirs(LAYOUTS_DIR, exist_ok=True)

# Set up logging
try:
    from loguru import logger

    # Configure loguru logger
    log_file = APP_DIR / "logs" / "window_manager.log"
    os.makedirs(log_file.parent, exist_ok=True)

    logger.remove()  # Remove default logger
    logger.add(sys.stderr, level="INFO")  # Console output
    logger.add(
        log_file,
        rotation="10 MB",
        retention="1 week",
        level="DEBUG",
        backtrace=True,
        diagnose=True
    )

except ImportError:
    import logging

    # Configure standard logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

# Ensure we can access the package
sys.path.insert(0, str(APP_DIR))

# Import the controller to manage all operations
from src.workflow.g_controllers import ApplicationController


def setup_argparse():
    """Set up command line argument parsing."""
    parser = argparse.ArgumentParser(description="Window Manager Application")

    # Layout management
    parser.add_argument("--save", type=str, help="Save current window layout to specified name")
    parser.add_argument("--apply", type=str, help="Apply window layout from specified name")
    parser.add_argument("--list-layouts", action="store_true", help="List all saved layouts")

    # Window operations
    parser.add_argument("--list", action="store_true", help="List all open windows")
    parser.add_argument("--list-by-category", type=str, help="List windows by category")
    parser.add_argument("--find", type=str, help="Find windows by title substring")
    parser.add_argument("--monitor", action="store_true", help="Start monitoring active window")

    # Window arrangements
    parser.add_argument("--tile-horizontal", action="store_true", help="Tile windows horizontally")
    parser.add_argument("--tile-vertical", action="store_true", help="Tile windows vertically")
    parser.add_argument("--cascade", action="store_true", help="Cascade windows")

    # Service mode
    parser.add_argument("--service", action="store_true", help="Run as a background service with hotkey support")

    # Application settings
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    return parser


def monitor_active_window(controller):
    """
    Continuously monitor and display information about the active window.
    Press Ctrl+C to exit.
    """
    print("Monitoring active window. Press Ctrl+C to exit.\n")

    try:
        while True:
            # Use the controller's command handler to execute the monitor-window command
            controller.command_handler.execute_command("monitor-window")
            time.sleep(0.5)
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


def run_service(controller):
    """
    Run the application as a service with hotkey support.
    This function will block until Ctrl+C is pressed.
    """
    print("Starting Window Manager service with hotkey support.")
    print("Registered hotkeys:")

    # Get all registered hotkeys
    hotkeys = controller.hotkey_manager.get_all_hotkeys()
    for hotkey, description in hotkeys.items():
        print(f"  {hotkey}: {description}")

    print("\nPress Ctrl+C to exit.")

    # Set up signal handler for graceful shutdown
    def signal_handler(sig, frame):
        print("\nShutting down Window Manager service...")
        controller.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    # Start controller
    controller.start()

    # Keep program running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        controller.stop()
        print("\nWindow Manager service stopped.")


def main():
    """Main application entry point."""
    parser = setup_argparse()
    args = parser.parse_args()

    # Update logging level if debug mode is enabled
    if args.debug:
        try:
            logger.remove()
            logger.add(sys.stderr, level="DEBUG")
            logger.debug("Debug logging enabled")
        except:
            logging.getLogger().setLevel(logging.DEBUG)
            logger.debug("Debug logging enabled")

    # Initialize the application controller
    controller = ApplicationController(
        config_dir=str(CONFIG_DIR),
        headless=not args.service  # If running as a service, we want UI capabilities
    )

    try:
        # Process command line arguments
        if args.service:
            run_service(controller)
        elif args.monitor:
            monitor_active_window(controller)
        else:
            # Map CLI arguments to command handler commands
            if args.list:
                controller.command_handler.execute_command("list-windows")
            elif args.list_by_category:
                controller.command_handler.execute_command("list-windows-by-category",
                                                        {"category": args.list_by_category})
            elif args.find:
                controller.command_handler.execute_command("find-window",
                                                        {"title": args.find})
            elif args.list_layouts:
                controller.command_handler.execute_command("list-layouts")
            elif args.save:
                controller.command_handler.execute_command("save-layout",
                                                        {"name": args.save})
            elif args.apply:
                controller.command_handler.execute_command("apply-layout",
                                                        {"name": args.apply})
            elif args.tile_horizontal:
                controller.command_handler.execute_command("tile-horizontal")
            elif args.tile_vertical:
                controller.command_handler.execute_command("tile-vertical")
            elif args.cascade:
                controller.command_handler.execute_command("cascade")
            else:
                # No arguments provided, show help
                parser.print_help()
    finally:
        # Clean up controller resources
        controller.stop()


if __name__ == "__main__":
    main()