"""
Window Manager Workflow Package

This package contains the core workflow components of the window management system,
organized into stages that process windows in a sequential pipeline.

Main workflow stages:
1. Detection (a_detection): Discovers and extracts basic information about windows
2. Categorization (b_categorization): Classifies windows into categories and extracts specialized information
3. Layout (c_layout): Manages window layouts, including saving and restoring arrangements
4. Automation (d_automation): Provides functions for manipulating windows using Win32 API
5. State (e_state): Manages application state and persistence
6. Interaction (f_interaction): Handles user interaction and UI components
7. Controllers (g_controllers): Coordinates between different stages of the workflow

Each stage builds upon the previous one, transforming window information into increasingly
specialized and useful forms. The stages are orchestrated by controller components.
"""

# Set up package structure to make modules with alphabetical prefixes accessible
# This allows imports like "from ..detection" instead of "from ..a_detection"
import sys

# Map user-friendly module names to the actual module names with alphabetical prefixes
module_mapping = {
    f"{__name__}.detection": f"{__name__}.a_detection",
    f"{__name__}.categorization": f"{__name__}.b_categorization",
    f"{__name__}.layout": f"{__name__}.c_layout",
    f"{__name__}.automation": f"{__name__}.d_automation",
    f"{__name__}.state": f"{__name__}.e_state",
    f"{__name__}.interaction": f"{__name__}.f_interaction",
    f"{__name__}.controllers": f"{__name__}.g_controllers"
}

# Import key components for direct access
from .a_detection import WindowDetector, WindowInfo
from .b_categorization import WindowCategorizer, WindowCategory
from .c_layout import LayoutManager
from .d_automation import (
    move_window,
    position_window_with_state,
    tile_windows_horizontally,
    tile_windows_vertically,
    cascade_windows,
    grid_arrange_windows
)
from .g_controllers import WindowController

# Import the actual modules
for friendly_name, actual_name in module_mapping.items():
    try:
        # Import the module with the alphabetical prefix
        module = __import__(actual_name, fromlist=[''])
        # Create the alias
        sys.modules[friendly_name] = module
    except ImportError:
        # Module doesn't exist or couldn't be imported
        pass

__all__ = [
    # Key classes
    'WindowDetector',
    'WindowInfo',
    'WindowCategorizer',
    'WindowCategory',
    'LayoutManager',
    'WindowController',

    # Key functions
    'move_window',
    'position_window_with_state',
    'tile_windows_horizontally',
    'tile_windows_vertically',
    'cascade_windows',
    'grid_arrange_windows'
]