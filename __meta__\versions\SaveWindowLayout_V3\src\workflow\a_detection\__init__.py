"""
Window Detection Package

Contains modules for detecting windows and their basic properties.
This is the first stage in the window management workflow.
"""

from .window_detector import WindowDetector, WindowInfo, WindowPosition, WindowSize, WindowPlacement, ProcessInfo

__all__ = [
    'WindowDetector',
    'WindowInfo',
    'WindowPosition',
    'WindowSize',
    'WindowPlacement',
    'ProcessInfo'
]