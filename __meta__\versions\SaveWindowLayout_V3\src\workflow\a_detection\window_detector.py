"""
Window Detection Module

This module provides functionality to detect and gather information about all windows
in the system using Win32 API. It serves as the first stage in the window management
workflow, handling the raw detection of windows and collecting their basic properties.

Key responsibilities:
1. Enumerate all windows in the system
2. Collect basic window properties (hwnd, title, class, etc.)
3. Filter windows based on visibility, process, or other criteria
4. Provide a consistent data structure for detected windows

This module operates at the lowest level of the window management stack, focusing only
on detection and basic property gathering. Window categorization, specialized window
handling, and advanced operations are handled by subsequent workflow stages.
"""

import os
import sys
import time
from typing import Dict, List, Optional, Tuple, Set, Union, Callable
from dataclasses import dataclass, field
import ctypes
from ctypes import wintypes
import win32gui
import win32con
import win32process
import win32api

# Import the logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

# Define base data structures for window information
@dataclass
class WindowPosition:
    """Represents the position of a window on the screen."""
    x: int
    y: int

    def as_tuple(self) -> Tuple[int, int]:
        """Return the position as a tuple."""
        return (self.x, self.y)

@dataclass
class WindowSize:
    """Represents the size of a window."""
    width: int
    height: int

    def as_tuple(self) -> Tuple[int, int]:
        """Return the size as a tuple."""
        return (self.width, self.height)

@dataclass
class WindowPlacement:
    """Represents the full placement information of a window."""
    flags: int
    show_cmd: int
    min_position: Tuple[int, int]
    max_position: Tuple[int, int]
    normal_position: Tuple[int, int, int, int]

    @classmethod
    def from_win32_placement(cls, placement):
        """Create a WindowPlacement from a win32 WINDOWPLACEMENT structure."""
        return cls(
            flags=placement.flags,
            show_cmd=placement.showCmd,
            min_position=(placement.ptMinPosition.x, placement.ptMinPosition.y),
            max_position=(placement.ptMaxPosition.x, placement.ptMaxPosition.y),
            normal_position=(
                placement.rcNormalPosition.left,
                placement.rcNormalPosition.top,
                placement.rcNormalPosition.right,
                placement.rcNormalPosition.bottom
            )
        )

@dataclass
class ProcessInfo:
    """Information about the process that owns a window."""
    pid: int
    exe_path: str = ""
    exe_name: str = ""

    def __post_init__(self):
        """Initialize additional fields after creation."""
        if self.pid and not self.exe_path:
            try:
                # Try to get the executable path from the process ID
                hProcess = win32api.OpenProcess(
                    win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ,
                    False, self.pid
                )
                if hProcess:
                    self.exe_path = win32process.GetModuleFileNameEx(hProcess, 0)
                    self.exe_name = os.path.basename(self.exe_path)
            except Exception as e:
                logger.debug(f"Error getting process info for PID {self.pid}: {e}")


@dataclass
class WindowInfo:
    """Basic information about a window."""
    hwnd: int
    title: str = ""
    class_name: str = ""
    is_visible: bool = False
    is_enabled: bool = False
    position: WindowPosition = field(default_factory=lambda: WindowPosition(0, 0))
    size: WindowSize = field(default_factory=lambda: WindowSize(0, 0))
    placement: Optional[WindowPlacement] = None
    process: Optional[ProcessInfo] = None
    parent_hwnd: int = 0

    def __post_init__(self):
        """Initialize fields that weren't provided."""
        if not self.title and self.hwnd:
            self.title = win32gui.GetWindowText(self.hwnd)

        if not self.class_name and self.hwnd:
            self.class_name = win32gui.GetClassName(self.hwnd)

        if self.hwnd:
            self.is_visible = bool(win32gui.IsWindowVisible(self.hwnd))
            self.is_enabled = bool(win32gui.IsWindowEnabled(self.hwnd))

            # Get window position and size
            try:
                rect = win32gui.GetWindowRect(self.hwnd)
                self.position = WindowPosition(rect[0], rect[1])
                self.size = WindowSize(rect[2] - rect[0], rect[3] - rect[1])
            except Exception as e:
                logger.debug(f"Error getting window rect for {self.hwnd}: {e}")

            # Get window placement
            try:
                placement = win32gui.GetWindowPlacement(self.hwnd)
                if placement:
                    placement_struct = win32gui.WINDOWPLACEMENT()
                    placement_struct.flags = placement[0]
                    placement_struct.showCmd = placement[1]
                    placement_struct.ptMinPosition = placement[2]
                    placement_struct.ptMaxPosition = placement[3]
                    placement_struct.rcNormalPosition = placement[4]
                    self.placement = WindowPlacement.from_win32_placement(placement_struct)
            except Exception as e:
                logger.debug(f"Error getting window placement for {self.hwnd}: {e}")

            # Get process info
            try:
                _, pid = win32process.GetWindowThreadProcessId(self.hwnd)
                if pid:
                    self.process = ProcessInfo(pid=pid)
            except Exception as e:
                logger.debug(f"Error getting thread/process ID for {self.hwnd}: {e}")

            # Get parent window
            try:
                self.parent_hwnd = win32gui.GetParent(self.hwnd)
            except Exception as e:
                logger.debug(f"Error getting parent for {self.hwnd}: {e}")


class WindowDetector:
    """
    Detects and enumerates windows in the system.

    This class provides methods to find and collect information about all
    windows in the system, with options to filter by various criteria.
    """

    def __init__(self):
        """Initialize the window detector."""
        self._windows_info = {}
        self._last_refresh_time = 0

    def detect_windows(self, visible_only: bool = True) -> Dict[int, WindowInfo]:
        """
        Refresh the collection of windows.

        Args:
            visible_only: If True, only collect visible windows

        Returns:
            Dictionary mapping window handles to WindowInfo objects
        """
        self._windows_info = {}

        def enum_windows_callback(hwnd, results):
            """Callback for EnumWindows."""
            if visible_only and not win32gui.IsWindowVisible(hwnd):
                return True

            # Skip certain system windows or windows without titles
            if visible_only:
                title = win32gui.GetWindowText(hwnd)
                if not title:
                    return True

            # Create a WindowInfo object for this window
            window_info = WindowInfo(hwnd=hwnd)
            self._windows_info[hwnd] = window_info
            return True

        try:
            win32gui.EnumWindows(enum_windows_callback, None)
            logger.debug(f"Found {len(self._windows_info)} windows")
            self._last_refresh_time = time.time()
        except Exception as e:
            logger.error(f"Error enumerating windows: {e}")

        return self._windows_info

    # Alias for backward compatibility
    refresh = detect_windows

    def get_window_info(self, hwnd: int) -> Optional[WindowInfo]:
        """
        Get information about a specific window by handle.

        Args:
            hwnd: Window handle

        Returns:
            WindowInfo object if the window exists, None otherwise
        """
        if hwnd in self._windows_info:
            return self._windows_info[hwnd]

        # Window not in cache, try to get it directly
        if win32gui.IsWindow(hwnd):
            window_info = WindowInfo(hwnd=hwnd)
            self._windows_info[hwnd] = window_info
            return window_info

        return None

    def get_active_window(self) -> Optional[WindowInfo]:
        """
        Get the currently active window.

        Returns:
            WindowInfo for the active window, or None if no active window
        """
        try:
            hwnd = win32gui.GetForegroundWindow()
            return self.get_window_info(hwnd)
        except Exception as e:
            logger.error(f"Error getting active window: {e}")
            return None

    def find_windows_by_title(self, title: str, exact_match: bool = False) -> List[WindowInfo]:
        """
        Find windows by their title.

        Args:
            title: Title to search for
            exact_match: If True, require an exact match, otherwise use substring

        Returns:
            List of matching WindowInfo objects
        """
        result = []
        title_lower = title.lower()

        for hwnd, info in self._windows_info.items():
            window_title = info.title.lower()
            if (exact_match and window_title == title_lower) or \
               (not exact_match and title_lower in window_title):
                result.append(info)

        return result

    def find_windows_by_class(self, class_name: str) -> List[WindowInfo]:
        """
        Find windows by their class name.

        Args:
            class_name: Class name to search for

        Returns:
            List of matching WindowInfo objects
        """
        result = []
        class_name_lower = class_name.lower()

        for hwnd, info in self._windows_info.items():
            window_class = info.class_name.lower()
            if class_name_lower in window_class:
                result.append(info)

        return result

    def find_windows_by_process(self, process_name: str) -> List[WindowInfo]:
        """
        Find windows belonging to a specific process.

        Args:
            process_name: Process name to search for

        Returns:
            List of matching WindowInfo objects
        """
        result = []
        process_name_lower = process_name.lower()

        for hwnd, info in self._windows_info.items():
            if info.process and info.process.exe_name:
                if process_name_lower in info.process.exe_name.lower():
                    result.append(info)

        return result

    def get_all_windows(self) -> Dict[int, WindowInfo]:
        """
        Get all detected windows.

        Returns:
            Dictionary mapping window handles to WindowInfo objects
        """
        return self._windows_info

    def get_last_refresh_time(self) -> float:
        """
        Get the timestamp of the last window refresh.

        Returns:
            Timestamp as a float
        """
        return self._last_refresh_time