"""
Window Categorization Module

This module builds upon the window detection functionality to categorize windows into
specific types based on their class names, process information, and other properties.
It serves as the second stage in the window management workflow, handling the intelligent
classification of windows to enable specialized handling in subsequent stages.

Key responsibilities:
1. Define window categories (Explorer, Browser, Office, etc.)
2. Categorize detected windows based on their properties
3. Provide specialized window type classes with enhanced functionality
4. Extract additional properties specific to each window type

This module works with the window detection data to add semantic meaning and
categorization, enabling more sophisticated operations in later workflow stages.
"""

import os
import sys
import re
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Set, Union, Callable
from dataclasses import dataclass, field

# Import from our detection module using relative imports
from ..a_detection.window_detector import WindowInfo, WindowDetector

# Import the logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

# Define window categories
class WindowCategory(Enum):
    """Enumeration of window categories."""
    UNKNOWN = auto()
    EXPLORER = auto()
    BROWSER = auto()
    DOCUMENT = auto()
    DEVELOPMENT = auto()
    MEDIA = auto()
    COMMUNICATION = auto()
    SYSTEM = auto()
    UTILITY = auto()


# Browser-specific window types
class BrowserType(Enum):
    """Enumeration of known browser types."""
    UNKNOWN = auto()
    CHROME = auto()
    EDGE = auto()
    FIREFOX = auto()
    SAFARI = auto()
    OPERA = auto()
    BRAVE = auto()
    VIVALDI = auto()


@dataclass
class CategorizedWindowInfo:
    """Enhanced window information with categorization."""
    base_info: WindowInfo
    category: WindowCategory = WindowCategory.UNKNOWN

    @property
    def hwnd(self) -> int:
        """Get the window handle."""
        return self.base_info.hwnd

    @property
    def title(self) -> str:
        """Get the window title."""
        return self.base_info.title

    @property
    def class_name(self) -> str:
        """Get the window class name."""
        return self.base_info.class_name

    @property
    def process_name(self) -> str:
        """Get the process name, if available."""
        if self.base_info.process and self.base_info.process.exe_name:
            return self.base_info.process.exe_name
        return ""


@dataclass
class ExplorerWindowInfo(CategorizedWindowInfo):
    """Information specific to Explorer windows."""
    location_path: str = ""
    is_desktop: bool = False
    view_mode: str = ""
    selected_items: List[str] = field(default_factory=list)

    def __post_init__(self):
        """Initialize Explorer-specific properties."""
        self.category = WindowCategory.EXPLORER

        # Detect if this is the desktop window
        if self.class_name == "WorkerW" or self.class_name == "Progman":
            self.is_desktop = True

        # Try to extract location path from title
        if " - " in self.title:
            self.location_path = self.title.split(" - ")[0]


@dataclass
class BrowserWindowInfo(CategorizedWindowInfo):
    """Information specific to web browser windows."""
    browser_type: BrowserType = BrowserType.UNKNOWN
    url: str = ""
    is_private_mode: bool = False

    def __post_init__(self):
        """Initialize browser-specific properties."""
        self.category = WindowCategory.BROWSER

        # Detect browser type based on class and process
        process_name = self.process_name.lower()
        class_name = self.class_name.lower()

        if "chrome" in process_name or "chrome" in class_name:
            self.browser_type = BrowserType.CHROME
        elif "msedge" in process_name or "edge" in class_name:
            self.browser_type = BrowserType.EDGE
        elif "firefox" in process_name or "mozilla" in class_name:
            self.browser_type = BrowserType.FIREFOX
        elif "safari" in process_name:
            self.browser_type = BrowserType.SAFARI
        elif "opera" in process_name:
            self.browser_type = BrowserType.OPERA
        elif "brave" in process_name:
            self.browser_type = BrowserType.BRAVE

        # Try to extract URL or page info from title
        # Most browsers put page title first, then browser name
        if " - " in self.title and not self.title.startswith("http"):
            # Extract page title (assume format "Page Title - Browser Name")
            pass


@dataclass
class DocumentWindowInfo(CategorizedWindowInfo):
    """Information specific to document editing windows (Word, Excel, etc.)."""
    app_name: str = ""
    document_path: str = ""
    document_name: str = ""
    is_modified: bool = False

    def __post_init__(self):
        """Initialize document-specific properties."""
        self.category = WindowCategory.DOCUMENT

        # Detect Microsoft Office applications
        process_name = self.process_name.lower()
        if "winword" in process_name:
            self.app_name = "Microsoft Word"
        elif "excel" in process_name:
            self.app_name = "Microsoft Excel"
        elif "powerpnt" in process_name:
            self.app_name = "Microsoft PowerPoint"
        elif "acrobat" in process_name or "reader" in process_name:
            self.app_name = "Adobe Reader/Acrobat"

        # Try to extract document name from title
        # Most document apps use format "DocumentName - AppName"
        if " - " in self.title:
            parts = self.title.split(" - ")
            # Check if the document is modified (usually indicated by * prefix)
            if parts[0].startswith("*"):
                self.is_modified = True
                self.document_name = parts[0][1:]  # Remove the asterisk
            else:
                self.document_name = parts[0]


@dataclass
class DevelopmentWindowInfo(CategorizedWindowInfo):
    """Information specific to development IDE windows."""
    ide_name: str = ""
    project_name: str = ""
    file_path: str = ""

    def __post_init__(self):
        """Initialize development-specific properties."""
        self.category = WindowCategory.DEVELOPMENT

        # Detect common IDEs
        process_name = self.process_name.lower()
        if "devenv" in process_name:
            self.ide_name = "Visual Studio"
        elif "code" in process_name:
            self.ide_name = "VS Code"
        elif "pycharm" in process_name:
            self.ide_name = "PyCharm"
        elif "intellij" in process_name:
            self.ide_name = "IntelliJ IDEA"
        elif "rider" in process_name:
            self.ide_name = "Rider"
        elif "sublimetext" in process_name:
            self.ide_name = "Sublime Text"
        elif "atom" in process_name:
            self.ide_name = "Atom"

        # Try to extract project/file information from title
        # Typical IDE title format: "FileName - ProjectName - IDE"
        parts = self.title.split(" - ")
        if len(parts) > 1:
            self.file_path = parts[0]
            if len(parts) > 2:
                self.project_name = parts[1]


@dataclass
class MediaWindowInfo(CategorizedWindowInfo):
    """Information specific to media player windows."""
    media_type: str = ""  # video, audio, image
    media_path: str = ""
    is_playing: bool = False

    def __post_init__(self):
        """Initialize media-specific properties."""
        self.category = WindowCategory.MEDIA

        # Detect common media applications
        process_name = self.process_name.lower()
        if "vlc" in process_name:
            self.media_type = "video"
        elif "wmplayer" in process_name:
            self.media_type = "video"
        elif "spotify" in process_name:
            self.media_type = "audio"
        elif "groove" in process_name:
            self.media_type = "audio"
        elif "photos" in process_name:
            self.media_type = "image"
        elif "paint" in process_name:
            self.media_type = "image"


@dataclass
class CommunicationWindowInfo(CategorizedWindowInfo):
    """Information specific to communication windows (email, chat, etc.)."""
    comm_type: str = ""  # email, chat, video-conference
    app_name: str = ""

    def __post_init__(self):
        """Initialize communication-specific properties."""
        self.category = WindowCategory.COMMUNICATION

        # Detect common communication applications
        process_name = self.process_name.lower()
        if "outlook" in process_name:
            self.comm_type = "email"
            self.app_name = "Microsoft Outlook"
        elif "thunderbird" in process_name:
            self.comm_type = "email"
            self.app_name = "Thunderbird"
        elif "teams" in process_name:
            self.comm_type = "chat"
            self.app_name = "Microsoft Teams"
        elif "slack" in process_name:
            self.comm_type = "chat"
            self.app_name = "Slack"
        elif "discord" in process_name:
            self.comm_type = "chat"
            self.app_name = "Discord"
        elif "zoom" in process_name:
            self.comm_type = "video-conference"
            self.app_name = "Zoom"
        elif "webex" in process_name:
            self.comm_type = "video-conference"
            self.app_name = "Webex"


@dataclass
class SystemWindowInfo(CategorizedWindowInfo):
    """Information specific to system windows (Control Panel, Task Manager, etc.)."""
    system_type: str = ""

    def __post_init__(self):
        """Initialize system-specific properties."""
        self.category = WindowCategory.SYSTEM

        # Detect common system windows
        title_lower = self.title.lower()
        if "control panel" in title_lower:
            self.system_type = "control-panel"
        elif "task manager" in title_lower:
            self.system_type = "task-manager"
        elif "settings" in title_lower:
            self.system_type = "settings"
        elif "registry editor" in title_lower:
            self.system_type = "registry-editor"
        elif "device manager" in title_lower:
            self.system_type = "device-manager"


@dataclass
class UtilityWindowInfo(CategorizedWindowInfo):
    """Information specific to utility windows (calculators, notepads, etc.)."""
    utility_type: str = ""

    def __post_init__(self):
        """Initialize utility-specific properties."""
        self.category = WindowCategory.UTILITY

        # Detect common utility applications
        process_name = self.process_name.lower()
        title_lower = self.title.lower()
        if "calculator" in process_name or "calc" in title_lower:
            self.utility_type = "calculator"
        elif "notepad" in process_name:
            self.utility_type = "text-editor"
        elif "wordpad" in process_name:
            self.utility_type = "text-editor"
        elif "calendar" in process_name:
            self.utility_type = "calendar"
        elif "clock" in process_name:
            self.utility_type = "clock"
        elif "terminal" in process_name or "cmd.exe" in process_name or "powershell" in process_name:
            self.utility_type = "terminal"


class WindowCategorizer:
    """
    Categorizes windows based on their properties.

    This class analyzes WindowInfo objects and categorizes them into specific types,
    creating specialized window info objects with additional properties.
    """

    def __init__(self, detector: Optional[WindowDetector] = None):
        """
        Initialize the window categorizer.

        Args:
            detector: Optional WindowDetector instance to use for detection
        """
        self.detector = detector or WindowDetector()
        self._categorized_windows = {}
        self._category_map = {}
        self._last_refresh_time = 0

    def refresh(self) -> Dict[int, CategorizedWindowInfo]:
        """
        Refresh the window detection and categorization.

        Returns:
            Dictionary mapping window handles to categorized window info objects
        """
        # Refresh window detection
        windows = self.detector.detect_windows()

        # Clear existing categorizations
        self._categorized_windows = {}
        self._category_map = {cat: set() for cat in WindowCategory}

        # Categorize each window
        for hwnd, window_info in windows.items():
            categorized_info = self._categorize_window(window_info)
            self._categorized_windows[hwnd] = categorized_info
            self._category_map[categorized_info.category].add(hwnd)

        logger.debug(f"Categorized {len(self._categorized_windows)} windows")
        return self._categorized_windows

    def _categorize_window(self, window_info: WindowInfo) -> CategorizedWindowInfo:
        """
        Categorize a single window based on its properties.

        Args:
            window_info: WindowInfo object to categorize

        Returns:
            Specialized CategorizedWindowInfo object
        """
        # Get basic properties
        class_name = window_info.class_name.lower()
        title = window_info.title.lower()
        process_name = ""
        if window_info.process and window_info.process.exe_name:
            process_name = window_info.process.exe_name.lower()

        # Explorer windows
        if class_name in ["cabinetwclass", "#32770", "explorewclass"] or \
           "explorer.exe" in process_name and not title.startswith("desktop"):
            return ExplorerWindowInfo(base_info=window_info)

        # Browser windows
        browser_classes = ["chrome", "mozillawindowclass", "edgewindow", "microsoftedgeframe"]
        browser_processes = ["chrome.exe", "firefox.exe", "msedge.exe", "safari.exe", "opera.exe", "brave.exe"]
        if any(bc in class_name for bc in browser_classes) or any(bp in process_name for bp in browser_processes):
            return BrowserWindowInfo(base_info=window_info)

        # Document windows
        document_processes = ["winword.exe", "excel.exe", "powerpnt.exe", "acrobat.exe", "acrord32.exe"]
        document_titles = ["word", "excel", "powerpoint", "office", "document", "spreadsheet", "presentation"]
        if any(dp in process_name for dp in document_processes) or \
           any(dt in title for dt in document_titles) and title.endswith(".docx") or \
           title.endswith(".xlsx") or title.endswith(".pptx") or title.endswith(".pdf"):
            return DocumentWindowInfo(base_info=window_info)

        # Development windows
        dev_processes = ["devenv.exe", "code.exe", "pycharm", "intellij", "rider", "sublimetext", "atom.exe"]
        dev_titles = ["visual studio", "vs code", "pycharm", "intellij", "rider", "sublime text", "atom"]
        if any(dp in process_name for dp in dev_processes) or any(dt in title for dt in dev_titles):
            return DevelopmentWindowInfo(base_info=window_info)

        # Media windows
        media_processes = ["vlc.exe", "wmplayer.exe", "spotify.exe", "groove", "photos", "paint"]
        media_titles = ["player", "video", "audio", "music", "photo", "picture", "image"]
        if any(mp in process_name for mp in media_processes) or any(mt in title for mt in media_titles):
            return MediaWindowInfo(base_info=window_info)

        # Communication windows
        comm_processes = ["outlook.exe", "thunderbird.exe", "teams.exe", "slack.exe", "discord.exe", "zoom.exe", "webex"]
        comm_titles = ["email", "mail", "chat", "message", "call", "meeting", "conference"]
        if any(cp in process_name for cp in comm_processes) or any(ct in title for ct in comm_titles):
            return CommunicationWindowInfo(base_info=window_info)

        # System windows
        system_titles = ["control panel", "task manager", "settings", "registry editor", "device manager"]
        if "shell_traywnd" in class_name or "progman" in class_name or any(st in title for st in system_titles):
            return SystemWindowInfo(base_info=window_info)

        # Utility windows
        utility_processes = ["calculator.exe", "notepad.exe", "wordpad.exe", "cmd.exe", "powershell.exe"]
        utility_titles = ["calculator", "notepad", "terminal", "command", "console"]
        if any(up in process_name for up in utility_processes) or any(ut in title for ut in utility_titles):
            return UtilityWindowInfo(base_info=window_info)

        # Default: Unknown category
        return CategorizedWindowInfo(base_info=window_info)

    def get_windows_by_category(self, category: WindowCategory) -> Dict[int, CategorizedWindowInfo]:
        """
        Get all windows of a specific category.

        Args:
            category: WindowCategory to filter by

        Returns:
            Dictionary mapping window handles to categorized window info objects
        """
        result = {}
        for hwnd in self._category_map.get(category, set()):
            result[hwnd] = self._categorized_windows[hwnd]
        return result

    def get_window_info(self, hwnd: int) -> Optional[CategorizedWindowInfo]:
        """
        Get categorized window info for a specific window handle.

        Args:
            hwnd: Window handle

        Returns:
            CategorizedWindowInfo object if found, None otherwise
        """
        return self._categorized_windows.get(hwnd)

    def find_windows_by_title(self, title: str, exact_match: bool = False) -> List[CategorizedWindowInfo]:
        """
        Find categorized windows by title.

        Args:
            title: Title to search for
            exact_match: Whether to require an exact match

        Returns:
            List of matching CategorizedWindowInfo objects
        """
        result = []
        title_lower = title.lower()

        for hwnd, info in self._categorized_windows.items():
            window_title = info.title.lower()
            if (exact_match and window_title == title_lower) or \
               (not exact_match and title_lower in window_title):
                result.append(info)

        return result

    def find_windows_by_process(self, process_name: str) -> List[CategorizedWindowInfo]:
        """
        Find categorized windows by process name.

        Args:
            process_name: Process name to search for

        Returns:
            List of matching CategorizedWindowInfo objects
        """
        result = []
        process_name_lower = process_name.lower()

        for hwnd, info in self._categorized_windows.items():
            if info.process_name.lower() and process_name_lower in info.process_name.lower():
                result.append(info)

        return result