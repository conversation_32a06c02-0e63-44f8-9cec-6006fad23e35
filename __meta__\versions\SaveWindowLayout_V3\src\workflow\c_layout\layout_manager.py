"""
Layout Manager Module

This module provides functionality for managing window layouts, including saving
and restoring window arrangements. It's the third stage in the window management workflow.

Key responsibilities:
1. Save current window layouts to storage
2. Load window layouts from storage
3. Apply layouts to restore window arrangements
4. Provide layout manipulation operations
"""

import os
import json
import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Set

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class LayoutManager:
    """
    Manages window layouts, including saving and restoring window arrangements.

    This class provides functionality to save the current state of windows,
    load previously saved layouts, and apply layouts to restore window positions.
    """

    def __init__(self, layouts_dir: Optional[str] = None):
        """
        Initialize the layout manager.

        Args:
            layouts_dir: Optional directory for storing layout files
        """
        self.layouts_dir = Path(layouts_dir) if layouts_dir else Path("config/layouts")

        # Create layouts directory if it doesn't exist
        os.makedirs(self.layouts_dir, exist_ok=True)

        logger.debug(f"LayoutManager initialized with layouts directory: {self.layouts_dir}")

    def save_layout(self, windows: Dict[int, Any], name: Optional[str] = None) -> str:
        """
        Save the current window layout to a file.

        Args:
            windows: Dictionary of window information
            name: Optional name for the layout file

        Returns:
            Path to the saved layout file
        """
        # Generate filename if not provided
        if not name:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            name = f"layout_{timestamp}.json"
        elif not name.endswith(".json"):
            name = f"{name}.json"

        # Create a simple layout structure (this is a placeholder implementation)
        layout = {
            "name": name,
            "timestamp": str(datetime.datetime.now()),
            "windows": []
        }

        # Add each window to the layout
        for hwnd, window in windows.items():
            window_dict = {
                "hwnd": hwnd,
                "title": window.title,
                "category": window.category.name,
                "position": [window.base_info.position.x, window.base_info.position.y],
                "size": [window.base_info.size.width, window.base_info.size.height]
            }

            # Add process info if available
            if window.base_info.process and window.base_info.process.exe_name:
                window_dict["process_name"] = window.base_info.process.exe_name

            # Add class name info
            window_dict["class_name"] = window.base_info.class_name

            layout["windows"].append(window_dict)

        # Save to file
        file_path = self.layouts_dir / name
        try:
            with open(file_path, 'w') as f:
                json.dump(layout, f, indent=2)
            logger.info(f"Saved layout with {len(layout['windows'])} windows to {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"Error saving layout to {file_path}: {e}")
            return ""

    def apply_layout(self,
                    layout_name: str,
                    restore_explorer: bool = True,
                    restore_browsers: bool = True,
                    restore_documents: bool = True,
                    restore_others: bool = True) -> int:
        """
        Apply a saved window layout to restore window positions and sizes.

        Args:
            layout_name: Name of the layout to apply or full path to layout file
            restore_explorer: Whether to restore Explorer windows
            restore_browsers: Whether to restore browser windows
            restore_documents: Whether to restore document windows
            restore_others: Whether to restore other windows

        Returns:
            Number of windows successfully restored
        """
        import win32gui
        import win32con
        import win32process
        import time

        logger.info(f"Applying layout: {layout_name}")

        # Handle both relative and absolute paths
        if os.path.isabs(layout_name):
            file_path = Path(layout_name)
        else:
            file_path = self.layouts_dir / layout_name
            if not file_path.exists() and not layout_name.endswith(".json"):
                file_path = self.layouts_dir / f"{layout_name}.json"

        # Check if file exists
        if not file_path.exists():
            logger.error(f"Layout file not found: {file_path}")
            return 0

        # Load the layout file
        try:
            with open(file_path, 'r') as f:
                layout = json.load(f)

            window_count = len(layout.get("windows", []))
            logger.info(f"Loaded layout with {window_count} windows")

            # Get current windows
            current_windows = self._get_current_windows()
            logger.debug(f"Found {len(current_windows)} current windows")

            # Apply the layout
            restored_count = 0

            for saved_window in layout.get("windows", []):
                # Check if we should restore this window type
                category = saved_window.get("category", "UNKNOWN")

                if (category == "EXPLORER" and not restore_explorer or
                    category == "BROWSER" and not restore_browsers or
                    category == "DOCUMENT" and not restore_documents or
                    (category not in ("EXPLORER", "BROWSER", "DOCUMENT") and not restore_others)):
                    logger.debug(f"Skipping window of category {category} (disabled in options)")
                    continue

                # Try to find matching window
                target_hwnd = self._find_matching_window(saved_window, current_windows)

                if not target_hwnd:
                    logger.debug(f"No matching window found for: {saved_window.get('title', 'Unknown')}")
                    continue

                # Get window info
                title = saved_window.get("title", "Unknown")
                position = saved_window.get("position", [0, 0])
                size = saved_window.get("size", [800, 600])

                logger.debug(f"Positioning window '{title}' to ({position[0]}, {position[1]}) with size {size[0]}x{size[1]}")

                try:
                    # Set window position and size
                    # First check if window is minimized or maximized
                    placement = win32gui.GetWindowPlacement(target_hwnd)
                    show_cmd = placement[1]  # showCmd

                    if show_cmd == win32con.SW_SHOWMINIMIZED:
                        # Restore the window if it's minimized
                        win32gui.ShowWindow(target_hwnd, win32con.SW_RESTORE)
                        time.sleep(0.1)  # Give it a moment to restore

                    if show_cmd != win32con.SW_SHOWMAXIMIZED:
                        # Only resize if not maximized
                        win32gui.MoveWindow(
                            target_hwnd,
                            position[0],
                            position[1],
                            size[0],
                            size[1],
                            True  # Redraw
                        )

                    # Ensure the window is visible
                    if not win32gui.IsWindowVisible(target_hwnd):
                        win32gui.ShowWindow(target_hwnd, win32con.SW_SHOW)

                    restored_count += 1
                    logger.info(f"Successfully positioned window: {title}")
                except Exception as e:
                    logger.error(f"Error positioning window '{title}': {e}")

            logger.info(f"Layout applied, restored {restored_count} out of {window_count} windows")
            return restored_count

        except Exception as e:
            logger.error(f"Error applying layout: {e}")
            return 0

    def _get_current_windows(self) -> Dict[int, Dict[str, Any]]:
        """
        Get a dictionary of all current windows.

        Returns:
            Dictionary mapping window handles to window info
        """
        import win32gui
        import win32process

        current_windows = {}

        def enum_windows_callback(hwnd, results):
            if not win32gui.IsWindowVisible(hwnd):
                return True

            # Skip windows without titles
            title = win32gui.GetWindowText(hwnd)
            if not title:
                return True

            try:
                # Get window rect
                rect = win32gui.GetWindowRect(hwnd)
                position = [rect[0], rect[1]]
                size = [rect[2] - rect[0], rect[3] - rect[1]]

                # Get process name
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                process_name = self._get_process_name(pid)

                # Get class name
                class_name = win32gui.GetClassName(hwnd)

                # Store window info
                current_windows[hwnd] = {
                    "hwnd": hwnd,
                    "title": title,
                    "position": position,
                    "size": size,
                    "pid": pid,
                    "process_name": process_name,
                    "class_name": class_name
                }
            except Exception as e:
                logger.debug(f"Error collecting window info for {hwnd}: {e}")

            return True

        win32gui.EnumWindows(enum_windows_callback, None)
        return current_windows

    def _get_process_name(self, pid: int) -> str:
        """
        Get the name of a process from its PID.

        Args:
            pid: Process ID

        Returns:
            Process name
        """
        try:
            import psutil
            process = psutil.Process(pid)
            return os.path.basename(process.exe()) if process else ""
        except Exception as e:
            logger.debug(f"Error getting process name for PID {pid}: {e}")
            return ""

    def _find_matching_window(self, saved_window: Dict[str, Any],
                             current_windows: Dict[int, Dict[str, Any]]) -> int:
        """
        Find a window in the current set that matches a saved window.

        Args:
            saved_window: Window info from a saved layout
            current_windows: Dictionary of current windows

        Returns:
            Handle of matching window or 0 if no match
        """
        # First check if the same HWND still exists (unlikely)
        saved_hwnd = saved_window.get("hwnd", 0)
        if saved_hwnd in current_windows:
            logger.debug(f"Found direct HWND match for {saved_window.get('title', 'Unknown')}")
            return saved_hwnd

        # Different strategies for different window types
        category = saved_window.get("category", "UNKNOWN")
        if category == "EXPLORER":
            return self._find_matching_explorer(saved_window, current_windows)
        elif category == "BROWSER":
            return self._find_matching_browser(saved_window, current_windows)
        else:
            return self._find_matching_generic(saved_window, current_windows)

    def _find_matching_explorer(self, saved_window: Dict[str, Any],
                               current_windows: Dict[int, Dict[str, Any]]) -> int:
        """
        Find a matching Explorer window.

        Args:
            saved_window: Window info from a saved layout
            current_windows: Dictionary of current windows

        Returns:
            Handle of matching window or 0 if no match
        """
        saved_title = saved_window.get("title", "").lower()
        saved_path = saved_title.split(" - ")[0] if " - " in saved_title else saved_title

        # Look for windows with matching path in title
        for hwnd, window in current_windows.items():
            process_name = window.get("process_name", "").lower()
            if "explorer.exe" not in process_name:
                continue

            title = window.get("title", "").lower()
            if saved_path and saved_path in title:
                return hwnd

        return 0

    def _find_matching_browser(self, saved_window: Dict[str, Any],
                              current_windows: Dict[int, Dict[str, Any]]) -> int:
        """
        Find a matching browser window, with special handling for browser-specific matching.

        Args:
            saved_window: Window info from a saved layout
            current_windows: Dictionary of current windows

        Returns:
            Handle of matching window or 0 if no match
        """
        saved_title = saved_window.get("title", "").lower()
        saved_process = saved_window.get("process_name", "").lower()
        saved_class = saved_window.get("class_name", "").lower()

        browser_types = {
            "chrome": ["chrome.exe", "chrome"],
            "edge": ["msedge.exe", "edge"],
            "firefox": ["firefox.exe", "mozilla"],
            "safari": ["safari.exe", "safari"],
            "opera": ["opera.exe", "opera"],
            "brave": ["brave.exe", "brave"]
        }

        # Determine the browser type from the saved window
        browser_type = None
        for bt, identifiers in browser_types.items():
            if any(ident in saved_process or ident in saved_class for ident in identifiers):
                browser_type = bt
                break

        # Find a matching browser window
        matches = []
        for hwnd, window in current_windows.items():
            process_name = window.get("process_name", "").lower()
            class_name = window.get("class_name", "").lower()

            # Must be same browser type
            if browser_type:
                if not any(ident in process_name or ident in class_name
                          for ident in browser_types.get(browser_type, [])):
                    continue

            # Add to potential matches
            matches.append(hwnd)

            # If title matches exactly, this is likely the right window
            if saved_title and saved_title == window.get("title", "").lower():
                return hwnd

        # Return first match if any found
        return matches[0] if matches else 0

    def _find_matching_generic(self, saved_window: Dict[str, Any],
                              current_windows: Dict[int, Dict[str, Any]]) -> int:
        """
        Generic window matching for other window types.

        Args:
            saved_window: Window info from a saved layout
            current_windows: Dictionary of current windows

        Returns:
            Handle of matching window or 0 if no match
        """
        saved_title = saved_window.get("title", "").lower()
        saved_process = saved_window.get("process_name", "").lower()
        saved_class = saved_window.get("class_name", "").lower()

        # Score each window
        best_match = None
        best_score = 0

        for hwnd, window in current_windows.items():
            title = window.get("title", "").lower()
            process_name = window.get("process_name", "").lower()
            class_name = window.get("class_name", "").lower()

            # Calculate match score
            score = 0

            # Process name is most important
            if saved_process and saved_process == process_name:
                score += 10
            elif saved_process and saved_process in process_name:
                score += 5

            # Class name is next most important
            if saved_class and saved_class == class_name:
                score += 5
            elif saved_class and saved_class in class_name:
                score += 3

            # Title is least important but still relevant
            if saved_title and saved_title == title:
                score += 5
            elif saved_title and saved_title in title:
                score += 2
            elif title and title in saved_title:
                score += 1

            # Update best match
            if score > best_score:
                best_score = score
                best_match = hwnd

        # Return the best match if it's a decent match
        return best_match if best_score >= 5 else 0

    def list_layouts(self) -> List[str]:
        """
        List all available layouts.

        Returns:
            List of layout names
        """
        try:
            layouts = []
            for file in self.layouts_dir.glob("*.json"):
                layouts.append(file.name)
            return sorted(layouts)
        except Exception as e:
            logger.error(f"Error listing layouts: {e}")
            return []