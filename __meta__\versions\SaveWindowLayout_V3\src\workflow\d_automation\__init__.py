"""
Window Automation Package

This package provides functions for automating window management operations, including:
- Manipulating individual windows (move, resize, show/hide)
- Batch operations on multiple windows (tile, cascade)
- Multi-monitor support
- Utility functions for window identification and searching
"""

# Import key window action functions
from .a_window_actions import (
    move_window,
    set_window_pos,
    show_window,
    set_foreground_window,
    get_window_rect,
    position_window_with_state
)

# Import key batch operations
from .b_batch_operations import (
    tile_windows_horizontally,
    tile_windows_vertically,
    cascade_windows,
    grid_arrange_windows,
    minimize_all_windows,
    restore_all_windows
)

# Import monitor-related functionality
from .c_monitors import (
    Monitor,
    get_all_monitors,
    get_primary_monitor,
    get_monitor_for_window,
    distribute_windows_across_monitors,
    maximize_window_on_monitor,
    center_window_on_monitor
)

# Import utility functions
from .d_utility import (
    get_process_name,
    get_window_text,
    get_window_class,
    find_window_by_title,
    find_windows_by_process,
    get_desktop_window,
    get_foreground_window,
    wait_for_window_title
)

__all__ = [
    # Window actions
    'move_window',
    'set_window_pos',
    'show_window',
    'set_foreground_window',
    'get_window_rect',
    'position_window_with_state',

    # Batch operations
    'tile_windows_horizontally',
    'tile_windows_vertically',
    'cascade_windows',
    'grid_arrange_windows',
    'minimize_all_windows',
    'restore_all_windows',

    # Monitor operations
    'Monitor',
    'get_all_monitors',
    'get_primary_monitor',
    'get_monitor_for_window',
    'distribute_windows_across_monitors',
    'maximize_window_on_monitor',
    'center_window_on_monitor',

    # Utility functions
    'get_process_name',
    'get_window_text',
    'get_window_class',
    'find_window_by_title',
    'find_windows_by_process',
    'get_desktop_window',
    'get_foreground_window',
    'wait_for_window_title'
]