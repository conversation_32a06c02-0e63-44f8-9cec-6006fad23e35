"""
Window Actions Module

This module provides functions for manipulating individual windows, including
moving, resizing, changing window state (minimize, maximize, etc.), and
controlling visibility and focus.

These functions encapsulate the Win32 API calls for window manipulation,
providing a clean interface for other modules to use.
"""

import time
import win32gui
import win32con
from typing import Tuple, Optional

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


def move_window(hwnd: int, x: int, y: int, width: int, height: int, redraw: bool = True) -> bool:
    """
    Move and resize a window to the specified position and dimensions.

    Args:
        hwnd: Window handle
        x: X-coordinate of the window's new position
        y: Y-coordinate of the window's new position
        width: New width of the window
        height: New height of the window
        redraw: Whether to redraw the window after moving

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        win32gui.MoveWindow(hwnd, x, y, width, height, redraw)
        logger.debug(f"Moved window {hwnd} to ({x}, {y}) with size {width}x{height}")
        return True
    except Exception as e:
        logger.error(f"Error moving window {hwnd}: {e}")
        return False


def set_window_pos(hwnd: int, x: int, y: int, width: int, height: int,
                  z_order: int = win32con.HWND_TOP, flags: int = 0) -> bool:
    """
    Set the position, size, and Z-order of a window.

    Args:
        hwnd: Window handle
        x: X-coordinate of the window's new position
        y: Y-coordinate of the window's new position
        width: New width of the window
        height: New height of the window
        z_order: Z-order position (HWND_TOP, HWND_BOTTOM, etc.)
        flags: Positioning flags (SWP_NOACTIVATE, SWP_NOZORDER, etc.)

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        win32gui.SetWindowPos(hwnd, z_order, x, y, width, height, flags)
        logger.debug(f"Set window {hwnd} position to ({x}, {y}) with size {width}x{height}")
        return True
    except Exception as e:
        logger.error(f"Error setting window {hwnd} position: {e}")
        return False


def get_window_placement(hwnd: int) -> Optional[Tuple[int, int, Tuple[int, int, int, int], Tuple[int, int, int, int]]]:
    """
    Get the show state and normal position of a window.

    Args:
        hwnd: Window handle

    Returns:
        Tuple containing window placement information or None if unsuccessful
    """
    try:
        return win32gui.GetWindowPlacement(hwnd)
    except Exception as e:
        logger.error(f"Error getting window {hwnd} placement: {e}")
        return None


def show_window(hwnd: int, cmd: int) -> bool:
    """
    Set the show state of a window.

    Args:
        hwnd: Window handle
        cmd: Show command (SW_SHOW, SW_HIDE, SW_MAXIMIZE, SW_MINIMIZE, etc.)

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        win32gui.ShowWindow(hwnd, cmd)
        state_names = {
            win32con.SW_HIDE: "hidden",
            win32con.SW_SHOW: "shown",
            win32con.SW_MINIMIZE: "minimized",
            win32con.SW_MAXIMIZE: "maximized",
            win32con.SW_RESTORE: "restored",
        }
        state_name = state_names.get(cmd, f"state {cmd}")
        logger.debug(f"Set window {hwnd} to {state_name}")
        return True
    except Exception as e:
        logger.error(f"Error showing window {hwnd}: {e}")
        return False


def set_foreground_window(hwnd: int) -> bool:
    """
    Bring a window to the foreground.

    Args:
        hwnd: Window handle

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        result = win32gui.SetForegroundWindow(hwnd)
        logger.debug(f"Set window {hwnd} as foreground window")
        return result
    except Exception as e:
        logger.error(f"Error setting window {hwnd} as foreground: {e}")
        return False


def is_window_visible(hwnd: int) -> bool:
    """
    Check if a window is visible.

    Args:
        hwnd: Window handle

    Returns:
        True if the window is visible, False otherwise
    """
    try:
        return win32gui.IsWindowVisible(hwnd)
    except Exception as e:
        logger.error(f"Error checking if window {hwnd} is visible: {e}")
        return False


def get_window_rect(hwnd: int) -> Optional[Tuple[int, int, int, int]]:
    """
    Get the dimensions of a window.

    Args:
        hwnd: Window handle

    Returns:
        Tuple (left, top, right, bottom) or None if unsuccessful
    """
    try:
        return win32gui.GetWindowRect(hwnd)
    except Exception as e:
        logger.error(f"Error getting window {hwnd} rect: {e}")
        return None


def position_window_with_state(hwnd: int, x: int, y: int, width: int, height: int) -> bool:
    """
    Position a window while respecting its current state (minimized, maximized).

    This function will:
    1. Restore the window if it's minimized
    2. Only resize if the window is not maximized
    3. Ensure the window is visible

    Args:
        hwnd: Window handle
        x: X-coordinate of the window's new position
        y: Y-coordinate of the window's new position
        width: New width of the window
        height: New height of the window

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        # Check if window is minimized or maximized
        placement = get_window_placement(hwnd)
        if not placement:
            return False

        show_cmd = placement[1]  # showCmd

        if show_cmd == win32con.SW_SHOWMINIMIZED:
            # Restore the window if it's minimized
            show_window(hwnd, win32con.SW_RESTORE)
            time.sleep(0.1)  # Give it a moment to restore

        if show_cmd != win32con.SW_SHOWMAXIMIZED:
            # Only resize if not maximized
            move_window(hwnd, x, y, width, height)

        # Ensure the window is visible
        if not is_window_visible(hwnd):
            show_window(hwnd, win32con.SW_SHOW)

        return True
    except Exception as e:
        logger.error(f"Error positioning window {hwnd}: {e}")
        return False