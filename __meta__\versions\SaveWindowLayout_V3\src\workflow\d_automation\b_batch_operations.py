"""
Batch Operations Module

This module provides functions for performing operations on multiple windows
simultaneously, such as tiling, cascading, and arranging windows in various layouts.
"""

import time
import win32con
from typing import List, Dict, Any, Tuple, Optional

from .a_window_actions import position_window_with_state, show_window, get_window_rect

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


def tile_windows_horizontally(windows: List[int]) -> bool:
    """
    Tile multiple windows horizontally across the screen.

    Args:
        windows: List of window handles to tile

    Returns:
        True if the operation was successful, False otherwise
    """
    if not windows:
        logger.warning("No windows provided for horizontal tiling")
        return False

    try:
        import win32api
        # Get screen dimensions
        screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
        screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN

        window_count = len(windows)
        window_width = screen_width // window_count

        for i, hwnd in enumerate(windows):
            x = i * window_width
            position_window_with_state(hwnd, x, 0, window_width, screen_height)

        logger.info(f"Tiled {window_count} windows horizontally")
        return True
    except Exception as e:
        logger.error(f"Error tiling windows horizontally: {e}")
        return False


def tile_windows_vertically(windows: List[int]) -> bool:
    """
    Tile multiple windows vertically down the screen.

    Args:
        windows: List of window handles to tile

    Returns:
        True if the operation was successful, False otherwise
    """
    if not windows:
        logger.warning("No windows provided for vertical tiling")
        return False

    try:
        import win32api
        # Get screen dimensions
        screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
        screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN

        window_count = len(windows)
        window_height = screen_height // window_count

        for i, hwnd in enumerate(windows):
            y = i * window_height
            position_window_with_state(hwnd, 0, y, screen_width, window_height)

        logger.info(f"Tiled {window_count} windows vertically")
        return True
    except Exception as e:
        logger.error(f"Error tiling windows vertically: {e}")
        return False


def cascade_windows(windows: List[int], offset_x: int = 30, offset_y: int = 30) -> bool:
    """
    Cascade windows with an offset.

    Args:
        windows: List of window handles to cascade
        offset_x: Horizontal offset between windows
        offset_y: Vertical offset between windows

    Returns:
        True if the operation was successful, False otherwise
    """
    if not windows:
        logger.warning("No windows provided for cascading")
        return False

    try:
        import win32api
        # Get screen dimensions
        screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
        screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN

        # Set initial window size (slightly smaller than screen)
        window_width = screen_width - 100
        window_height = screen_height - 100

        for i, hwnd in enumerate(windows):
            x = i * offset_x
            y = i * offset_y

            # Ensure window stays within screen bounds
            if x + window_width > screen_width:
                x = screen_width - window_width
            if y + window_height > screen_height:
                y = screen_height - window_height

            position_window_with_state(hwnd, x, y, window_width, window_height)

        logger.info(f"Cascaded {len(windows)} windows")
        return True
    except Exception as e:
        logger.error(f"Error cascading windows: {e}")
        return False


def grid_arrange_windows(windows: List[int], columns: int = 2) -> bool:
    """
    Arrange windows in a grid layout.

    Args:
        windows: List of window handles to arrange
        columns: Number of columns in the grid

    Returns:
        True if the operation was successful, False otherwise
    """
    if not windows:
        logger.warning("No windows provided for grid arrangement")
        return False

    try:
        import win32api
        # Get screen dimensions
        screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
        screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN

        window_count = len(windows)
        rows = (window_count + columns - 1) // columns  # Ceiling division

        cell_width = screen_width // columns
        cell_height = screen_height // rows

        for i, hwnd in enumerate(windows):
            col = i % columns
            row = i // columns

            x = col * cell_width
            y = row * cell_height

            position_window_with_state(hwnd, x, y, cell_width, cell_height)

        logger.info(f"Arranged {window_count} windows in a {rows}x{columns} grid")
        return True
    except Exception as e:
        logger.error(f"Error arranging windows in grid: {e}")
        return False


def minimize_all_windows(windows: List[int]) -> bool:
    """
    Minimize all specified windows.

    Args:
        windows: List of window handles to minimize

    Returns:
        True if the operation was successful, False otherwise
    """
    if not windows:
        logger.warning("No windows provided for minimizing")
        return False

    try:
        for hwnd in windows:
            show_window(hwnd, win32con.SW_MINIMIZE)

        logger.info(f"Minimized {len(windows)} windows")
        return True
    except Exception as e:
        logger.error(f"Error minimizing windows: {e}")
        return False


def restore_all_windows(windows: List[int]) -> bool:
    """
    Restore all specified windows.

    Args:
        windows: List of window handles to restore

    Returns:
        True if the operation was successful, False otherwise
    """
    if not windows:
        logger.warning("No windows provided for restoring")
        return False

    try:
        for hwnd in windows:
            show_window(hwnd, win32con.SW_RESTORE)

        logger.info(f"Restored {len(windows)} windows")
        return True
    except Exception as e:
        logger.error(f"Error restoring windows: {e}")
        return False


def apply_layout_to_windows(window_layouts: List[Dict[str, Any]],
                           window_map: Dict[int, Dict[str, Any]]) -> int:
    """
    Apply a saved layout to a set of windows.

    Args:
        window_layouts: List of window layout specifications from a saved layout
        window_map: Dictionary mapping window handles to current window info

    Returns:
        Number of windows successfully positioned
    """
    success_count = 0

    for layout in window_layouts:
        # Find the corresponding window
        hwnd = layout.get("hwnd", 0)
        if hwnd in window_map:
            # Direct HWND match (same session)
            target_hwnd = hwnd
        else:
            # No direct match, window will need to be found by other means
            # (This would be handled by the window matching in the layout manager)
            continue

        position = layout.get("position", [0, 0])
        size = layout.get("size", [800, 600])

        if position_window_with_state(target_hwnd, position[0], position[1], size[0], size[1]):
            success_count += 1

    return success_count