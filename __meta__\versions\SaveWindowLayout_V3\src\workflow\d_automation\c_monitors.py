"""
Monitors Module

This module provides functions for working with multiple monitors, including
getting monitor information, detecting monitor configuration, and positioning
windows relative to specific monitors.
"""

import win32api
import win32con
from typing import List, Dict, Tuple, Optional, Any

from .a_window_actions import position_window_with_state, get_window_rect, show_window

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class Monitor:
    """
    Represents a physical monitor in the system.
    """

    def __init__(self, index: int, handle: int, rect: Tuple[int, int, int, int],
                work_area: Tuple[int, int, int, int], is_primary: bool = False):
        """
        Initialize a Monitor object.

        Args:
            index: Monitor index (0-based)
            handle: Monitor handle
            rect: Monitor rectangle (left, top, right, bottom)
            work_area: Working area rectangle (left, top, right, bottom)
            is_primary: Whether this is the primary monitor
        """
        self.index = index
        self.handle = handle
        self.rect = rect
        self.work_area = work_area
        self.is_primary = is_primary

        # Calculated properties
        self.left = rect[0]
        self.top = rect[1]
        self.right = rect[2]
        self.bottom = rect[3]
        self.width = rect[2] - rect[0]
        self.height = rect[3] - rect[1]

        # Work area properties
        self.work_left = work_area[0]
        self.work_top = work_area[1]
        self.work_right = work_area[2]
        self.work_bottom = work_area[3]
        self.work_width = work_area[2] - work_area[0]
        self.work_height = work_area[3] - work_area[1]

    def __repr__(self) -> str:
        return f"Monitor(index={self.index}, size={self.width}x{self.height}, primary={self.is_primary})"


def get_all_monitors() -> List[Monitor]:
    """
    Get information about all monitors in the system.

    Returns:
        List of Monitor objects
    """
    monitors = []

    try:
        # Define monitor enumeration callback
        def callback(handle: int, hdc: int, rect: win32con.RECT, data: Any) -> int:
            # Get monitor info
            monitor_info = win32api.GetMonitorInfo(handle)

            # Check if primary
            is_primary = (monitor_info["Flags"] & win32con.MONITORINFOF_PRIMARY) != 0

            # Create Monitor object
            monitor = Monitor(
                index=len(monitors),
                handle=handle,
                rect=(rect[0], rect[1], rect[2], rect[3]),
                work_area=monitor_info["Work"],
                is_primary=is_primary
            )

            monitors.append(monitor)
            return 1  # Continue enumeration

        # Enumerate monitors
        win32api.EnumDisplayMonitors(None, None, callback, None)

        # Sort monitors by left position and then top position
        monitors.sort(key=lambda m: (m.left, m.top))

        logger.debug(f"Found {len(monitors)} monitors")
        return monitors

    except Exception as e:
        logger.error(f"Error getting monitor information: {e}")
        return []


def get_primary_monitor() -> Optional[Monitor]:
    """
    Get the primary monitor.

    Returns:
        Primary Monitor object or None if not found
    """
    monitors = get_all_monitors()
    for monitor in monitors:
        if monitor.is_primary:
            return monitor
    return None if not monitors else monitors[0]


def get_monitor_at_position(x: int, y: int) -> Optional[Monitor]:
    """
    Get the monitor that contains the specified point.

    Args:
        x: X-coordinate
        y: Y-coordinate

    Returns:
        Monitor object or None if not found
    """
    monitors = get_all_monitors()
    for monitor in monitors:
        if monitor.left <= x < monitor.right and monitor.top <= y < monitor.bottom:
            return monitor
    return get_primary_monitor()


def get_monitor_for_window(hwnd: int) -> Optional[Monitor]:
    """
    Get the monitor that contains the specified window.

    Args:
        hwnd: Window handle

    Returns:
        Monitor object or None if not found
    """
    try:
        rect = get_window_rect(hwnd)
        if not rect:
            return get_primary_monitor()

        # Calculate window center point
        center_x = (rect[0] + rect[2]) // 2
        center_y = (rect[1] + rect[3]) // 2

        return get_monitor_at_position(center_x, center_y)
    except Exception as e:
        logger.error(f"Error getting monitor for window {hwnd}: {e}")
        return get_primary_monitor()


def distribute_windows_across_monitors(windows: List[int]) -> bool:
    """
    Distribute windows across all available monitors.

    Args:
        windows: List of window handles to distribute

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        monitors = get_all_monitors()
        if not monitors:
            logger.warning("No monitors found")
            return False

        window_count = len(windows)
        if window_count == 0:
            logger.warning("No windows provided for distribution")
            return False

        # Determine how to distribute windows
        windows_per_monitor = window_count // len(monitors)
        extra_windows = window_count % len(monitors)

        window_index = 0

        for i, monitor in enumerate(monitors):
            # Calculate how many windows for this monitor
            this_monitor_windows = windows_per_monitor
            if i < extra_windows:
                this_monitor_windows += 1

            if this_monitor_windows == 0:
                continue

            # For this monitor, arrange windows in a grid
            cols = int(this_monitor_windows ** 0.5)
            rows = (this_monitor_windows + cols - 1) // cols

            cell_width = monitor.work_width // cols
            cell_height = monitor.work_height // rows

            for j in range(this_monitor_windows):
                if window_index >= window_count:
                    break

                col = j % cols
                row = j // cols

                x = monitor.work_left + col * cell_width
                y = monitor.work_top + row * cell_height

                position_window_with_state(
                    windows[window_index],
                    x, y,
                    cell_width, cell_height
                )

                window_index += 1

        logger.info(f"Distributed {window_count} windows across {len(monitors)} monitors")
        return True
    except Exception as e:
        logger.error(f"Error distributing windows across monitors: {e}")
        return False


def maximize_window_on_monitor(hwnd: int, monitor_index: Optional[int] = None) -> bool:
    """
    Maximize a window on a specific monitor.

    Args:
        hwnd: Window handle
        monitor_index: Index of the target monitor, or None for current monitor

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        # If no monitor specified, use the one containing the window
        if monitor_index is None:
            monitor = get_monitor_for_window(hwnd)
        else:
            monitors = get_all_monitors()
            if 0 <= monitor_index < len(monitors):
                monitor = monitors[monitor_index]
            else:
                monitor = get_primary_monitor()

        if not monitor:
            logger.warning("No valid monitor found")
            return False

        # First move the window to the monitor
        position_window_with_state(
            hwnd,
            monitor.work_left,
            monitor.work_top,
            monitor.work_width,
            monitor.work_height
        )

        # Then maximize it
        show_window(hwnd, win32con.SW_MAXIMIZE)

        logger.info(f"Maximized window {hwnd} on monitor {monitor.index}")
        return True
    except Exception as e:
        logger.error(f"Error maximizing window on monitor: {e}")
        return False


def center_window_on_monitor(hwnd: int, monitor_index: Optional[int] = None,
                            width: Optional[int] = None, height: Optional[int] = None) -> bool:
    """
    Center a window on a specific monitor.

    Args:
        hwnd: Window handle
        monitor_index: Index of the target monitor, or None for current monitor
        width: Optional width to use (if None, uses window's current width)
        height: Optional height to use (if None, uses window's current height)

    Returns:
        True if the operation was successful, False otherwise
    """
    try:
        # If no monitor specified, use the one containing the window
        if monitor_index is None:
            monitor = get_monitor_for_window(hwnd)
        else:
            monitors = get_all_monitors()
            if 0 <= monitor_index < len(monitors):
                monitor = monitors[monitor_index]
            else:
                monitor = get_primary_monitor()

        if not monitor:
            logger.warning("No valid monitor found")
            return False

        # Get window size (if not provided)
        if width is None or height is None:
            rect = get_window_rect(hwnd)
            if not rect:
                logger.warning(f"Could not get window rect for {hwnd}")
                return False

            if width is None:
                width = rect[2] - rect[0]
            if height is None:
                height = rect[3] - rect[1]

        # Calculate center position
        x = monitor.work_left + (monitor.work_width - width) // 2
        y = monitor.work_top + (monitor.work_height - height) // 2

        # Position window
        position_window_with_state(hwnd, x, y, width, height)

        logger.info(f"Centered window {hwnd} on monitor {monitor.index}")
        return True
    except Exception as e:
        logger.error(f"Error centering window on monitor: {e}")
        return False