"""
Automation Utility Module

This module provides utility functions for window automation that don't fit into
the other specific categories, such as window identification, error handling,
and specialized window operations.
"""

import os
import time
import ctypes
import win32gui
import win32con
import win32process
from typing import Dict, List, Tuple, Optional, Any, Set, Callable

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


def get_process_path(hwnd: int) -> str:
    """
    Get the full path of the process that owns a window.

    Args:
        hwnd: Window handle

    Returns:
        Full path to the process executable or empty string if not found
    """
    try:
        import psutil
        # Get process ID
        _, pid = win32process.GetWindowThreadProcessId(hwnd)

        # Get process info
        process = psutil.Process(pid)
        return process.exe() if process else ""
    except Exception as e:
        logger.debug(f"Could not get process path for window {hwnd}: {e}")
        return ""


def get_process_name(hwnd: int) -> str:
    """
    Get the name of the process that owns a window.

    Args:
        hwnd: Window handle

    Returns:
        Process name or empty string if not found
    """
    path = get_process_path(hwnd)
    return os.path.basename(path) if path else ""


def get_window_text(hwnd: int) -> str:
    """
    Safely get the text (title) of a window.

    Args:
        hwnd: Window handle

    Returns:
        Window text or empty string if not found
    """
    try:
        return win32gui.GetWindowText(hwnd)
    except Exception as e:
        logger.debug(f"Could not get window text for window {hwnd}: {e}")
        return ""


def get_window_class(hwnd: int) -> str:
    """
    Safely get the class name of a window.

    Args:
        hwnd: Window handle

    Returns:
        Window class name or empty string if not found
    """
    try:
        return win32gui.GetClassName(hwnd)
    except Exception as e:
        logger.debug(f"Could not get window class for window {hwnd}: {e}")
        return ""


def is_window_enabled(hwnd: int) -> bool:
    """
    Check if a window is enabled (can receive input).

    Args:
        hwnd: Window handle

    Returns:
        True if the window is enabled, False otherwise
    """
    try:
        return win32gui.IsWindowEnabled(hwnd)
    except Exception as e:
        logger.debug(f"Could not check if window {hwnd} is enabled: {e}")
        return False


def is_window_unicode(hwnd: int) -> bool:
    """
    Check if a window is a Unicode window.

    Args:
        hwnd: Window handle

    Returns:
        True if the window is Unicode, False otherwise
    """
    try:
        return win32gui.IsWindowUnicode(hwnd)
    except Exception as e:
        logger.debug(f"Could not check if window {hwnd} is Unicode: {e}")
        return False


def get_all_window_handles(visible_only: bool = True) -> List[int]:
    """
    Get a list of all window handles in the system.

    Args:
        visible_only: Whether to include only visible windows

    Returns:
        List of window handles
    """
    windows = []

    def enum_windows_callback(hwnd: int, _) -> bool:
        if not visible_only or win32gui.IsWindowVisible(hwnd):
            windows.append(hwnd)
        return True

    try:
        win32gui.EnumWindows(enum_windows_callback, None)
    except Exception as e:
        logger.error(f"Error enumerating windows: {e}")

    return windows


def find_window_by_title(title: str, exact_match: bool = False) -> int:
    """
    Find a window by its title.

    Args:
        title: Window title to search for
        exact_match: Whether to require an exact match

    Returns:
        Window handle or 0 if not found
    """
    title_lower = title.lower()

    def enum_windows_callback(hwnd: int, result: List[int]) -> bool:
        if win32gui.IsWindowVisible(hwnd):
            window_title = get_window_text(hwnd).lower()
            if (exact_match and window_title == title_lower) or \
               (not exact_match and title_lower in window_title):
                result.append(hwnd)
                return False  # Stop enumeration after first match
        return True

    result = []
    try:
        win32gui.EnumWindows(enum_windows_callback, result)
    except Exception as e:
        logger.error(f"Error finding window by title: {e}")

    return result[0] if result else 0


def find_windows_by_class(class_name: str) -> List[int]:
    """
    Find all windows with a specific class name.

    Args:
        class_name: Window class name to search for

    Returns:
        List of window handles
    """
    class_name_lower = class_name.lower()
    windows = []

    def enum_windows_callback(hwnd: int, _) -> bool:
        if win32gui.IsWindowVisible(hwnd):
            window_class = get_window_class(hwnd).lower()
            if class_name_lower in window_class:
                windows.append(hwnd)
        return True

    try:
        win32gui.EnumWindows(enum_windows_callback, None)
    except Exception as e:
        logger.error(f"Error finding windows by class: {e}")

    return windows


def find_windows_by_process(process_name: str) -> List[int]:
    """
    Find all windows belonging to a specific process.

    Args:
        process_name: Process name to search for

    Returns:
        List of window handles
    """
    process_name_lower = process_name.lower()
    windows = []

    def enum_windows_callback(hwnd: int, _) -> bool:
        if win32gui.IsWindowVisible(hwnd):
            proc_name = get_process_name(hwnd).lower()
            if process_name_lower in proc_name:
                windows.append(hwnd)
        return True

    try:
        win32gui.EnumWindows(enum_windows_callback, None)
    except Exception as e:
        logger.error(f"Error finding windows by process: {e}")

    return windows


def get_desktop_window() -> int:
    """
    Get the desktop window handle.

    Returns:
        Desktop window handle
    """
    try:
        return win32gui.GetDesktopWindow()
    except Exception as e:
        logger.error(f"Error getting desktop window: {e}")
        return 0


def get_foreground_window() -> int:
    """
    Get the currently active (foreground) window handle.

    Returns:
        Foreground window handle
    """
    try:
        return win32gui.GetForegroundWindow()
    except Exception as e:
        logger.error(f"Error getting foreground window: {e}")
        return 0


def wait_for_window(criteria: Callable[[int], bool], timeout: float = 5.0, check_interval: float = 0.1) -> int:
    """
    Wait for a window that matches the given criteria to appear.

    Args:
        criteria: Function that takes a window handle and returns True if it matches
        timeout: Maximum time to wait in seconds
        check_interval: How often to check for matching windows

    Returns:
        Window handle or 0 if not found within the timeout
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        windows = get_all_window_handles()
        for hwnd in windows:
            if criteria(hwnd):
                return hwnd
        time.sleep(check_interval)
    return 0


def wait_for_window_title(title: str, exact_match: bool = False, timeout: float = 5.0) -> int:
    """
    Wait for a window with the given title to appear.

    Args:
        title: Window title to wait for
        exact_match: Whether to require an exact match
        timeout: Maximum time to wait in seconds

    Returns:
        Window handle or 0 if not found within the timeout
    """
    title_lower = title.lower()

    def match_criteria(hwnd: int) -> bool:
        window_title = get_window_text(hwnd).lower()
        return (exact_match and window_title == title_lower) or \
               (not exact_match and title_lower in window_title)

    return wait_for_window(match_criteria, timeout)


def wait_for_process_window(process_name: str, timeout: float = 5.0) -> int:
    """
    Wait for a window belonging to the given process to appear.

    Args:
        process_name: Process name to wait for
        timeout: Maximum time to wait in seconds

    Returns:
        Window handle or 0 if not found within the timeout
    """
    process_name_lower = process_name.lower()

    def match_criteria(hwnd: int) -> bool:
        proc_name = get_process_name(hwnd).lower()
        return process_name_lower in proc_name

    return wait_for_window(match_criteria, timeout)