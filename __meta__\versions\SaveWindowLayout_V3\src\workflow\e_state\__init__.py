"""
Window State Package

Contains modules for managing application state and persistence.
This module is responsible for:
1. Managing configuration and user preferences
2. Persisting window layouts and settings
3. Tracking session information
4. Providing a consistent state across application components
"""

from .config_manager import ConfigManager
from .session_manager import SessionManager
from .preferences import UserPreferences

__all__ = [
    'ConfigManager',
    'SessionManager',
    'UserPreferences'
]