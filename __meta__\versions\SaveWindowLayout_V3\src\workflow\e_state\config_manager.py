"""
Configuration Manager Module

This module handles the application configuration, including loading, saving,
and providing access to configuration settings throughout the application.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class ConfigManager:
    """
    Manages application configuration settings.

    Responsibilities:
    - Load configuration from files
    - Save configuration to files
    - Provide access to configuration values
    - Handle configuration defaults
    """

    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_dir: Directory where configuration files are stored.
                        If None, uses default app config directory.
        """
        self.config_dir = Path(config_dir) if config_dir else Path.home() / ".window_manager"
        self.config_file = self.config_dir / "config.json"
        self.config: Dict[str, Any] = {}

        # Create config directory if it doesn't exist
        os.makedirs(self.config_dir, exist_ok=True)

        # Load configuration or create default
        self._load_config()

        logger.debug(f"ConfigManager initialized with config directory: {self.config_dir}")

    def _load_config(self) -> None:
        """Load configuration from file or create with defaults if it doesn't exist."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
                logger.debug(f"Loaded configuration from {self.config_file}")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                self._create_default_config()
        else:
            self._create_default_config()

    def _create_default_config(self) -> None:
        """Create and save default configuration."""
        self.config = {
            "layouts_dir": str(self.config_dir / "layouts"),
            "auto_save": False,
            "auto_save_interval": 30,  # minutes
            "default_layout": "default",
            "startup_layout": None,
            "monitor_settings": {
                "use_work_area": True,
                "respect_taskbar": True
            },
            "ui_settings": {
                "show_notifications": True,
                "minimize_to_tray": True,
                "dark_mode": False
            }
        }

        # Create layouts directory
        os.makedirs(Path(self.config["layouts_dir"]), exist_ok=True)

        # Save default configuration
        self.save_config()
        logger.debug("Created default configuration")

    def save_config(self) -> bool:
        """
        Save current configuration to file.

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.debug(f"Saved configuration to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            key: Configuration key
            default: Default value if key doesn't exist

        Returns:
            Configuration value or default
        """
        return self.config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value.

        Args:
            key: Configuration key
            value: Value to set
        """
        self.config[key] = value
        logger.debug(f"Set configuration {key}={value}")

    def update(self, updates: Dict[str, Any]) -> None:
        """
        Update multiple configuration values.

        Args:
            updates: Dictionary of configuration updates
        """
        self.config.update(updates)
        logger.debug(f"Updated configuration with {len(updates)} values")

    def get_layouts_dir(self) -> str:
        """
        Get the directory for storing layouts.

        Returns:
            Path to layouts directory
        """
        layouts_dir = self.get("layouts_dir")
        os.makedirs(layouts_dir, exist_ok=True)
        return layouts_dir