"""
User Preferences Module

This module handles user preferences for the application, including UI settings,
behavior options, and per-layout preferences.
"""

import json
from typing import Dict, Any, Optional, List
from pathlib import Path

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class UserPreferences:
    """
    Manages user preferences for the application.

    Responsibilities:
    - Store user preferences for UI and behavior
    - Load and save preferences to disk
    - Provide defaults for missing preferences
    - Notify other components of preference changes
    """

    def __init__(self, preferences_file: Optional[str] = None):
        """
        Initialize user preferences.

        Args:
            preferences_file: Path to the preferences file. If None, a default location is used.
        """
        self.preferences_file = Path(preferences_file) if preferences_file else None
        self.preferences: Dict[str, Any] = {}
        self._observers: List[callable] = []

        # Set up default preferences
        self._set_defaults()

        # Load preferences if file exists
        if self.preferences_file and self.preferences_file.exists():
            self.load_preferences()

        logger.debug(f"UserPreferences initialized with preferences file: {self.preferences_file}")

    def _set_defaults(self) -> None:
        """Set default preference values."""
        self.preferences = {
            "ui": {
                "theme": "system",  # system, light, dark
                "show_tooltips": True,
                "confirm_layout_apply": True,
                "show_notifications": True,
                "window_animations": True
            },
            "behavior": {
                "auto_save_layouts": False,
                "auto_save_interval": 60,  # minutes
                "startup_action": "none",  # none, restore_last, apply_specific
                "startup_layout": "",
                "check_for_updates": True,
                "update_interval": 7  # days
            },
            "window_matching": {
                "match_by_hwnd": True,
                "match_by_title": True,
                "match_by_class": True,
                "match_by_process": True,
                "title_match_threshold": 0.75  # For fuzzy matching
            },
            "hotkeys": {
                "save_layout": "ctrl+alt+s",
                "apply_layout": "ctrl+alt+a",
                "toggle_window_tracking": "ctrl+alt+t"
            },
            "layout_defaults": {
                "include_minimized": False,
                "restore_explorer": True,
                "restore_browsers": True,
                "restore_documents": True,
                "restore_others": True
            }
        }

    def register_observer(self, callback: callable) -> None:
        """
        Register a callback to be notified when preferences change.

        Args:
            callback: Function to call when preferences change
        """
        if callback not in self._observers:
            self._observers.append(callback)

    def unregister_observer(self, callback: callable) -> None:
        """
        Remove a callback from the notification list.

        Args:
            callback: Function to remove
        """
        if callback in self._observers:
            self._observers.remove(callback)

    def _notify_observers(self) -> None:
        """Notify all registered observers that preferences have changed."""
        for callback in self._observers:
            try:
                callback(self.preferences)
            except Exception as e:
                logger.error(f"Error notifying preference observer: {e}")

    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        Get a preference value.

        Args:
            section: Preference section (e.g., "ui", "behavior")
            key: Preference key
            default: Default value if the preference doesn't exist

        Returns:
            Preference value or default
        """
        if section in self.preferences and key in self.preferences[section]:
            return self.preferences[section][key]
        return default

    def set(self, section: str, key: str, value: Any) -> None:
        """
        Set a preference value.

        Args:
            section: Preference section (e.g., "ui", "behavior")
            key: Preference key
            value: Value to set
        """
        if section not in self.preferences:
            self.preferences[section] = {}

        self.preferences[section][key] = value
        logger.debug(f"Set preference {section}.{key}={value}")

        # Notify observers
        self._notify_observers()

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get all preferences in a section.

        Args:
            section: Preference section (e.g., "ui", "behavior")

        Returns:
            Dictionary of preferences in the section or empty dict if section doesn't exist
        """
        return self.preferences.get(section, {}).copy()

    def load_preferences(self) -> bool:
        """
        Load preferences from file.

        Returns:
            True if loaded successfully, False otherwise
        """
        if not self.preferences_file:
            logger.warning("No preferences file specified")
            return False

        try:
            with open(self.preferences_file, 'r') as f:
                loaded_prefs = json.load(f)

            # Merge loaded preferences with defaults
            for section, values in loaded_prefs.items():
                if section not in self.preferences:
                    self.preferences[section] = {}

                if isinstance(values, dict):
                    self.preferences[section].update(values)

            logger.debug(f"Loaded preferences from {self.preferences_file}")

            # Notify observers
            self._notify_observers()

            return True
        except Exception as e:
            logger.error(f"Error loading preferences: {e}")
            return False

    def save_preferences(self) -> bool:
        """
        Save preferences to file.

        Returns:
            True if saved successfully, False otherwise
        """
        if not self.preferences_file:
            logger.warning("No preferences file specified")
            return False

        try:
            # Make sure the directory exists
            self.preferences_file.parent.mkdir(parents=True, exist_ok=True)

            with open(self.preferences_file, 'w') as f:
                json.dump(self.preferences, f, indent=4)

            logger.debug(f"Saved preferences to {self.preferences_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving preferences: {e}")
            return False

    def reset_section(self, section: str) -> None:
        """
        Reset a preference section to defaults.

        Args:
            section: Preference section to reset
        """
        if section in self.preferences:
            # Create temporary defaults
            defaults = {}
            self._set_defaults()

            # Copy default values for this section
            if section in self.preferences:
                self.preferences[section] = self.preferences[section].copy()

            logger.debug(f"Reset preferences section: {section}")

            # Notify observers
            self._notify_observers()

    def reset_all(self) -> None:
        """Reset all preferences to defaults."""
        self._set_defaults()
        logger.debug("Reset all preferences to defaults")

        # Notify observers
        self._notify_observers()