"""
Session Manager Module

This module manages the application session state, tracking information about
the current session such as active windows, applied layouts, and recent operations.
"""

import time
import json
from typing import Dict, List, Optional, Any
from pathlib import Path

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class SessionManager:
    """
    Manages the application session state.

    Responsibilities:
    - Track active windows
    - Track recently used layouts
    - Maintain history of operations
    - Provide session information to other components
    """

    def __init__(self, session_file: Optional[str] = None):
        """
        Initialize the session manager.

        Args:
            session_file: Path to the session file. If None, a default location is used.
        """
        self.session_file = Path(session_file) if session_file else None
        self.session_data: Dict[str, Any] = {
            "start_time": time.time(),
            "last_active_layout": None,
            "recent_layouts": [],
            "recent_windows": {},
            "operations_history": [],
            "last_window_positions": {}
        }

        logger.debug(f"SessionManager initialized with session file: {self.session_file}")

    def record_layout_use(self, layout_name: str) -> None:
        """
        Record that a layout was used.

        Args:
            layout_name: Name of the layout that was used
        """
        self.session_data["last_active_layout"] = layout_name

        # Add to recent layouts, avoiding duplicates
        if layout_name in self.session_data["recent_layouts"]:
            self.session_data["recent_layouts"].remove(layout_name)

        self.session_data["recent_layouts"].insert(0, layout_name)

        # Keep only the 10 most recent
        self.session_data["recent_layouts"] = self.session_data["recent_layouts"][:10]

        logger.debug(f"Recorded use of layout: {layout_name}")

    def record_window_position(self, hwnd: int, position: Dict[str, Any]) -> None:
        """
        Record the position of a window.

        Args:
            hwnd: Window handle
            position: Position information
        """
        self.session_data["last_window_positions"][str(hwnd)] = position
        logger.debug(f"Recorded position for window {hwnd}")

    def record_operation(self, operation: str, details: Dict[str, Any]) -> None:
        """
        Record an operation in the history.

        Args:
            operation: Type of operation (e.g., "apply_layout", "move_window")
            details: Additional details about the operation
        """
        entry = {
            "timestamp": time.time(),
            "operation": operation,
            "details": details
        }

        self.session_data["operations_history"].append(entry)

        # Keep only the 100 most recent operations
        self.session_data["operations_history"] = self.session_data["operations_history"][-100:]

        logger.debug(f"Recorded operation: {operation}")

    def get_recent_layouts(self, count: int = 5) -> List[str]:
        """
        Get a list of recently used layouts.

        Args:
            count: Maximum number of layouts to return

        Returns:
            List of layout names
        """
        return self.session_data["recent_layouts"][:count]

    def get_last_active_layout(self) -> Optional[str]:
        """
        Get the name of the last active layout.

        Returns:
            Layout name or None if no layout was applied
        """
        return self.session_data["last_active_layout"]

    def get_last_window_position(self, hwnd: int) -> Optional[Dict[str, Any]]:
        """
        Get the last recorded position for a window.

        Args:
            hwnd: Window handle

        Returns:
            Position information or None if not found
        """
        return self.session_data["last_window_positions"].get(str(hwnd))

    def save_session(self) -> bool:
        """
        Save the current session state to file.

        Returns:
            True if saved successfully, False otherwise
        """
        if not self.session_file:
            logger.warning("No session file specified, not saving session")
            return False

        try:
            # Make sure the directory exists
            self.session_file.parent.mkdir(parents=True, exist_ok=True)

            with open(self.session_file, 'w') as f:
                json.dump(self.session_data, f, indent=4)

            logger.debug(f"Saved session to {self.session_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving session: {e}")
            return False

    def load_session(self) -> bool:
        """
        Load session state from file.

        Returns:
            True if loaded successfully, False otherwise
        """
        if not self.session_file or not self.session_file.exists():
            logger.warning("No session file found, using empty session")
            return False

        try:
            with open(self.session_file, 'r') as f:
                self.session_data = json.load(f)

            logger.debug(f"Loaded session from {self.session_file}")
            return True
        except Exception as e:
            logger.error(f"Error loading session: {e}")
            return False