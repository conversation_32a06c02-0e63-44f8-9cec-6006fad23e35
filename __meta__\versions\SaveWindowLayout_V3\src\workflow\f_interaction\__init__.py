"""
Window Interaction Package

Contains modules for handling user interaction and UI components.
This module is responsible for:
1. Providing user interface components
2. Handling user input and events
3. Visualizing window layouts and operations
4. Managing notifications and alerts
"""

from .ui_manager import UIManager
from .command_handler import CommandHandler
from .notification_manager import NotificationManager
from .hotkey_manager import HotkeyManager

__all__ = [
    'UIManager',
    'CommandHandler',
    'NotificationManager',
    'HotkeyManager'
]