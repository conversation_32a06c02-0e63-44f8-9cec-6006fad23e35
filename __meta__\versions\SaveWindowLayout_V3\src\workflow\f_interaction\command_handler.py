"""
Command Handler Module

This module provides the interface for processing and executing user commands,
including command-line arguments and interactive commands.
"""

from typing import Dict, List, Optional, Any, Callable
import argparse
import sys

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class CommandHandler:
    """
    Processes and executes user commands.

    Responsibilities:
    - Parse command-line arguments
    - Register command handlers
    - Execute commands based on user input
    - Provide help and usage information
    """

    def __init__(self):
        """Initialize the command handler."""
        self.commands: Dict[str, Dict[str, Any]] = {}
        self.parser = None
        logger.debug("CommandHandler initialized")

    def register_command(self, name: str, handler: Callable, help_text: str,
                        arguments: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Register a command with the handler.

        Args:
            name: Command name
            handler: Function to execute when the command is called
            help_text: Help text for the command
            arguments: List of argument definitions for the command
        """
        self.commands[name] = {
            "handler": handler,
            "help": help_text,
            "arguments": arguments or []
        }
        logger.debug(f"Registered command: {name}")

    def setup_argument_parser(self) -> argparse.ArgumentParser:
        """
        Set up the argument parser for command-line arguments.

        Returns:
            Configured ArgumentParser
        """
        parser = argparse.ArgumentParser(description="Window Manager Application")

        # Each command becomes a subparser
        subparsers = parser.add_subparsers(dest="command", help="Command to execute")

        # Add subparsers for each registered command
        for cmd_name, cmd_info in self.commands.items():
            subparser = subparsers.add_parser(cmd_name, help=cmd_info["help"])

            # Add arguments for this command
            for arg in cmd_info["arguments"]:
                kwargs = {k: v for k, v in arg.items() if k != "name"}
                subparser.add_argument(arg["name"], **kwargs)

        self.parser = parser
        return parser

    def process_args(self, args: Optional[List[str]] = None) -> bool:
        """
        Process command-line arguments and execute the corresponding command.

        Args:
            args: Command-line arguments to process, or None to use sys.argv

        Returns:
            True if command executed successfully, False otherwise
        """
        if self.parser is None:
            self.setup_argument_parser()

        # Parse arguments
        parsed_args = self.parser.parse_args(args)

        # Get the command to execute
        command = getattr(parsed_args, "command", None)

        if not command:
            self.parser.print_help()
            return False

        if command not in self.commands:
            logger.error(f"Unknown command: {command}")
            return False

        try:
            # Execute the command handler
            result = self.commands[command]["handler"](parsed_args)
            return result is None or bool(result)
        except Exception as e:
            logger.error(f"Error executing command {command}: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False

    def execute_command(self, command: str, args: Dict[str, Any] = None) -> Any:
        """
        Execute a command programmatically.

        Args:
            command: Command name to execute
            args: Dictionary of arguments to pass to the command

        Returns:
            Result of the command execution
        """
        if command not in self.commands:
            logger.error(f"Unknown command: {command}")
            return False

        try:
            # Convert args to an object that mimics parsed argparse arguments
            if args is None:
                args = {}

            class Args:
                pass

            parsed_args = Args()
            parsed_args.command = command

            for k, v in args.items():
                setattr(parsed_args, k, v)

            # Execute the command handler
            result = self.commands[command]["handler"](parsed_args)
            return result
        except Exception as e:
            logger.error(f"Error executing command {command}: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False

    def get_command_help(self, command: str) -> str:
        """
        Get help text for a specific command.

        Args:
            command: Command name

        Returns:
            Help text for the command
        """
        if command not in self.commands:
            return f"Unknown command: {command}"

        cmd_info = self.commands[command]
        help_text = cmd_info["help"] + "\n\nArguments:\n"

        for arg in cmd_info["arguments"]:
            arg_name = arg["name"]
            arg_help = arg.get("help", "")
            arg_required = "required" if arg.get("required", False) else "optional"
            help_text += f"  {arg_name}: {arg_help} ({arg_required})\n"

        return help_text

    def get_all_commands(self) -> Dict[str, str]:
        """
        Get a dictionary of all commands and their help text.

        Returns:
            Dictionary mapping command names to help text
        """
        return {name: info["help"] for name, info in self.commands.items()}