"""
Hotkey Manager Module

This module handles keyboard shortcuts (hotkeys) for the application,
allowing users to trigger actions using keyboard combinations.
"""

from typing import Dict, List, Optional, Any, Callable
import threading

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class HotkeyManager:
    """
    Manages application hotkeys (keyboard shortcuts).

    Responsibilities:
    - Register and unregister global hotkeys
    - Handle hotkey events
    - Provide a way to configure hotkeys
    - Save and load hotkey configuration
    """

    def __init__(self):
        """Initialize the hotkey manager."""
        self.hotkeys: Dict[str, Dict[str, Any]] = {}
        self.listener_running = False
        self.listener_thread = None

        logger.debug("HotkeyManager initialized")

    def register_hotkey(self, key_combo: str, callback: Callable,
                       description: str = "") -> bool:
        """
        Register a hotkey combination.

        Args:
            key_combo: Keyboard combination (e.g., "ctrl+alt+s")
            callback: Function to call when the hotkey is triggered
            description: Human-readable description of what the hotkey does

        Returns:
            True if registered successfully, False otherwise
        """
        # Normalize the key combination to a standard format
        normalized = self._normalize_key_combo(key_combo)

        # Check if this hotkey is already registered
        if normalized in self.hotkeys:
            logger.warning(f"Hotkey {key_combo} already registered, overwriting")

        self.hotkeys[normalized] = {
            "callback": callback,
            "original": key_combo,
            "description": description
        }

        logger.debug(f"Registered hotkey: {key_combo}")
        return True

    def unregister_hotkey(self, key_combo: str) -> bool:
        """
        Unregister a hotkey combination.

        Args:
            key_combo: Keyboard combination to unregister

        Returns:
            True if unregistered successfully, False otherwise
        """
        normalized = self._normalize_key_combo(key_combo)

        if normalized in self.hotkeys:
            del self.hotkeys[normalized]
            logger.debug(f"Unregistered hotkey: {key_combo}")
            return True

        logger.warning(f"Hotkey {key_combo} not registered")
        return False

    def _normalize_key_combo(self, key_combo: str) -> str:
        """
        Normalize a key combination to a standard format.

        Args:
            key_combo: Keyboard combination (e.g., "ctrl+alt+s")

        Returns:
            Normalized key combination
        """
        # Convert to lowercase
        key_combo = key_combo.lower()

        # Split by + and sort modifiers
        parts = key_combo.split("+")

        # Special handling for plus key
        if "" in parts and "+" in key_combo:
            # Handle case like "ctrl++"
            parts.remove("")
            parts.append("+")

        # Separate modifiers from the main key
        modifiers = [p for p in parts if p in ("ctrl", "alt", "shift", "win")]
        other_keys = [p for p in parts if p not in modifiers]

        # Sort modifiers in a consistent order
        modifiers.sort()

        # Reconstruct the key combination
        normalized = "+".join(modifiers + other_keys)

        return normalized

    def start_listener(self) -> bool:
        """
        Start listening for hotkeys in a background thread.

        Returns:
            True if started successfully, False otherwise
        """
        if self.listener_running:
            logger.warning("Hotkey listener already running")
            return False

        try:
            # This is a placeholder for actual hotkey listening code
            # In a real implementation, this would start a keyboard hook

            # Import keyboard library only when needed
            # (keyboard library is a third-party library for global keyboard hooks)
            try:
                import keyboard
                self._start_keyboard_listener(keyboard)
                return True
            except ImportError:
                logger.error("Keyboard library not available, hotkeys disabled")
                return False
        except Exception as e:
            logger.error(f"Error starting hotkey listener: {e}")
            return False

    def _start_keyboard_listener(self, keyboard_module) -> None:
        """
        Start listening for hotkeys using the keyboard module.

        Args:
            keyboard_module: Imported keyboard module
        """
        # Mark as running before starting thread
        self.listener_running = True

        def listener_thread_func():
            logger.debug("Hotkey listener thread started")

            # Register all current hotkeys
            for key_combo, info in self.hotkeys.items():
                keyboard_module.add_hotkey(
                    key_combo,
                    lambda k=key_combo: self._trigger_hotkey(k)
                )

            # Keep thread alive until listener_running is False
            while self.listener_running:
                keyboard_module.wait(1)

            # Unregister all hotkeys
            keyboard_module.unhook_all_hotkeys()
            logger.debug("Hotkey listener thread stopped")

        # Start the listener thread
        self.listener_thread = threading.Thread(
            target=listener_thread_func,
            daemon=True
        )
        self.listener_thread.start()
        logger.debug("Hotkey listener thread started")

    def stop_listener(self) -> None:
        """Stop listening for hotkeys."""
        if not self.listener_running:
            logger.warning("Hotkey listener not running")
            return

        # Set flag to stop listener thread
        self.listener_running = False

        # Wait for thread to stop (with timeout)
        if self.listener_thread and self.listener_thread.is_alive():
            self.listener_thread.join(timeout=1.0)

        logger.debug("Hotkey listener stopped")

    def _trigger_hotkey(self, key_combo: str) -> None:
        """
        Trigger a hotkey's callback.

        Args:
            key_combo: Keyboard combination that was triggered
        """
        if key_combo not in self.hotkeys:
            logger.warning(f"Hotkey triggered but not registered: {key_combo}")
            return

        info = self.hotkeys[key_combo]

        try:
            logger.debug(f"Hotkey triggered: {info['original']}")
            info["callback"]()
        except Exception as e:
            logger.error(f"Error in hotkey callback for {key_combo}: {e}")

    def get_all_hotkeys(self) -> Dict[str, str]:
        """
        Get a dictionary of all registered hotkeys and their descriptions.

        Returns:
            Dictionary mapping key combinations to descriptions
        """
        return {info["original"]: info["description"]
                for key_combo, info in self.hotkeys.items()}