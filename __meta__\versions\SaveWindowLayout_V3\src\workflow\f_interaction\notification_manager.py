"""
Notification Manager Module

This module handles user notifications, including system tray notifications,
in-app messages, and sound alerts.
"""

from typing import Dict, List, Optional, Any, Callable
import time
import threading
import os

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class NotificationManager:
    """
    Manages user notifications.

    Responsibilities:
    - Display system tray notifications
    - Show in-app messages
    - Play sound alerts
    - Manage notification history
    """

    def __init__(self, enabled: bool = True, use_sounds: bool = True,
                max_history: int = 100):
        """
        Initialize the notification manager.

        Args:
            enabled: Whether notifications are enabled
            use_sounds: Whether to play sounds with notifications
            max_history: Maximum number of notifications to keep in history
        """
        self.enabled = enabled
        self.use_sounds = use_sounds
        self.max_history = max_history
        self.system_tray_available = False
        self.notification_history: List[Dict[str, Any]] = []

        # Check if we can use system tray notifications
        self._check_system_tray_availability()

        logger.debug(f"NotificationManager initialized (enabled={enabled}, sounds={use_sounds})")

    def _check_system_tray_availability(self) -> None:
        """Check if system tray notifications are available."""
        try:
            # This is a placeholder for actual system tray detection code
            # In a real implementation, this would check for system tray support
            self.system_tray_available = True
            logger.debug("System tray notifications available")
        except Exception as e:
            self.system_tray_available = False
            logger.debug(f"System tray notifications not available: {e}")

    def notify(self, title: str, message: str, level: str = "info",
              play_sound: bool = True, timeout: int = 5) -> None:
        """
        Show a notification to the user.

        Args:
            title: Notification title
            message: Notification message
            level: Notification level (info, warning, error)
            play_sound: Whether to play a sound with the notification
            timeout: Time in seconds to show the notification
        """
        if not self.enabled:
            logger.debug(f"Notification suppressed (disabled): {title}")
            return

        # Add to history
        notification = {
            "timestamp": time.time(),
            "title": title,
            "message": message,
            "level": level
        }

        self.notification_history.append(notification)

        # Trim history if needed
        if len(self.notification_history) > self.max_history:
            self.notification_history = self.notification_history[-self.max_history:]

        # Log the notification
        log_method = getattr(logger, level.lower(), logger.info)
        log_method(f"NOTIFICATION: {title} - {message}")

        # Show system tray notification if available
        if self.system_tray_available:
            self._show_system_notification(title, message, level, timeout)

        # Play sound if enabled
        if play_sound and self.use_sounds:
            self._play_notification_sound(level)

    def _show_system_notification(self, title: str, message: str,
                                level: str = "info", timeout: int = 5) -> None:
        """
        Show a system tray notification.

        Args:
            title: Notification title
            message: Notification message
            level: Notification level
            timeout: Time in seconds to show the notification
        """
        # This is a placeholder for actual system notification code
        # In a real implementation, this would use platform-specific APIs
        logger.debug(f"System notification: {title} - {message} ({level})")

        # On Windows, we could use win10toast or similar libraries
        # On macOS, we could use pyobjc and NSUserNotification
        # On Linux, we could use notify2 or similar libraries

    def _play_notification_sound(self, level: str = "info") -> None:
        """
        Play a notification sound.

        Args:
            level: Notification level determining which sound to play
        """
        # This is a placeholder for actual sound playing code
        # In a real implementation, this would play different sounds based on level
        logger.debug(f"Playing notification sound for level: {level}")

        # We could use winsound on Windows or pydub on any platform

    def clear_history(self) -> None:
        """Clear the notification history."""
        self.notification_history = []
        logger.debug("Notification history cleared")

    def get_recent_notifications(self, count: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent notifications from history.

        Args:
            count: Maximum number of notifications to return

        Returns:
            List of recent notifications
        """
        return self.notification_history[-count:]

    def set_enabled(self, enabled: bool) -> None:
        """
        Enable or disable notifications.

        Args:
            enabled: Whether notifications should be enabled
        """
        self.enabled = enabled
        logger.debug(f"Notifications {'enabled' if enabled else 'disabled'}")

    def set_sound_enabled(self, enabled: bool) -> None:
        """
        Enable or disable notification sounds.

        Args:
            enabled: Whether sounds should be enabled
        """
        self.use_sounds = enabled
        logger.debug(f"Notification sounds {'enabled' if enabled else 'disabled'}")