"""
Application Controller Module

This module provides the main application controller that integrates all components
of the window management system, coordinating between detection, categorization,
layout, automation, state, and interaction layers.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

# Import components from each layer
from ..a_detection import WindowDetector
from ..b_categorization import WindowCategorizer
from ..c_layout import LayoutManager
from ..e_state import ConfigManager, SessionManager, UserPreferences
from ..f_interaction import UIManager, CommandHandler, NotificationManager, HotkeyManager
from .window_controller import WindowController


class ApplicationController:
    """
    Main application controller that coordinates all components.

    Responsibilities:
    - Initialize all system components
    - Coordinate between different layers
    - Set up interaction capabilities (hotkeys, commands)
    - Handle application lifecycle (startup, shutdown)
    """

    def __init__(self, config_dir: Optional[str] = None, headless: bool = False):
        """
        Initialize the application controller.

        Args:
            config_dir: Directory for configuration files
            headless: Whether to run in headless mode (no UI)
        """
        self.headless = headless

        # Initialize state components
        self.config_manager = ConfigManager(config_dir)
        self.session_manager = SessionManager(
            os.path.join(config_dir, "session.json") if config_dir else None
        )
        self.preferences = UserPreferences(
            os.path.join(config_dir, "preferences.json") if config_dir else None
        )

        # Initialize interaction components
        self.ui_manager = UIManager(headless=headless)
        self.command_handler = CommandHandler()
        self.notification_manager = NotificationManager(
            enabled=self.preferences.get("ui", "show_notifications", True)
        )
        self.hotkey_manager = HotkeyManager()

        # Initialize window management components
        self.window_controller = WindowController(
            layouts_dir=self.config_manager.get_layouts_dir()
        )

        # Register commands and hotkeys
        self._register_commands()
        self._register_hotkeys()

        # Store last active window for monitoring
        self._last_active_hwnd = None

        logger.debug("ApplicationController initialized")

    def _register_commands(self) -> None:
        """Register all application commands with the command handler."""
        # Register layout commands
        self.command_handler.register_command(
            "save-layout",
            self._cmd_save_layout,
            "Save current window layout",
            [{"name": "name", "help": "Name for the layout", "required": True}]
        )

        self.command_handler.register_command(
            "apply-layout",
            self._cmd_apply_layout,
            "Apply a saved window layout",
            [{"name": "name", "help": "Name of the layout to apply", "required": True}]
        )

        self.command_handler.register_command(
            "list-layouts",
            self._cmd_list_layouts,
            "List all saved layouts",
            []
        )

        # Register window management commands
        self.command_handler.register_command(
            "list-windows",
            self._cmd_list_windows,
            "List all open windows",
            []
        )

        self.command_handler.register_command(
            "list-windows-by-category",
            self._cmd_list_windows_by_category,
            "List windows of a specific category",
            [{"name": "category", "help": "Category name", "required": True}]
        )

        self.command_handler.register_command(
            "find-window",
            self._cmd_find_window,
            "Find windows by title",
            [{"name": "title", "help": "Window title to search for", "required": True}]
        )

        self.command_handler.register_command(
            "monitor-window",
            self._cmd_monitor_window,
            "Display information about the active window",
            []
        )

        # Register arrangement commands
        self.command_handler.register_command(
            "tile-horizontal",
            self._cmd_tile_horizontal,
            "Tile windows horizontally",
            []
        )

        self.command_handler.register_command(
            "tile-vertical",
            self._cmd_tile_vertical,
            "Tile windows vertically",
            []
        )

        self.command_handler.register_command(
            "cascade",
            self._cmd_cascade,
            "Cascade windows",
            []
        )

        logger.debug("Registered application commands")

    def _register_hotkeys(self) -> None:
        """Register all application hotkeys."""
        # Get hotkey settings from preferences
        hotkey_prefs = self.preferences.get_section("hotkeys")

        # Register save layout hotkey
        save_hotkey = hotkey_prefs.get("save_layout", "ctrl+alt+s")
        self.hotkey_manager.register_hotkey(
            save_hotkey,
            self._hotkey_save_layout,
            "Save current window layout"
        )

        # Register apply layout hotkey
        apply_hotkey = hotkey_prefs.get("apply_layout", "ctrl+alt+a")
        self.hotkey_manager.register_hotkey(
            apply_hotkey,
            self._hotkey_apply_layout,
            "Apply saved window layout"
        )

        # Register tile windows hotkey
        tile_h_hotkey = hotkey_prefs.get("tile_horizontal", "ctrl+alt+h")
        self.hotkey_manager.register_hotkey(
            tile_h_hotkey,
            self._hotkey_tile_horizontal,
            "Tile windows horizontally"
        )

        # Register tile windows vertically hotkey
        tile_v_hotkey = hotkey_prefs.get("tile_vertical", "ctrl+alt+v")
        self.hotkey_manager.register_hotkey(
            tile_v_hotkey,
            self._hotkey_tile_vertical,
            "Tile windows vertically"
        )

        # Register monitor window hotkey
        monitor_hotkey = hotkey_prefs.get("monitor_window", "ctrl+alt+m")
        self.hotkey_manager.register_hotkey(
            monitor_hotkey,
            self._hotkey_monitor_window,
            "Monitor active window"
        )

        logger.debug("Registered application hotkeys")

    def start(self) -> None:
        """Start the application controller and all required components."""
        # Initialize UI if not in headless mode
        if not self.headless:
            self.ui_manager.initialize_ui()

        # Start hotkey listener
        if self.preferences.get("behavior", "enable_hotkeys", True):
            success = self.hotkey_manager.start_listener()
            if success:
                # Show notification
                self.notification_manager.notify(
                    "Window Manager",
                    "Hotkeys activated",
                    "info"
                )
            else:
                logger.warning("Failed to start hotkey listener")

        # Load session if enabled
        if self.preferences.get("behavior", "restore_session", True):
            self.session_manager.load_session()

            # Apply startup layout if configured
            startup_action = self.preferences.get("behavior", "startup_action", "none")
            if startup_action == "restore_last":
                last_layout = self.session_manager.get_last_active_layout()
                if last_layout:
                    self._apply_layout(last_layout)
            elif startup_action == "apply_specific":
                startup_layout = self.preferences.get("behavior", "startup_layout", "")
                if startup_layout:
                    self._apply_layout(startup_layout)

        logger.info("Application controller started")

    def stop(self) -> None:
        """Stop the application controller and perform cleanup."""
        # Save session if enabled
        if self.preferences.get("behavior", "save_session", True):
            self.session_manager.save_session()

        # Stop hotkey listener
        self.hotkey_manager.stop_listener()

        # Clean up UI resources
        self.ui_manager.cleanup()

        logger.info("Application controller stopped")

    # Command handlers
    def _cmd_save_layout(self, args) -> bool:
        """
        Handle save layout command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        name = getattr(args, "name", None)
        if not name:
            logger.error("No layout name provided")
            return False

        try:
            layout_path = self.window_controller.save_layout(name)
            print(f"Layout saved to: {layout_path}")

            # Record layout in session
            self.session_manager.record_layout_use(name)

            # Record operation
            self.session_manager.record_operation(
                "save_layout",
                {"name": name, "path": layout_path}
            )

            return True
        except Exception as e:
            logger.error(f"Error saving layout: {e}")
            print(f"Error saving layout: {e}")
            return False

    def _cmd_apply_layout(self, args) -> bool:
        """
        Handle apply layout command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        name = getattr(args, "name", None)
        if not name:
            logger.error("No layout name provided")
            return False

        try:
            return self._apply_layout(name)
        except Exception as e:
            logger.error(f"Error applying layout: {e}")
            print(f"Error applying layout: {e}")
            return False

    def _apply_layout(self, name: str) -> bool:
        """
        Apply a layout.

        Args:
            name: Layout name

        Returns:
            True if successful
        """
        # Get layout preferences
        layout_prefs = self.preferences.get_section("layout_defaults")

        # Apply layout with preferences
        window_count = self.window_controller.apply_layout(
            name,
            restore_explorer=layout_prefs.get("restore_explorer", True),
            restore_browsers=layout_prefs.get("restore_browsers", True),
            restore_documents=layout_prefs.get("restore_documents", True),
            restore_others=layout_prefs.get("restore_others", True)
        )

        if window_count > 0:
            print(f"Applied layout '{name}', restored {window_count} windows.")

            # Show notification
            self.notification_manager.notify(
                "Layout Applied",
                f"Restored {window_count} windows",
                "info"
            )

            # Record layout in session
            self.session_manager.record_layout_use(name)

            # Record operation
            self.session_manager.record_operation(
                "apply_layout",
                {"name": name, "window_count": window_count}
            )

            return True
        else:
            print(f"Failed to apply layout '{name}' or no windows were restored.")
            return False

    def _cmd_list_layouts(self, args) -> bool:
        """
        Handle list layouts command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        layouts = self.window_controller.list_layouts()

        if not layouts:
            print("No saved layouts found.")
            return True

        print(f"\nFound {len(layouts)} saved layouts:\n")
        for layout in layouts:
            print(f"  {layout}")
        print()

        return True

    def _cmd_list_windows(self, args) -> bool:
        """
        Handle list windows command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        windows = self.window_controller.get_all_windows()

        if not windows:
            print("No windows found.")
            return True

        print(f"\nFound {len(windows)} windows:\n")

        # Format the output
        for hwnd, window in windows.items():
            title = window.title[:50] + "..." if len(window.title) > 50 else window.title
            if not title:
                title = "<No Title>"

            # Get position and size
            pos = window.base_info.position
            size = window.base_info.size

            # Get process info if available
            proc_info = ""
            if window.base_info.process:
                proc_info = f"- {window.base_info.process.exe_name} (PID: {window.base_info.process.pid})"

            print(f"[{window.category.name}] {title} {proc_info}")
            print(f"    Position: ({pos.x}, {pos.y}), Size: {size.width}x{size.height}")
            print(f"    HWND: {hwnd}, Class: {window.class_name}")
            print()

        return True

    def _cmd_list_windows_by_category(self, args) -> bool:
        """
        Handle list windows by category command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        category_name = getattr(args, "category", None)
        if not category_name:
            logger.error("No category name provided")
            print("Error: No category name provided")
            return False

        try:
            # Import the WindowCategory enum
            from ..b_categorization.window_categorizer import WindowCategory

            # Try to convert the string to an enum value
            try:
                category = WindowCategory[category_name.upper()]
            except KeyError:
                print(f"Unknown category: {category_name}")
                print("Available categories:", ", ".join([c.name for c in WindowCategory]))
                return False

            # Get windows of the specified category
            windows = self.window_controller.get_windows_by_category(category)

            if not windows:
                print(f"No windows found in category: {category.name}")
                return True

            print(f"\nFound {len(windows)} windows in category {category.name}:\n")

            # Format the output
            for i, window in enumerate(windows, 1):
                title = window.title[:50] + "..." if len(window.title) > 50 else window.title
                if not title:
                    title = "<No Title>"

                print(f"{i}. [{window.base_info.hwnd}] {title}")

                # Show specialized info based on category
                if category == WindowCategory.EXPLORER and hasattr(window, 'location_path'):
                    print(f"   Location: {window.location_path}")
                elif category == WindowCategory.BROWSER and hasattr(window, 'browser_type'):
                    if hasattr(window, 'browser_type'):
                        print(f"   Browser: {window.browser_type.name}")
                    if hasattr(window, 'url'):
                        print(f"   URL: {window.url}")
                elif category == WindowCategory.DOCUMENT:
                    if hasattr(window, 'app_name'):
                        print(f"   Application: {window.app_name}")
                    if hasattr(window, 'document_name'):
                        print(f"   Document: {window.document_name}")

                # Show process info if available
                if window.base_info.process:
                    process = window.base_info.process
                    if process.exe_name:
                        print(f"   Process: {process.exe_name} (PID: {process.pid})")

                print()

            return True

        except Exception as e:
            logger.error(f"Error listing windows by category: {e}")
            print(f"Error listing windows by category: {e}")
            return False

    def _cmd_monitor_window(self, args) -> bool:
        """
        Handle monitor window command. Shows information about the active window.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        # Get the currently active window
        active_window = self.window_controller.get_active_window()

        if not active_window:
            return True

        # Only update if the active window has changed
        if active_window.base_info.hwnd != self._last_active_hwnd:
            # Clear console (this works on both Windows and Unix)
            os.system('cls' if os.name == 'nt' else 'clear')

            # Display window information
            print("ACTIVE WINDOW:")
            print(f"Title: {active_window.title}")
            print(f"HWND: {active_window.base_info.hwnd}")
            print(f"Class: {active_window.class_name}")
            print(f"Category: {active_window.category.name}")

            # Get position and size
            pos = active_window.base_info.position
            size = active_window.base_info.size
            print(f"Position: ({pos.x}, {pos.y})")
            print(f"Size: {size.width}x{size.height}")

            # Get process info if available
            if active_window.base_info.process:
                print("\nPROCESS INFO:")
                print(f"Name: {active_window.base_info.process.exe_name}")
                print(f"PID: {active_window.base_info.process.pid}")
                print(f"Path: {active_window.base_info.process.exe_path}")

            # Display specialized info based on window type
            if active_window.category.name == "BROWSER" and hasattr(active_window, 'url'):
                print("\nBROWSER INFO:")
                print(f"Browser Type: {active_window.browser_type.name if hasattr(active_window, 'browser_type') else 'Unknown'}")
                print(f"URL: {active_window.url}")

            elif active_window.category.name == "EXPLORER" and hasattr(active_window, 'location_path'):
                print("\nEXPLORER INFO:")
                print(f"Path: {active_window.location_path}")

            # Update the last active window handle
            self._last_active_hwnd = active_window.base_info.hwnd

        return True

    def _cmd_find_window(self, args) -> bool:
        """
        Handle find window command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        title = getattr(args, "title", None)
        if not title:
            logger.error("No window title provided")
            return False

        windows = self.window_controller.find_windows(title=title)

        if not windows:
            print(f"No windows found with title containing: {title}")
            return True

        print(f"\nFound {len(windows)} windows with title containing '{title}':\n")

        # Format the output
        for hwnd, window in windows.items():
            title = window.title[:50] + "..." if len(window.title) > 50 else window.title
            if not title:
                title = "<No Title>"

            # Get position and size
            pos = window.base_info.position
            size = window.base_info.size

            print(f"[{window.category.name}] {title}")
            print(f"    Position: ({pos.x}, {pos.y}), Size: {size.width}x{size.height}")
            print(f"    HWND: {hwnd}, Class: {window.class_name}")
            print()

        return True

    def _cmd_tile_horizontal(self, args) -> bool:
        """
        Handle tile horizontal command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        # Get visible windows
        windows = self.window_controller.get_all_windows()
        window_handles = list(windows.keys())

        # Import from automation module
        from ..d_automation import tile_windows_horizontally

        # Tile windows
        success = tile_windows_horizontally(window_handles)

        if success:
            print(f"Tiled {len(window_handles)} windows horizontally.")

            # Show notification
            self.notification_manager.notify(
                "Windows Tiled",
                f"Tiled {len(window_handles)} windows horizontally",
                "info"
            )

            # Record operation
            self.session_manager.record_operation(
                "tile_horizontal",
                {"window_count": len(window_handles)}
            )

        return success

    def _cmd_tile_vertical(self, args) -> bool:
        """
        Handle tile vertical command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        # Get visible windows
        windows = self.window_controller.get_all_windows()
        window_handles = list(windows.keys())

        # Import from automation module
        from ..d_automation import tile_windows_vertically

        # Tile windows
        success = tile_windows_vertically(window_handles)

        if success:
            print(f"Tiled {len(window_handles)} windows vertically.")

            # Show notification
            self.notification_manager.notify(
                "Windows Tiled",
                f"Tiled {len(window_handles)} windows vertically",
                "info"
            )

            # Record operation
            self.session_manager.record_operation(
                "tile_vertical",
                {"window_count": len(window_handles)}
            )

        return success

    def _cmd_cascade(self, args) -> bool:
        """
        Handle cascade windows command.

        Args:
            args: Command arguments

        Returns:
            True if successful
        """
        # Get visible windows
        windows = self.window_controller.get_all_windows()
        window_handles = list(windows.keys())

        # Import from automation module
        from ..d_automation import cascade_windows

        # Cascade windows
        success = cascade_windows(window_handles)

        if success:
            print(f"Cascaded {len(window_handles)} windows.")

            # Show notification
            self.notification_manager.notify(
                "Windows Cascaded",
                f"Cascaded {len(window_handles)} windows",
                "info"
            )

            # Record operation
            self.session_manager.record_operation(
                "cascade",
                {"window_count": len(window_handles)}
            )

        return success

    # Hotkey callbacks
    def _hotkey_save_layout(self) -> None:
        """Handle save layout hotkey."""
        # Show UI for layout name input if available
        if not self.headless:
            # This would show a dialog to get the layout name
            # For now, use a default name with timestamp
            import time
            name = f"layout_{int(time.time())}"
        else:
            # Use a default name with timestamp
            import time
            name = f"layout_{int(time.time())}"

        # Save layout
        try:
            layout_path = self.window_controller.save_layout(name)

            # Show notification
            self.notification_manager.notify(
                "Layout Saved",
                f"Saved as '{name}'",
                "info"
            )

            # Record layout in session
            self.session_manager.record_layout_use(name)

            # Record operation
            self.session_manager.record_operation(
                "save_layout",
                {"name": name, "path": layout_path}
            )

        except Exception as e:
            logger.error(f"Error in save layout hotkey handler: {e}")
            self.notification_manager.notify(
                "Error Saving Layout",
                str(e),
                "error"
            )

    def _hotkey_apply_layout(self) -> None:
        """Handle apply layout hotkey."""
        # If we have a last active layout, use that
        last_layout = self.session_manager.get_last_active_layout()

        if last_layout:
            try:
                self._apply_layout(last_layout)
            except Exception as e:
                logger.error(f"Error in apply layout hotkey handler: {e}")
                self.notification_manager.notify(
                    "Error Applying Layout",
                    str(e),
                    "error"
                )
        else:
            # Show notification
            self.notification_manager.notify(
                "No Layout",
                "No previous layout to apply",
                "warning"
            )

    def _hotkey_tile_horizontal(self) -> None:
        """Handle tile horizontal hotkey."""
        # Get visible windows
        windows = self.window_controller.get_all_windows()
        window_handles = list(windows.keys())

        # Import from automation module
        from ..d_automation import tile_windows_horizontally

        # Tile windows
        success = tile_windows_horizontally(window_handles)

        if success:
            # Show notification
            self.notification_manager.notify(
                "Windows Tiled",
                f"Tiled {len(window_handles)} windows horizontally",
                "info"
            )

            # Record operation
            self.session_manager.record_operation(
                "tile_horizontal",
                {"window_count": len(window_handles)}
            )
        else:
            # Show error notification
            self.notification_manager.notify(
                "Tiling Failed",
                "Failed to tile windows horizontally",
                "error"
            )

    def _hotkey_tile_vertical(self) -> None:
        """
        Handle the tile windows vertically hotkey.
        """
        logger.debug("Tile windows vertically hotkey triggered")

        # Show notification
        self.notification_manager.notify(
            "Window Manager",
            "Tiling windows vertically",
            "info"
        )

        # Execute the command
        result = self.command_handler.execute_command("tile-vertical")

        # Show result notification
        if result:
            self.notification_manager.notify(
                "Window Manager",
                "Windows tiled vertically",
                "info"
            )
        else:
            self.notification_manager.notify(
                "Window Manager",
                "Failed to tile windows vertically",
                "error"
            )

    def _hotkey_monitor_window(self) -> None:
        """
        Handle the monitor active window hotkey.
        """
        logger.debug("Monitor active window hotkey triggered")

        # Show notification
        self.notification_manager.notify(
            "Window Manager",
            "Monitoring active window",
            "info"
        )

        # Execute the command
        result = self.command_handler.execute_command("monitor-window")

        # No need for result notification as the monitor command
        # already shows window information