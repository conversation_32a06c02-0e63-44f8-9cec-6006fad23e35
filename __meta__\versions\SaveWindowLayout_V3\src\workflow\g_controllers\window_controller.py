"""
Window Controller Module

This module provides the central controller for window management operations,
coordinating between different stages of the window management workflow.

Key responsibilities:
1. Initialize and coordinate workflow components
2. Provide a high-level API for window management operations
3. Handle error logging and recovery
4. Maintain system state
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Set
import win32gui

# Import workflow components using the new alphabetical structure
from ..a_detection.window_detector import WindowDetector, WindowInfo
from ..b_categorization.window_categorizer import WindowCategorizer, CategorizedWindowInfo, WindowCategory
from ..c_layout.layout_manager import LayoutManager
from ..d_automation import position_window_with_state, move_window

# Try to import logger
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class WindowController:
    """
    Central controller for window management operations.

    This class coordinates between different stages of the window management workflow,
    providing a high-level API for window-related operations.
    """

    def __init__(self, layouts_dir: Optional[str] = None):
        """
        Initialize the window controller.

        Args:
            layouts_dir: Optional directory for storing layout files
        """
        # Initialize workflow components
        self.detector = WindowDetector()
        self.categorizer = WindowCategorizer(self.detector)
        self.layout_manager = LayoutManager(layouts_dir)

        # Initialize state
        self.windows: Dict[int, CategorizedWindowInfo] = {}
        self.active_window: Optional[CategorizedWindowInfo] = None

        # Initial window refresh
        self.refresh_windows()

        logger.debug("WindowController initialized")

    def refresh_windows(self) -> Dict[int, CategorizedWindowInfo]:
        """
        Refresh the list of windows.

        Returns:
            Dict of window handles to categorized window information
        """
        # Detect all windows
        self.detector.detect_windows(visible_only=True)
        logger.debug(f"Detected {len(self.detector.get_all_windows())} windows")

        # Categorize windows
        self.windows = self.categorizer.refresh()
        logger.debug(f"Categorized {len(self.windows)} windows")

        # Update active window
        active_hwnd = win32gui.GetForegroundWindow()
        self.active_window = self.windows.get(active_hwnd)

        return self.windows

    def get_all_windows(self) -> Dict[int, CategorizedWindowInfo]:
        """
        Get all detected windows.

        Returns:
            Dict of window handles to categorized window information
        """
        return self.windows

    def get_active_window(self) -> Optional[CategorizedWindowInfo]:
        """
        Get the currently active window.

        Returns:
            Active window info or None if not found
        """
        # Always get the latest active window handle
        active_hwnd = win32gui.GetForegroundWindow()

        # If active window isn't in our list or has changed, refresh
        if active_hwnd not in self.windows or active_hwnd != (self.active_window.base_info.hwnd if self.active_window else None):
            self.active_window = self.windows.get(active_hwnd)

            # If still not found, try a full refresh
            if not self.active_window:
                self.refresh_windows()
                self.active_window = self.windows.get(active_hwnd)

        return self.active_window

    def get_windows_by_category(self, category_name: str) -> Dict[int, CategorizedWindowInfo]:
        """
        Get windows filtered by category.

        Args:
            category_name: Name of the category to filter by

        Returns:
            Dict of window handles to categorized window information
        """
        try:
            # Try to convert string to enum
            category = WindowCategory[category_name.upper()]

            # Filter windows by category
            return {
                hwnd: window for hwnd, window in self.windows.items()
                if window.category == category
            }

        except (KeyError, ValueError):
            logger.error(f"Invalid category: {category_name}")
            return {}

    def find_windows(self,
                    title: Optional[str] = None,
                    class_name: Optional[str] = None,
                    process_name: Optional[str] = None) -> Dict[int, CategorizedWindowInfo]:
        """
        Find windows by various criteria.

        Args:
            title: Substring to match in window title
            class_name: Substring to match in window class name
            process_name: Substring to match in process name

        Returns:
            Dict of window handles to categorized window information
        """
        result = {}

        for hwnd, window in self.windows.items():
            match = True

            # Match title if provided
            if title and (not window.title or title.lower() not in window.title.lower()):
                match = False

            # Match class name if provided
            if match and class_name and (not window.class_name or class_name.lower() not in window.class_name.lower()):
                match = False

            # Match process name if provided
            if match and process_name and (
                not window.base_info.process or
                not window.base_info.process.exe_name or
                process_name.lower() not in window.base_info.process.exe_name.lower()
            ):
                match = False

            # Add to result if all criteria match
            if match:
                result[hwnd] = window

        return result

    # Layout management methods
    def save_layout(self, name: Optional[str] = None) -> str:
        """
        Save the current window layout.

        Args:
            name: Optional name for the layout

        Returns:
            Path to the saved layout file
        """
        # Refresh windows to ensure we have the latest information
        self.refresh_windows()

        # Save the layout
        layout_path = self.layout_manager.save_layout(self.windows, name)

        if layout_path:
            logger.info(f"Saved layout to {layout_path}")
        else:
            logger.error("Failed to save layout")

        return layout_path

    def apply_layout(self, name: str,
                    restore_explorer: bool = True,
                    restore_browsers: bool = True,
                    restore_documents: bool = True,
                    restore_others: bool = True) -> int:
        """
        Apply a saved window layout.

        Args:
            name: Name of the layout to apply
            restore_explorer: Whether to restore Explorer windows
            restore_browsers: Whether to restore browser windows
            restore_documents: Whether to restore document windows
            restore_others: Whether to restore other windows

        Returns:
            Number of windows successfully restored
        """
        # Refresh windows to ensure we have the latest information
        self.refresh_windows()

        # Apply the layout
        restored_count = self.layout_manager.apply_layout(
            name,
            restore_explorer=restore_explorer,
            restore_browsers=restore_browsers,
            restore_documents=restore_documents,
            restore_others=restore_others
        )

        logger.info(f"Applied layout '{name}', restored {restored_count} windows")
        return restored_count

    def list_layouts(self) -> List[str]:
        """
        List all available layouts.

        Returns:
            List of layout names
        """
        return self.layout_manager.list_layouts()

    # Window positioning methods
    def position_window(self, hwnd: int, x: int, y: int, width: int, height: int) -> bool:
        """
        Position a window.

        Args:
            hwnd: Window handle
            x: X-coordinate
            y: Y-coordinate
            width: Width
            height: Height

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use the automation module function
            result = position_window_with_state(hwnd, x, y, width, height)
            logger.debug(f"Positioned window {hwnd} to ({x}, {y}) with size {width}x{height}")
            return result
        except Exception as e:
            logger.error(f"Error positioning window {hwnd}: {e}")
            return False