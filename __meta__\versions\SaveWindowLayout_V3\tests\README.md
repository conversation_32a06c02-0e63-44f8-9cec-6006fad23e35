# Testing Strategy for Window Manager

This directory contains automated tests for the Window Manager application. The testing approach is designed to be comprehensive, covering various levels of testing from individual components to end-to-end integration.

## Test Structure

The tests are organized into several directories:

-   `unit/`: Unit tests for individual components
-   `integration/`: Integration tests for multiple components working together
-   `mocks/`: Mock implementations of external dependencies
-   `fixtures/`: Test data and fixtures

## Running Tests

You can run the tests using pytest directly:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/unit/test_window_detector.py

# Run with coverage reporting
pytest --cov=src --cov-report=term --cov-report=html:coverage_html
```

Alternatively, you can use the provided `run_tests.py` script:

```bash
python run_tests.py
```

## Mock System

Since the Window Manager interacts with the Windows API, we've implemented a comprehensive mocking system in `tests/mocks/win32_mock.py`. This allows tests to run without actually manipulating windows on the system. The mock system:

1. Simulates a collection of windows with various properties
2. Implements mock versions of Win32 API functions like `EnumWindows`, `GetWindowText`, etc.
3. Maintains state between function calls (e.g., when a window is moved or focused)

## Test Coverage Goals

The test suite aims to cover:

1. **Window Detection Layer**: Tests for finding, listing, and filtering windows
2. **Window Categorization Layer**: Tests for correctly categorizing windows by type
3. **Layout Management Layer**: Tests for saving and loading window layouts
4. **Automation Layer**: Tests for manipulating window properties
5. **Controller Layer**: Tests for coordinating between components
6. **Integration Tests**: Tests for the full window management flow

## Adding New Tests

When adding new features, please also add corresponding tests. For a new feature:

1. Add unit tests for any new components or functions
2. Update integration tests if the feature affects multiple components
3. Update mock implementations if the feature interacts with the Windows API

## Continuous Integration

Tests are automatically run on pull requests and commits to the main branch. Make sure all tests pass before submitting a PR.

## Testing Best Practices

1. Use fixtures to set up test data and environments
2. Mock external dependencies to ensure tests are fast and deterministic
3. Test edge cases and error conditions, not just the happy path
4. Aim for high code coverage but focus on functionality, not just lines of code
