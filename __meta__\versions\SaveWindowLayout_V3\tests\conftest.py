"""
Configuration for pytest.

This module sets up test fixtures and configures mock imports for tests.
"""

import os
import sys
import shutil
import pytest
import tempfile
from pathlib import Path
from unittest import mock

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Mock the win32gui and win32process modules
sys.modules["win32gui"] = __import__("tests.mocks.win32gui_mock", fromlist=["*"])
sys.modules["win32process"] = __import__("tests.mocks.win32process_mock", fromlist=["*"])
sys.modules["win32api"] = __import__("tests.mocks.win32process_mock", fromlist=["*"])


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing that's cleaned up afterward."""
    with tempfile.TemporaryDirectory() as tmpdirname:
        yield Path(tmpdirname)


@pytest.fixture
def temp_layouts_dir(temp_dir):
    """Create a temporary layouts directory for testing."""
    layouts_dir = temp_dir / "layouts"
    layouts_dir.mkdir(exist_ok=True)
    return layouts_dir


@pytest.fixture
def mock_windows():
    """Return a list of mock window objects for testing."""
    from src.workflow.a_detection.window_detector import Window

    windows = [
        Window(1001, "Google Chrome", "Chrome_WidgetWin_1", (100, 100, 900, 700), "chrome.exe", 5001),
        Window(1002, "Mozilla Firefox - Window Manager Documentation", "MozillaWindowClass", (200, 200, 1000, 800), "firefox.exe", 5002),
        Window(1003, "Documents", "CabinetWClass", (300, 300, 800, 600), "explorer.exe", 5003),
        Window(1005, "window_manager.py - Visual Studio Code", "Chrome_WidgetWin_0", (500, 100, 1300, 900), "Code.exe", 5004),
        Window(1006, "Python IDLE", "TkTopLevel", (600, 200, 1100, 700), "pythonw.exe", 5005),
        Window(1007, "Project Plan.docx - Word", "OpusApp", (700, 300, 1400, 900), "WINWORD.EXE", 5006),
        Window(1008, "Budget.xlsx - Excel", "XLMAIN", (800, 400, 1500, 1000), "EXCEL.EXE", 5007),
    ]

    return windows


@pytest.fixture
def mock_categorized_windows(mock_windows):
    """Return categorized mock window objects for testing."""
    from src.workflow.b_categorization.window_categorizer import WindowCategory

    categorized_windows = {
        WindowCategory.BROWSER: [mock_windows[0], mock_windows[1]],
        WindowCategory.EXPLORER: [mock_windows[2]],
        WindowCategory.DEVELOPMENT: [mock_windows[3], mock_windows[4]],
        WindowCategory.DOCUMENT: [mock_windows[5], mock_windows[6]],
    }

    return categorized_windows


@pytest.fixture
def mock_layout_data(mock_windows):
    """Return mock layout data for testing."""
    return {
        "name": "test_layout",
        "created_at": "2023-06-01T12:00:00",
        "windows": [
            {
                "hwnd": window.hwnd,
                "title": window.title,
                "class_name": window.class_name,
                "rect": window.rect,
                "process_name": window.process_name,
                "process_id": window.process_id,
            }
            for window in mock_windows[:3]  # Include only first 3 windows
        ]
    }


@pytest.fixture
def mock_layout_file(temp_layouts_dir, mock_layout_data):
    """Create a mock layout file for testing."""
    import json

    layout_file = temp_layouts_dir / "test_layout.json"
    with open(layout_file, "w") as f:
        json.dump(mock_layout_data, f)

    return layout_file


@pytest.fixture
def patch_config_dir(temp_dir, monkeypatch):
    """Patch the config directory to use a temporary directory."""
    from src.utils.config import CONFIG_DIR

    # Create temp config directory structure
    temp_config_dir = temp_dir / "config"
    temp_config_dir.mkdir(exist_ok=True)
    temp_layouts_dir = temp_config_dir / "layouts"
    temp_layouts_dir.mkdir(exist_ok=True)

    # Patch CONFIG_DIR
    monkeypatch.setattr("src.utils.config.CONFIG_DIR", str(temp_config_dir))

    return temp_config_dir


@pytest.fixture
def mock_keyboard():
    """Mock the keyboard module used for hotkeys."""
    with mock.patch("keyboard.add_hotkey") as mock_add_hotkey, \
         mock.patch("keyboard.remove_hotkey") as mock_remove_hotkey, \
         mock.patch("keyboard.wait") as mock_wait:

        mock_wait.side_effect = KeyboardInterrupt  # To exit service loop

        yield {
            "add_hotkey": mock_add_hotkey,
            "remove_hotkey": mock_remove_hotkey,
            "wait": mock_wait,
        }