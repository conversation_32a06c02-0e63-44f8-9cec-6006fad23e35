"""
Integration tests for the window detection and categorization components.

These tests verify that the WindowDetector and WindowCategorizer classes work together correctly.
"""

import pytest
from unittest.mock import patch, MagicMock

from src.workflow.a_detection.window_detector import WindowDetector
from src.workflow.b_categorization.window_categorizer import WindowCategorizer, WindowCategory

# Mock window data for tests
def EnumWindows(callback, lParam):
    """Mock EnumWindows function."""
    # Create some test windows
    callback(1001, 0)  # Chrome window
    callback(1002, 0)  # VS Code window
    callback(1003, 0)  # Explorer window
    callback(1004, 0)  # Hidden window
    callback(1005, 0)  # Notepad window
    return True

def GetWindowText(hwnd):
    """Mock GetWindowText function."""
    window_texts = {
        1001: "Google Chrome",
        1002: "window_detector.py - Visual Studio Code",
        1003: "Documents",
        1004: "Hidden Window",
        1005: "Untitled - Notepad"
    }
    return window_texts.get(hwnd, "")

def IsWindowVisible(hwnd):
    """Mock IsWindowVisible function."""
    return hwnd != 1004  # All windows visible except 1004

def GetClassName(hwnd):
    """Mock GetClassName function."""
    window_classes = {
        1001: "Chrome_WidgetWin_1",
        1002: "VSCodeFrameClass",
        1003: "CabinetWClass",
        1004: "HiddenWindowClass",
        1005: "Notepad"
    }
    return window_classes.get(hwnd, "")

def GetWindowRect(hwnd):
    """Mock GetWindowRect function."""
    window_rects = {
        1001: (100, 100, 900, 700),
        1002: (200, 200, 1000, 800),
        1003: (300, 300, 800, 600),
        1004: (0, 0, 0, 0),
        1005: (400, 400, 700, 600)
    }
    return window_rects.get(hwnd, (0, 0, 0, 0))

def GetForegroundWindow():
    """Mock GetForegroundWindow function."""
    return 1001  # Chrome is active

@pytest.mark.skip(reason="Integration tests need to be updated to match the actual implementation")
class TestWindowDetectionAndCategorization:
    """Tests for the integration between window detection and categorization."""

    def setup_method(self):
        """Set up the test environment."""
        self.detector = WindowDetector()
        self.categorizer = WindowCategorizer()

    @patch('src.workflow.a_detection.window_detector.win32gui')
    def test_detect_and_categorize_windows(self, mock_win32gui):
        """Test the integration of window detection and categorization."""
        # Configure detector mock
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect

        # 1. Detect windows
        detected_windows = self.detector.get_all_windows()

        # Verify detection results
        assert len(detected_windows) == 3  # 3 visible windows in our mock
        assert 1001 in detected_windows  # Chrome window
        assert 1002 in detected_windows  # VS Code window
        assert 1003 in detected_windows  # Explorer window
        assert 1004 not in detected_windows  # Hidden window excluded

        # 2. Categorize all windows
        categorized_windows = {}
        for hwnd, window in detected_windows.items():
            categorized_windows[hwnd] = self.categorizer.categorize(window)

        # Verify categorization results
        chrome_window = categorized_windows[1001]
        vscode_window = categorized_windows[1002]
        explorer_window = categorized_windows[1003]

        assert chrome_window.category == WindowCategory.BROWSER
        assert vscode_window.category == WindowCategory.DEVELOPMENT
        assert explorer_window.category == WindowCategory.EXPLORER

        # 3. Get browser windows only
        browser_windows = {hwnd: window for hwnd, window in categorized_windows.items()
                          if window.category == WindowCategory.BROWSER}

        assert len(browser_windows) == 1
        assert 1001 in browser_windows  # Only Chrome is a browser

    @patch('src.workflow.a_detection.window_detector.win32gui')
    def test_find_and_categorize_browser_windows(self, mock_win32gui):
        """Test finding and categorizing browser windows specifically."""
        # Configure detector mock
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect

        # 1. Find windows with "Chrome" in the title
        chrome_windows = self.detector.find_windows_by_title("Chrome")

        # Verify detection results
        assert len(chrome_windows) == 1
        chrome_window = chrome_windows[1001]
        assert chrome_window.title == "Google Chrome"

        # 2. Categorize the Chrome window
        categorized_chrome = self.categorizer.categorize(chrome_window)

        # Verify categorization
        assert categorized_chrome.category == WindowCategory.BROWSER
        assert categorized_chrome.browser_type.name == "CHROME"

    @patch('src.workflow.a_detection.window_detector.win32gui')
    def test_get_and_categorize_active_window(self, mock_win32gui):
        """Test getting and categorizing the active window."""
        # Configure detector mock
        mock_win32gui.GetForegroundWindow.side_effect = GetForegroundWindow
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect

        # 1. Get the active window
        active_window = self.detector.get_active_window()

        # Verify detection results
        assert active_window is not None
        assert active_window.hwnd == 1001  # First window is active in our mock

        # 2. Categorize the active window
        categorized_window = self.categorizer.categorize_window(active_window)

        # Verify categorization
        assert categorized_window.category == WindowCategory.BROWSER
        assert categorized_window.title == "Google Chrome"

    @patch('src.workflow.a_detection.window_detector.win32gui')
    def test_filter_categorized_windows_by_category(self, mock_win32gui):
        """Test filtering categorized windows by category."""
        # Configure detector mock
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect

        # 1. Detect windows
        detected_windows = self.detector.get_all_windows()

        # 2. Categorize all windows
        categorized_windows = self.categorizer.categorize_windows(detected_windows)

        # 3. Get windows by category
        browsers = self.categorizer.get_windows_by_category(categorized_windows, WindowCategory.BROWSER)
        explorers = self.categorizer.get_windows_by_category(categorized_windows, WindowCategory.EXPLORER)
        dev_tools = self.categorizer.get_windows_by_category(categorized_windows, WindowCategory.DEVELOPMENT)

        # Verify filtering
        assert len(browsers) == 1
        assert len(explorers) == 1
        assert len(dev_tools) == 1

        assert list(browsers.values())[0].title == "Google Chrome"
        assert list(explorers.values())[0].title == "Documents"
        assert "Visual Studio Code" in list(dev_tools.values())[0].title