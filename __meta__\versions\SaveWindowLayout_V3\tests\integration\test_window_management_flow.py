"""
Integration tests for the window management flow.

These tests assess the complete flow from window detection to layout management.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.workflow.b_categorization.window_categorizer import WindowCategory, BrowserType
from src.workflow.g_controllers.window_controller import WindowController

# Mock functions for testing
def EnumWindows(callback, lParam):
    """Mock EnumWindows function."""
    # Create some test windows
    callback(1001, 0)  # Chrome window
    callback(1002, 0)  # VS Code window
    callback(1003, 0)  # Explorer window
    callback(1005, 0)  # Notepad window
    return True

def GetWindowText(hwnd):
    """Mock GetWindowText function."""
    window_texts = {
        1001: "Google Chrome",
        1002: "window_detector.py - Visual Studio Code",
        1003: "Documents",
        1005: "Untitled - Notepad"
    }
    return window_texts.get(hwnd, "")

def IsWindowVisible(hwnd):
    """Mock IsWindowVisible function."""
    return True

def IsWindowEnabled(hwnd):
    """Mock IsWindowEnabled function."""
    return True

def GetClassName(hwnd):
    """Mock GetClassName function."""
    window_classes = {
        1001: "Chrome_WidgetWin_1",
        1002: "VSCodeFrameClass",
        1003: "CabinetWClass",
        1005: "Notepad"
    }
    return window_classes.get(hwnd, "")

def GetWindowRect(hwnd):
    """Mock GetWindowRect function."""
    window_rects = {
        1001: (100, 100, 900, 700),
        1002: (200, 200, 1000, 800),
        1003: (300, 300, 800, 600),
        1005: (400, 400, 700, 600)
    }
    return window_rects.get(hwnd, (0, 0, 0, 0))

def GetForegroundWindow():
    """Mock GetForegroundWindow function."""
    return 1001

def SetForegroundWindow(hwnd):
    """Mock SetForegroundWindow function."""
    return True

def MoveWindow(hwnd, x, y, width, height, repaint):
    """Mock MoveWindow function."""
    return True

@pytest.mark.skip(reason="Integration tests need to be updated to match the actual implementation")
class TestWindowManagementFlow:
    """Tests for the complete window management flow."""

    def setup_method(self):
        """Set up the test environment."""
        # Create a temporary directory for layout files
        self.temp_dir = tempfile.TemporaryDirectory()
        self.layouts_dir = Path(self.temp_dir.name) / "layouts"
        self.layouts_dir.mkdir(exist_ok=True)

        # Create the controller with the temp directory
        self.controller = WindowController(layouts_dir=str(self.layouts_dir))

    def teardown_method(self):
        """Clean up after the tests."""
        self.temp_dir.cleanup()

    @patch('src.workflow.a_detection.window_detector.win32gui')
    @patch('src.workflow.d_automation.window_actions')
    def test_save_and_apply_layout(self, mock_actions, mock_win32gui):
        """Test saving and applying a window layout."""
        # Configure detector mock
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect
        mock_win32gui.IsWindowEnabled.side_effect = IsWindowEnabled

        # Step 1: Detect and categorize windows
        self.controller.refresh_windows()
        assert len(self.controller.windows) == 4

        # Step 2: Save the layout
        layout_file = self.controller.save_layout("test_layout")
        assert layout_file
        assert Path(layout_file).exists()

        # Step 3: Apply the layout
        restored_count = self.controller.apply_layout("test_layout")
        assert restored_count == 4  # All windows restored

    @patch('src.workflow.a_detection.window_detector.win32gui')
    def test_find_windows_and_categories(self, mock_win32gui):
        """Test finding windows by properties and filtering by category."""
        # Configure mocks
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect

        # 1. Find windows by title
        chrome_windows = self.controller.find_windows(title="Chrome")

        # Verify the results
        assert len(chrome_windows) == 1
        chrome_window = list(chrome_windows.values())[0]
        assert chrome_window.title == "Google Chrome"
        assert chrome_window.category == WindowCategory.BROWSER

        # 2. Find windows by class
        explorer_windows = self.controller.find_windows(class_name="CabinetWClass")

        # Verify the results
        assert len(explorer_windows) == 1
        explorer_window = list(explorer_windows.values())[0]
        assert "Explorer" in explorer_window.title
        assert explorer_window.category == WindowCategory.EXPLORER

        # 3. Get windows by category
        browser_windows = self.controller.get_windows_by_category(WindowCategory.BROWSER)
        explorer_windows = self.controller.get_windows_by_category(WindowCategory.EXPLORER)

        # Verify results
        assert len(browser_windows) == 1
        assert len(explorer_windows) == 1

    @patch('src.workflow.a_detection.window_detector.win32gui')
    @patch('src.workflow.d_automation.window_actions')
    def test_window_operations(self, mock_actions, mock_win32gui):
        """Test basic window operations through the controller."""
        # Configure mocks
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect
        mock_win32gui.GetForegroundWindow.side_effect = GetForegroundWindow
        mock_win32gui.SetForegroundWindow.side_effect = SetForegroundWindow
        mock_win32gui.MoveWindow.side_effect = MoveWindow

        # Refresh windows
        self.controller.refresh_windows()

        # 1. Get active window
        active_window = self.controller.get_active_window()
        assert active_window is not None
        assert active_window.hwnd == 1001

        # 2. Focus on a specific window
        success = self.controller.focus_window(1003)  # Focus on Explorer
        assert success is True

        # 3. Move a window
        success = self.controller.move_window(1002, 500, 500, 800, 600)
        assert success is True

    @patch('src.workflow.a_detection.window_detector.win32gui')
    @patch('src.workflow.d_automation.batch_operations')
    def test_batch_window_operations(self, mock_batch, mock_win32gui):
        """Test batch operations like tiling and cascading windows."""
        # Configure mocks
        mock_win32gui.EnumWindows.side_effect = EnumWindows
        mock_win32gui.GetWindowText.side_effect = GetWindowText
        mock_win32gui.IsWindowVisible.side_effect = IsWindowVisible
        mock_win32gui.GetClassName.side_effect = GetClassName
        mock_win32gui.GetWindowRect.side_effect = GetWindowRect

        # Mock batch operations to return success
        mock_batch.tile_windows.return_value = 4
        mock_batch.cascade_windows.return_value = 4

        # Refresh windows
        self.controller.refresh_windows()

        # 1. Tile windows
        tiles = self.controller.tile_windows([1001, 1002, 1003, 1005])
        assert tiles == 4

        # 2. Cascade windows
        cascade = self.controller.cascade_windows([1001, 1002, 1003, 1005])
        assert cascade == 4

        # 3. Tile by category
        browser_tiles = self.controller.tile_windows_by_category(WindowCategory.BROWSER)
        assert browser_tiles == 1  # Only Chrome