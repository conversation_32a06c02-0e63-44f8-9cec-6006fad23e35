"""
Windows API Mock Implementation

This module provides mock implementations of the Windows API functions used by the
Window Manager application. These mocks allow testing without interacting with the
actual Windows environment.
"""

from unittest.mock import MagicMock
import random
from typing import Dict, List, Tuple, Callable, Any, Optional

# Mock window database
# This will store our simulated windows
mock_windows: Dict[int, Dict[str, Any]] = {}

# Constants that simulate win32con values
SW_HIDE = 0
SW_NORMAL = 1
SW_MAXIMIZE = 3
SW_MINIMIZE = 6
SW_RESTORE = 9

# Tracking for mocked state
active_window = 0
screen_size = (1920, 1080)


def setup_mock_windows(windows: Optional[Dict[int, Dict[str, Any]]] = None) -> None:
    """
    Set up mock windows for testing.

    Args:
        windows: Optional dictionary of window handles to window information.
                If not provided, a default set of mock windows will be created.
    """
    global mock_windows, active_window

    if windows is None:
        # Create a default set of mock windows
        mock_windows = {
            1001: {
                "title": "Google Chrome",
                "class_name": "Chrome_WidgetWin_1",
                "visible": True,
                "enabled": True,
                "rect": (100, 100, 900, 700),  # left, top, right, bottom
                "process_id": 1234,
                "process_name": "chrome.exe",
                "state": SW_NORMAL
            },
            1002: {
                "title": "Documents - File Explorer",
                "class_name": "CabinetWClass",
                "visible": True,
                "enabled": True,
                "rect": (950, 100, 1750, 700),
                "process_id": 5678,
                "process_name": "explorer.exe",
                "state": SW_NORMAL
            },
            1003: {
                "title": "Notepad",
                "class_name": "Notepad",
                "visible": True,
                "enabled": True,
                "rect": (200, 200, 800, 600),
                "process_id": 9012,
                "process_name": "notepad.exe",
                "state": SW_NORMAL
            },
            1004: {
                "title": "Hidden Window",
                "class_name": "HiddenClass",
                "visible": False,
                "enabled": True,
                "rect": (0, 0, 0, 0),
                "process_id": 3456,
                "process_name": "hidden.exe",
                "state": SW_HIDE
            }
        }
    else:
        mock_windows = windows

    # Set the first window as active
    active_window = list(mock_windows.keys())[0] if mock_windows else 0


# Mock implementations of win32gui functions
def EnumWindows(callback: Callable[[int, int], bool], lparam: int) -> bool:
    """
    Mock implementation of win32gui.EnumWindows.

    Args:
        callback: Function to call for each window
        lparam: Application-defined value to pass to callback

    Returns:
        True on success
    """
    for hwnd in mock_windows.keys():
        if not callback(hwnd, lparam):
            break
    return True


def GetWindowText(hwnd: int) -> str:
    """
    Mock implementation of win32gui.GetWindowText.

    Args:
        hwnd: Window handle

    Returns:
        Window title
    """
    if hwnd in mock_windows:
        return mock_windows[hwnd]["title"]
    return ""


def GetClassName(hwnd: int) -> str:
    """
    Mock implementation of win32gui.GetClassName.

    Args:
        hwnd: Window handle

    Returns:
        Window class name
    """
    if hwnd in mock_windows:
        return mock_windows[hwnd]["class_name"]
    return ""


def IsWindowVisible(hwnd: int) -> bool:
    """
    Mock implementation of win32gui.IsWindowVisible.

    Args:
        hwnd: Window handle

    Returns:
        True if window is visible
    """
    if hwnd in mock_windows:
        return mock_windows[hwnd]["visible"]
    return False


def IsWindowEnabled(hwnd: int) -> bool:
    """
    Mock implementation of win32gui.IsWindowEnabled.

    Args:
        hwnd: Window handle

    Returns:
        True if window is enabled
    """
    if hwnd in mock_windows:
        return mock_windows[hwnd]["enabled"]
    return False


def GetWindowRect(hwnd: int) -> Tuple[int, int, int, int]:
    """
    Mock implementation of win32gui.GetWindowRect.

    Args:
        hwnd: Window handle

    Returns:
        Tuple of (left, top, right, bottom)
    """
    if hwnd in mock_windows:
        return mock_windows[hwnd]["rect"]
    return (0, 0, 0, 0)


def GetForegroundWindow() -> int:
    """
    Mock implementation of win32gui.GetForegroundWindow.

    Returns:
        Active window handle
    """
    return active_window


def SetForegroundWindow(hwnd: int) -> bool:
    """
    Mock implementation of win32gui.SetForegroundWindow.

    Args:
        hwnd: Window handle

    Returns:
        True on success
    """
    global active_window
    if hwnd in mock_windows and mock_windows[hwnd]["visible"]:
        active_window = hwnd
        return True
    return False


def MoveWindow(hwnd: int, x: int, y: int, width: int, height: int, repaint: bool) -> bool:
    """
    Mock implementation of win32gui.MoveWindow.

    Args:
        hwnd: Window handle
        x: New x position
        y: New y position
        width: New width
        height: New height
        repaint: Whether to repaint

    Returns:
        True on success
    """
    if hwnd in mock_windows:
        mock_windows[hwnd]["rect"] = (x, y, x + width, y + height)
        return True
    return False


def ShowWindow(hwnd: int, cmd: int) -> bool:
    """
    Mock implementation of win32gui.ShowWindow.

    Args:
        hwnd: Window handle
        cmd: Show command (SW_*)

    Returns:
        True on success
    """
    if hwnd in mock_windows:
        if cmd == SW_HIDE:
            mock_windows[hwnd]["visible"] = False
        elif cmd in (SW_NORMAL, SW_RESTORE, SW_MAXIMIZE, SW_MINIMIZE):
            mock_windows[hwnd]["visible"] = True
            mock_windows[hwnd]["state"] = cmd
        return True
    return False


def GetWindowThreadProcessId(hwnd: int) -> Tuple[int, int]:
    """
    Mock implementation of win32process.GetWindowThreadProcessId.

    Args:
        hwnd: Window handle

    Returns:
        Tuple of (thread_id, process_id)
    """
    if hwnd in mock_windows:
        thread_id = random.randint(1000, 9999)
        process_id = mock_windows[hwnd]["process_id"]
        return (thread_id, process_id)
    return (0, 0)


# Additional mock functions as needed
def create_mock_window(hwnd: int = None,
                       title: str = "Mock Window",
                       class_name: str = "MockClass",
                       visible: bool = True,
                       enabled: bool = True,
                       position: Tuple[int, int] = (0, 0),
                       size: Tuple[int, int] = (800, 600),
                       process_id: int = None,
                       process_name: str = "mock.exe") -> int:
    """
    Create a new mock window for testing.

    Args:
        hwnd: Window handle (generated if not provided)
        title: Window title
        class_name: Window class name
        visible: Whether window is visible
        enabled: Whether window is enabled
        position: (x, y) position
        size: (width, height) size
        process_id: Process ID (generated if not provided)
        process_name: Process executable name

    Returns:
        The window handle
    """
    global mock_windows

    if hwnd is None:
        # Generate a unique handle
        hwnd = max(mock_windows.keys()) + 1 if mock_windows else 1000

    if process_id is None:
        process_id = random.randint(1000, 9999)

    x, y = position
    width, height = size

    mock_windows[hwnd] = {
        "title": title,
        "class_name": class_name,
        "visible": visible,
        "enabled": enabled,
        "rect": (x, y, x + width, y + height),
        "process_id": process_id,
        "process_name": process_name,
        "state": SW_NORMAL if visible else SW_HIDE
    }

    return hwnd


def remove_mock_window(hwnd: int) -> bool:
    """
    Remove a mock window.

    Args:
        hwnd: Window handle

    Returns:
        True if window was removed, False if not found
    """
    global mock_windows, active_window

    if hwnd in mock_windows:
        if active_window == hwnd:
            # Update active window if we're removing the active one
            remaining = [h for h in mock_windows if h != hwnd]
            active_window = remaining[0] if remaining else 0

        del mock_windows[hwnd]
        return True

    return False


def reset_mock_windows() -> None:
    """Reset all mock windows to default state."""
    global mock_windows, active_window
    mock_windows = {}
    active_window = 0
    setup_mock_windows()