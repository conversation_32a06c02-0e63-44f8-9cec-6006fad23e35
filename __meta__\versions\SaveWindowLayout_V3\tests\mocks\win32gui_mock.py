"""
Mock implementation of win32gui for testing purposes.

This module provides mock implementations of the win32gui functions used by the window manager.
It allows tests to run without actually interacting with the Windows API.
"""

from typing import Dict, List, Tuple, Any, Callable
import random


class Win32GuiMock:
    """Mock implementation of win32gui functions."""

    def __init__(self):
        """Initialize the mock with a set of fake windows."""
        self.windows = {}
        self.z_order = []
        self.foreground_window = None
        self._create_test_windows()

    def _create_test_windows(self):
        """Create a set of test windows with realistic properties."""
        # Browser windows
        self._add_window(
            hwnd=1001,
            title="Google Chrome",
            class_name="Chrome_WidgetWin_1",
            rect=(100, 100, 900, 700),
            visible=True,
            enabled=True,
            process_id=5001,
        )
        self._add_window(
            hwnd=1002,
            title="Mozilla Firefox - Window Manager Documentation",
            class_name="MozillaWindowClass",
            rect=(200, 200, 1000, 800),
            visible=True,
            enabled=True,
            process_id=5002,
        )

        # Explorer windows
        self._add_window(
            hwnd=1003,
            title="Documents",
            class_name="CabinetWClass",
            rect=(300, 300, 800, 600),
            visible=True,
            enabled=True,
            process_id=5003,
        )
        self._add_window(
            hwnd=1004,
            title="Downloads",
            class_name="CabinetWClass",
            rect=(400, 400, 900, 700),
            visible=False,  # This one is not visible
            enabled=True,
            process_id=5003,  # Same process as Documents
        )

        # Development windows
        self._add_window(
            hwnd=1005,
            title="window_manager.py - Visual Studio Code",
            class_name="Chrome_WidgetWin_0",
            rect=(500, 100, 1300, 900),
            visible=True,
            enabled=True,
            process_id=5004,
        )
        self._add_window(
            hwnd=1006,
            title="Python IDLE",
            class_name="TkTopLevel",
            rect=(600, 200, 1100, 700),
            visible=True,
            enabled=True,
            process_id=5005,
        )

        # Document windows
        self._add_window(
            hwnd=1007,
            title="Project Plan.docx - Word",
            class_name="OpusApp",
            rect=(700, 300, 1400, 900),
            visible=True,
            enabled=True,
            process_id=5006,
        )
        self._add_window(
            hwnd=1008,
            title="Budget.xlsx - Excel",
            class_name="XLMAIN",
            rect=(800, 400, 1500, 1000),
            visible=True,
            enabled=True,
            process_id=5007,
        )

        # Set the foreground window
        self.foreground_window = 1001  # Chrome is active

    def _add_window(self, hwnd, title, class_name, rect, visible, enabled, process_id):
        """Add a window to the mock system."""
        self.windows[hwnd] = {
            "hwnd": hwnd,
            "title": title,
            "class_name": class_name,
            "rect": rect,
            "visible": visible,
            "enabled": enabled,
            "process_id": process_id,
        }
        # Only add visible windows to z-order
        if visible:
            self.z_order.append(hwnd)

    # Mock win32gui functions

    def EnumWindows(self, callback, extra):
        """Mock for EnumWindows function."""
        for hwnd in self.windows.keys():
            callback(hwnd, extra)
        return True

    def GetWindowText(self, hwnd):
        """Mock for GetWindowText function."""
        if hwnd not in self.windows:
            return ""
        return self.windows[hwnd]["title"]

    def IsWindowVisible(self, hwnd):
        """Mock for IsWindowVisible function."""
        if hwnd not in self.windows:
            return False
        return self.windows[hwnd]["visible"]

    def IsWindowEnabled(self, hwnd):
        """Mock for IsWindowEnabled function."""
        if hwnd not in self.windows:
            return False
        return self.windows[hwnd]["enabled"]

    def IsWindow(self, hwnd):
        """Mock for IsWindow function."""
        return hwnd in self.windows

    def GetClassName(self, hwnd):
        """Mock for GetClassName function."""
        if hwnd not in self.windows:
            return ""
        return self.windows[hwnd]["class_name"]

    def GetWindowRect(self, hwnd):
        """Mock for GetWindowRect function."""
        if hwnd not in self.windows:
            return (0, 0, 0, 0)
        return self.windows[hwnd]["rect"]

    def SetForegroundWindow(self, hwnd):
        """Mock for SetForegroundWindow function."""
        if hwnd in self.windows:
            self.foreground_window = hwnd
            return True
        return False

    def GetForegroundWindow(self):
        """Mock for GetForegroundWindow function."""
        return self.foreground_window

    def MoveWindow(self, hwnd, x, y, width, height, repaint):
        """Mock for MoveWindow function."""
        if hwnd in self.windows:
            self.windows[hwnd]["rect"] = (x, y, x + width, y + height)
            return True
        return False

    def SetWindowPos(self, hwnd, hwnd_insert_after, x, y, cx, cy, flags):
        """Mock for SetWindowPos function."""
        if hwnd in self.windows:
            if flags & 1 == 0:  # SWP_NOMOVE not set
                self.windows[hwnd]["rect"] = (
                    x,
                    y,
                    x + (self.windows[hwnd]["rect"][2] - self.windows[hwnd]["rect"][0]),
                    y + (self.windows[hwnd]["rect"][3] - self.windows[hwnd]["rect"][1]),
                )
            if flags & 2 == 0:  # SWP_NOSIZE not set
                self.windows[hwnd]["rect"] = (
                    self.windows[hwnd]["rect"][0],
                    self.windows[hwnd]["rect"][1],
                    self.windows[hwnd]["rect"][0] + cx,
                    self.windows[hwnd]["rect"][1] + cy,
                )
            return True
        return False

    def GetClientRect(self, hwnd):
        """Mock for GetClientRect function."""
        if hwnd in self.windows:
            rect = self.windows[hwnd]["rect"]
            # Return client rect (0, 0, width, height)
            return (0, 0, rect[2] - rect[0], rect[3] - rect[1])
        return (0, 0, 0, 0)

    def GetDesktopWindow(self):
        """Mock for GetDesktopWindow function."""
        return 0  # Special HWND for desktop

    def GetWindow(self, hwnd, relation):
        """Mock for GetWindow function."""
        # Define constants
        GW_HWNDNEXT = 2
        GW_HWNDPREV = 3
        GW_OWNER = 4
        GW_CHILD = 5
        GW_ENABLEDPOPUP = 6

        # Handle z-order navigation
        if relation == GW_HWNDNEXT and hwnd in self.z_order:
            idx = self.z_order.index(hwnd)
            if idx < len(self.z_order) - 1:
                return self.z_order[idx + 1]
        elif relation == GW_HWNDPREV and hwnd in self.z_order:
            idx = self.z_order.index(hwnd)
            if idx > 0:
                return self.z_order[idx - 1]

        # For other relations, just return the next window in our list
        # This is a simplification but works for most test cases
        if hwnd in self.windows:
            hwnd_list = list(self.windows.keys())
            idx = hwnd_list.index(hwnd)
            if idx < len(hwnd_list) - 1:
                return hwnd_list[idx + 1]

        return 0

    def GetWindowThreadProcessId(self, hwnd):
        """Mock for GetWindowThreadProcessId function."""
        if hwnd not in self.windows:
            return (0, 0)
        # Return (thread_id, process_id)
        return (random.randint(1000, 9999), self.windows[hwnd]["process_id"])

    def GetWindowPlacement(self, hwnd):
        """Mock for GetWindowPlacement function."""
        if hwnd not in self.windows:
            raise Exception(f"Invalid window handle: {hwnd}")

        # Default values for a normal window
        flags = 0
        show_cmd = 1  # SW_SHOWNORMAL
        min_position = (0, 0)
        max_position = (-1, -1)
        normal_position = self.windows[hwnd]["rect"]

        return (flags, show_cmd, min_position, max_position, normal_position)

    def ShowWindow(self, hwnd, cmd):
        """Mock for ShowWindow function."""
        if hwnd not in self.windows:
            return False

        # Handle show commands
        SW_HIDE = 0
        SW_SHOW = 5

        if cmd == SW_HIDE:
            self.windows[hwnd]["visible"] = False
            if hwnd in self.z_order:
                self.z_order.remove(hwnd)
        elif cmd == SW_SHOW:
            self.windows[hwnd]["visible"] = True
            if hwnd not in self.z_order:
                self.z_order.append(hwnd)

        return True

    def GetParent(self, hwnd):
        """Mock for GetParent function."""
        # For simplicity, all windows have no parent in our mock
        return 0

# Create a singleton instance
win32gui_mock = Win32GuiMock()

# Export the mock functions to match win32gui's interface
EnumWindows = win32gui_mock.EnumWindows
GetWindowText = win32gui_mock.GetWindowText
IsWindowVisible = win32gui_mock.IsWindowVisible
IsWindowEnabled = win32gui_mock.IsWindowEnabled
IsWindow = win32gui_mock.IsWindow
GetClassName = win32gui_mock.GetClassName
GetWindowRect = win32gui_mock.GetWindowRect
SetForegroundWindow = win32gui_mock.SetForegroundWindow
GetForegroundWindow = win32gui_mock.GetForegroundWindow
MoveWindow = win32gui_mock.MoveWindow
SetWindowPos = win32gui_mock.SetWindowPos
GetClientRect = win32gui_mock.GetClientRect
GetDesktopWindow = win32gui_mock.GetDesktopWindow
GetWindow = win32gui_mock.GetWindow
GetWindowThreadProcessId = win32gui_mock.GetWindowThreadProcessId
GetWindowPlacement = win32gui_mock.GetWindowPlacement
ShowWindow = win32gui_mock.ShowWindow
GetParent = win32gui_mock.GetParent