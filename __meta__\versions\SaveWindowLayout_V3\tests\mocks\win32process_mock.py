"""
Mock implementation of win32process for testing purposes.

This module provides mock implementations of the win32process functions used by the window manager.
It allows tests to run without actually interacting with the Windows API.
"""

from typing import Dict, Optional
import os
from tests.mocks.win32gui_mock import win32gui_mock


class Win32ProcessMock:
    """Mock implementation of win32process functions."""

    def __init__(self):
        """Initialize the mock process data."""
        self.processes = {}
        self._create_test_processes()

    def _create_test_processes(self):
        """Create test process data that corresponds to the mock windows."""
        # Map of process IDs to executable names
        self.processes = {
            5001: {"name": "chrome.exe", "path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"},
            5002: {"name": "firefox.exe", "path": "C:\\Program Files\\Mozilla Firefox\\firefox.exe"},
            5003: {"name": "explorer.exe", "path": "C:\\Windows\\explorer.exe"},
            5004: {"name": "Code.exe", "path": "C:\\Program Files\\Microsoft VS Code\\Code.exe"},
            5005: {"name": "pythonw.exe", "path": "C:\\Program Files\\Python\\pythonw.exe"},
            5006: {"name": "WINWORD.EXE", "path": "C:\\Program Files\\Microsoft Office\\root\\Office16\\WINWORD.EXE"},
            5007: {"name": "EXCEL.EXE", "path": "C:\\Program Files\\Microsoft Office\\root\\Office16\\EXCEL.EXE"},
        }

    def GetProcessImageFileName(self, process_handle):
        """Mock for GetProcessImageFileName function."""
        process_id = self._handle_to_pid(process_handle)
        if process_id in self.processes:
            return self.processes[process_id]["path"]
        return ""

    def GetModuleFileNameEx(self, process_handle, module_handle=0):
        """Mock for GetModuleFileNameEx function."""
        process_id = self._handle_to_pid(process_handle)
        if process_id in self.processes:
            return self.processes[process_id]["path"]
        return ""

    def EnumProcesses(self):
        """Mock for EnumProcesses function."""
        return list(self.processes.keys())

    def _handle_to_pid(self, handle):
        """Convert a process handle to a process ID for testing purposes."""
        # For simplicity, we'll treat the handle as the process ID in tests
        return handle


# Create a singleton instance
win32process_mock = Win32ProcessMock()

# Export the mock functions to match win32process's interface
GetProcessImageFileName = win32process_mock.GetProcessImageFileName
GetModuleFileNameEx = win32process_mock.GetModuleFileNameEx
EnumProcesses = win32process_mock.EnumProcesses


# Additional functions often used with win32process

def OpenProcess(desiredAccess, inheritHandle, processId):
    """Mock for win32process.OpenProcess function."""
    # For testing simplicity, just return the process ID as the handle
    if processId in win32process_mock.processes:
        return processId
    return None

def CloseHandle(handle):
    """Mock for win32api.CloseHandle function."""
    # Nothing to do in the mock
    return True

def GetProcessId(handle):
    """Mock for win32process.GetProcessId function."""
    # In our mock, handles are the same as process IDs for simplicity
    return handle

def GetWindowThreadProcessId(hwnd):
    """Mock for win32process.GetWindowThreadProcessId function."""
    # Delegate to win32gui mock
    return win32gui_mock.GetWindowThreadProcessId(hwnd)

def GetExitCodeProcess(handle):
    """Mock for win32process.GetExitCodeProcess function."""
    # All processes are running in our mock
    STILL_ACTIVE = 259
    return STILL_ACTIVE