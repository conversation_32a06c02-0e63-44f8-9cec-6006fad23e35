"""
Layout Manager Unit Tests

This module contains unit tests for the LayoutManager class and related functionality
in the c_layout layer.
"""

import os
import json
import pytest
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path

from src.workflow.c_layout.layout_manager import LayoutManager
from src.workflow.a_detection.window_detector import WindowInfo, WindowPosition, WindowSize
from src.workflow.b_categorization.window_categorizer import WindowCategory


class TestLayoutManager:
    """Tests for the LayoutManager class."""

    @pytest.fixture
    def layout_manager(self, tmp_path):
        """Create a layout manager with a temporary layouts directory."""
        return LayoutManager(layouts_dir=str(tmp_path / "layouts"))

    @pytest.fixture
    def mock_categorized_windows(self):
        """Create mock window objects for testing."""
        # Browser window
        browser_window = MagicMock()
        browser_window.title = "Test Browser"
        browser_window.category.name = "BROWSER"
        browser_window.base_info.position.x = 100
        browser_window.base_info.position.y = 200
        browser_window.base_info.size.width = 800
        browser_window.base_info.size.height = 600
        browser_window.base_info.process.exe_name = "chrome.exe"
        browser_window.base_info.class_name = "ChromeWidgetWin1"

        # Explorer window
        explorer_window = MagicMock()
        explorer_window.title = "Documents - Explorer"
        explorer_window.category.name = "EXPLORER"
        explorer_window.base_info.position.x = 300
        explorer_window.base_info.position.y = 400
        explorer_window.base_info.size.width = 700
        explorer_window.base_info.size.height = 500
        explorer_window.base_info.process.exe_name = "explorer.exe"
        explorer_window.base_info.class_name = "CabinetWClass"

        return {1001: browser_window, 1002: explorer_window}

    @pytest.fixture
    def sample_layout_json(self):
        """Create a sample layout JSON string for testing."""
        return '''
{
  "name": "test_layout",
  "timestamp": "2023-06-01T12:00:00",
  "windows": [
    {
      "hwnd": 1001,
      "title": "Test Browser",
      "category": "BROWSER",
      "position": [
        100,
        200
      ],
      "size": [
        800,
        600
      ],
      "process_name": "chrome.exe",
      "class_name": "ChromeWidgetWin1"
    },
    {
      "hwnd": 1002,
      "title": "Documents - Explorer",
      "category": "EXPLORER",
      "position": [
        300,
        400
      ],
      "size": [
        700,
        500
      ],
      "process_name": "explorer.exe",
      "class_name": "CabinetWClass"
    }
  ]
}
'''

    def test_save_layout(self, layout_manager, mock_categorized_windows):
        """Test saving a window layout."""
        # Call the save_layout method
        result = layout_manager.save_layout(mock_categorized_windows, "test_layout")

        # Check if the file was created
        assert result
        assert Path(result).is_file()

        # Verify the file contents
        with open(result, 'r') as f:
            data = json.load(f)
            assert data["name"] == "test_layout.json"
            assert "timestamp" in data
            assert len(data["windows"]) == 2
            assert data["windows"][0]["title"] == "Test Browser"
            assert data["windows"][1]["title"] == "Documents - Explorer"

    @pytest.mark.skip(reason="Difficult to mock external win32 dependencies in tests")
    def test_apply_layout(self, layout_manager, sample_layout_json):
        """Test applying a window layout."""
        # Since this method depends heavily on win32gui, it's better to test
        # it with integration tests or a simpler approach that avoids mocking
        # complex external dependencies
        pass

    def test_list_layouts(self, layout_manager):
        """Test listing available layouts."""
        # Create some test layout files
        layout_dir = layout_manager.layouts_dir

        # Create test layout files
        open(os.path.join(layout_dir, "layout1.json"), 'w').close()
        open(os.path.join(layout_dir, "layout2.json"), 'w').close()
        open(os.path.join(layout_dir, "not_a_layout.txt"), 'w').close()

        # Call the method
        layouts = layout_manager.list_layouts()

        # Verify only JSON files are included
        assert len(layouts) >= 2
        assert "layout1.json" in layouts
        assert "layout2.json" in layouts
        assert "not_a_layout.txt" not in layouts

    # Skip this test if psutil is not installed
    @pytest.mark.skip(reason="psutil dependency not installed")
    def test_get_process_name(self, layout_manager):
        """Test getting a process name from PID."""
        # This method depends on psutil which may not be installed
        pass

    def test_find_matching_window(self, layout_manager):
        """Test finding a matching window."""
        saved_window = {
            "title": "Test Browser",
            "category": "BROWSER",
            "process_name": "chrome.exe",
            "class_name": "ChromeWidgetWin1"
        }

        current_windows = {
            1001: {
                "title": "Test Browser",
                "process_name": "chrome.exe",
                "class_name": "ChromeWidgetWin1"
            },
            1002: {
                "title": "Another Window",
                "process_name": "notepad.exe",
                "class_name": "Notepad"
            }
        }

        # Mock the specific matcher methods
        with patch.object(layout_manager, "_find_matching_browser", return_value=1001) as mock_browser_matcher:
            # Call the method
            result = layout_manager._find_matching_window(saved_window, current_windows)

            # Verify result
            assert result == 1001
            mock_browser_matcher.assert_called_once_with(saved_window, current_windows)

    @pytest.mark.skip(reason="Difficult to mock external win32 dependencies in tests")
    def test_get_current_windows(self, layout_manager):
        """Test getting current windows."""
        # This method depends heavily on win32gui and win32process, which
        # are difficult to mock properly in tests without complicated setups
        pass