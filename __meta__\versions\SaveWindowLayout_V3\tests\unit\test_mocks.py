"""
Test the mocking system for Windows API functions.

These tests verify that our mock implementations of Windows API functions
work correctly and can be used in the test suite.
"""

import pytest
import win32gui
import win32process
import win32api


def test_win32gui_mock_enumwindows():
    """Test that EnumWindows calls the callback for each window."""
    windows = []

    def callback(hwnd, lparam):
        windows.append(hwnd)
        return True

    win32gui.EnumWindows(callback, 0)

    # Should have found all the mock windows
    assert len(windows) > 0
    assert 1001 in windows  # Chrome window
    assert 1002 in windows  # Firefox window


def test_win32gui_mock_window_info():
    """Test retrieving window information from the mock."""
    hwnd = 1001  # Chrome window

    title = win32gui.GetWindowText(hwnd)
    class_name = win32gui.GetClassName(hwnd)
    rect = win32gui.GetWindowRect(hwnd)
    visible = win32gui.IsWindowVisible(hwnd)

    assert title == "Google Chrome"
    assert class_name == "Chrome_WidgetWin_1"
    assert rect == (100, 100, 900, 700)
    assert visible is True


def test_win32gui_mock_window_manipulation():
    """Test window manipulation functions in the mock."""
    hwnd = 1001  # Chrome window

    # Set as foreground window
    result = win32gui.SetForegroundWindow(hwnd)
    assert result is True
    assert win32gui.GetForegroundWindow() == hwnd

    # Move window
    win32gui.MoveWindow(hwnd, 200, 200, 1000, 800, True)
    new_rect = win32gui.GetWindowRect(hwnd)
    assert new_rect == (200, 200, 1200, 1000)


def test_win32process_mock():
    """Test the win32process mock functions."""
    hwnd = 1001  # Chrome window

    # Get process ID
    thread_id, process_id = win32gui.GetWindowThreadProcessId(hwnd)
    assert process_id == 5001

    # Open process
    process_handle = win32process.OpenProcess(0, False, process_id)
    assert process_handle is not None

    # Get process image name
    image_path = win32process.GetProcessImageFileName(process_handle)
    assert "chrome.exe" in image_path

    # Close handle
    result = win32api.CloseHandle(process_handle)
    assert result is True


def test_win32gui_mock_show_window():
    """Test the ShowWindow function in the mock."""
    hwnd = 1001  # Chrome window

    # Initially visible
    assert win32gui.IsWindowVisible(hwnd) is True

    # Hide window
    win32gui.ShowWindow(hwnd, 0)  # SW_HIDE
    assert win32gui.IsWindowVisible(hwnd) is False

    # Show window
    win32gui.ShowWindow(hwnd, 5)  # SW_SHOW
    assert win32gui.IsWindowVisible(hwnd) is True


def test_win32gui_mock_z_order():
    """Test the z-order handling in the mock."""
    hwnd1 = 1001  # Chrome window
    hwnd2 = 1002  # Firefox window

    # Get next window in z-order
    GW_HWNDNEXT = 2
    next_hwnd = win32gui.GetWindow(hwnd1, GW_HWNDNEXT)

    # The next window should be valid
    assert next_hwnd != 0
    assert next_hwnd in [hwnd for hwnd in win32gui.win32gui_mock.windows.keys()
                         if win32gui.IsWindowVisible(hwnd)]