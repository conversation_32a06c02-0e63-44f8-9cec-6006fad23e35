"""
Window Detector Unit Tests

This module contains unit tests for the WindowDetector class and related functionality
in the a_detection layer.
"""

import os
import sys
import pytest
from unittest.mock import patch, MagicMock, call

# Import the WindowDetector class and related types
from src.workflow.a_detection.window_detector import WindowDetector, WindowInfo

# The tests are using our mock system for win32gui defined in conftest.py
# No need to import explicit mocks anymore


class TestWindowDetector:
    """Tests for the WindowDetector class."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test environment before each test."""
        # Create the detector
        self.detector = WindowDetector()
        yield
        # Clean up after test
        self.detector = None

    def test_detect_windows(self):
        """Test that WindowDetector.detect_windows returns all visible windows."""
        # Call the method
        windows = self.detector.detect_windows(visible_only=True)

        # Verify results
        # The mock system has 7 visible windows and 1 invisible window
        assert len(windows) >= 7

        # Verify window properties for one of the windows
        chrome_window = next((w for w in windows.values() if "Chrome" in w.title), None)
        assert chrome_window is not None
        assert "Chrome" in chrome_window.title
        assert "Chrome_WidgetWin_1" in chrome_window.class_name
        assert chrome_window.is_visible is True

    def test_get_active_window(self):
        """Test that WindowDetector.get_active_window returns the foreground window."""
        # First detect windows
        self.detector.detect_windows()

        # Call the method
        active_window = self.detector.get_active_window()

        # Verify results
        assert active_window is not None
        assert active_window.hwnd == 1001  # Set as foreground in win32gui_mock
        assert "Google Chrome" in active_window.title

    def test_find_windows_by_title(self):
        """Test that WindowDetector finds windows matching a title."""
        # First detect windows
        self.detector.detect_windows()

        # Call the method with different search terms
        chrome_windows = self.detector.find_windows_by_title("Chrome")
        word_windows = self.detector.find_windows_by_title("Word")
        none_windows = self.detector.find_windows_by_title("Nonexistent")

        # Verify results
        assert len(chrome_windows) >= 1
        assert any("Chrome" in w.title for w in chrome_windows)

        assert len(word_windows) >= 1
        assert any("Word" in w.title for w in word_windows)

        assert len(none_windows) == 0

    def test_find_windows_by_class(self):
        """Test that WindowDetector finds windows by class name."""
        # First detect windows
        self.detector.detect_windows()

        # Call the method
        chrome_windows = self.detector.find_windows_by_class("Chrome_WidgetWin_1")
        explorer_windows = self.detector.find_windows_by_class("CabinetWClass")

        # Verify results
        assert len(chrome_windows) >= 1
        assert all(w.class_name == "Chrome_WidgetWin_1" for w in chrome_windows)

        assert len(explorer_windows) >= 1
        assert all(w.class_name == "CabinetWClass" for w in explorer_windows)

    def test_visible_only_filter(self):
        """Test that visible_only parameter works correctly."""
        # Get visible windows only
        visible_windows = self.detector.detect_windows(visible_only=True)

        # Get all windows including invisible ones
        all_windows = self.detector.detect_windows(visible_only=False)

        # Verify there are more windows when including invisible ones
        assert len(all_windows) >= len(visible_windows)

        # Verify that invisible windows are found when requested
        invisible_windows = [w for w in all_windows.values() if not w.is_visible]
        assert len(invisible_windows) >= 1