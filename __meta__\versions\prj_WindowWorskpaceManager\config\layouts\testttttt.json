{"name": "testttttt.json", "timestamp": "2025-03-27 15:31:54.465943", "windows": [{"hwnd": 4459668, "title": "C:\\Windows\\system32\\cmd.exe", "category": "UNKNOWN", "position": [434, 442], "size": [1129, 635], "process_name": "WindowsTerminal.exe", "class_name": "CASCADIA_HOSTING_WINDOW_CLASS"}, {"hwnd": 3475986, "title": "prj_WindowWorskpaceManager - File Explorer", "category": "EXPLORER", "position": [642, 1024], "size": [1549, 851], "process_name": "explorer.exe", "class_name": "CabinetWClass"}, {"hwnd": 22219672, "title": "<PERSON><PERSON><PERSON>s - prj_WindowWorskpaceManager - Cursor", "category": "BROWSER", "position": [0, 0], "size": [3124, 1716], "process_name": "Cursor.exe", "class_name": "Chrome_WidgetWin_1"}, {"hwnd": 19532838, "title": "Cline - Google Chrome", "category": "BROWSER", "position": [0, 720], "size": [1920, 720], "process_name": "chrome.exe", "class_name": "Chrome_WidgetWin_1"}, {"hwnd": 11670948, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Projects\\prj_WindowWorskpaceManager\\prj_WindowWorskpaceManager.sublime-project - Sublime Text", "category": "DEVELOPMENT", "position": [1920, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 7673244, "title": "prj_WindowWorskpaceManager - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Projects\\prj_WindowWorskpaceManager\\scripts\\setup_workspace.bat - Sublime Text", "category": "DEVELOPMENT", "position": [-8, -8], "size": [3856, 2128], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 26282840, "title": "scripts - File Explorer", "category": "EXPLORER", "position": [668, 1050], "size": [1549, 851], "process_name": "explorer.exe", "class_name": "CabinetWClass"}, {"hwnd": 10685630, "title": "Python - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__ProjectGenerator\\src\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py - Sublime Text", "category": "DEVELOPMENT", "position": [2304, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 23010894, "title": "prj_WindowWorskpaceManager - File Explorer", "category": "EXPLORER", "position": [668, 1050], "size": [1549, 851], "process_name": "explorer.exe", "class_name": "CabinetWClass"}, {"hwnd": 14746984, "title": "Sourcetree", "category": "UNKNOWN", "position": [0, 488], "size": [3842, 1623], "process_name": "SourceTree.exe", "class_name": "HwndWrapper[SourceTree.exe;;5190855e-112e-432b-8eae-3e576c2e6d74]"}, {"hwnd": 19075494, "title": "Windows Input Experience", "category": "UNKNOWN", "position": [0, 0], "size": [3840, 2160], "process_name": "TextInputHost.exe", "class_name": "Windows.UI.Core.CoreWindow"}, {"hwnd": 9507494, "title": "Leaving Calad -  ├─ Defaults: DSK - D:\\NOT_BACKED_UP - Everything (1.5a) 1.5.0.1383a (x64)", "category": "UNKNOWN", "position": [0, 836], "size": [3840, 229], "process_name": "Everything64.exe", "class_name": "EVERYTHING_(1.5a)"}, {"hwnd": 18031516, "title": "D:\\NOT_BACKED_UP\\transcribe\\batch_transcription_en.yaml • - Sublime Text", "category": "DEVELOPMENT", "position": [2496, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 9378902, "title": "py__GitHistoryGraph - :: =============================================== • - Sublime Text", "category": "DEVELOPMENT", "position": [2688, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 8388894, "title": "SaveWindowLayout_V3 - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Utils_Todo\\Py_WindowTiler\\SaveWindowLayout_V3\\run_tests.py - Sublime Text", "category": "DEVELOPMENT", "position": [2880, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 19273848, "title": "py__MarkdownChunker - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__MarkdownChunker\\markdown_chunker.py - Sublime Text", "category": "DEVELOPMENT", "position": [3072, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 35725632, "title": "llm_working_singlescripts - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Ai_Utils\\working_systems\\llm_working_singlescripts\\b_llm_sysinstructions_simple\\langchain_sysinstructions_simple_027_tailrhymes.py - Sublime Text", "category": "DEVELOPMENT", "position": [3264, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 1124444, "title": "app_mpvplayer - C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Apps\\app_mpvplayer\\user\\observations.md - Sublime Text", "category": "DEVELOPMENT", "position": [3456, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 19729490, "title": "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__ProjectGenerator\\src\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py - Sublime Text", "category": "DEVELOPMENT", "position": [3648, 0], "size": [192, 2160], "process_name": "sublime_text.exe", "class_name": "PX_WINDOW_CLASS"}, {"hwnd": 15009288, "title": "<!ext:url;lnk sort:dm> dir:sfda: -  ├─ Defaults: DSK - D:\\Programmering - Everything (1.5a) 1.5.0.1383a (x64)", "category": "UNKNOWN", "position": [0, 1012], "size": [3840, 229], "process_name": "Everything64.exe", "class_name": "EVERYTHING_(1.5a)"}, {"hwnd": 9639898, "title": "Device Manager", "category": "SYSTEM", "position": [71, 42], "size": [795, 1752], "class_name": "MMCMainFrame"}, {"hwnd": 15209880, "title": "System Properties", "category": "EXPLORER", "position": [156, 156], "size": [426, 475], "class_name": "#32770"}, {"hwnd": 15405304, "title": "<!ext:url;lnk sort:dm>  -  ├─ unsaved_tabs_backup - Everything (1.5a) 1.5.0.1383a (x64)", "category": "UNKNOWN", "position": [0, 1540], "size": [3840, 229], "process_name": "Everything64.exe", "class_name": "EVERYTHING_(1.5a)"}, {"hwnd": 7677262, "title": "<PERSON><PERSON> navn - <PERSON><PERSON>", "category": "MEDIA", "position": [-32000, -32000], "size": [704, 500], "process_name": "mspaint.exe", "class_name": "MSPaintApp"}, {"hwnd": 13962126, "title": "(7) Bambulab 3D Printer - YouTube — Firefox Developer Edition", "category": "BROWSER", "position": [0, 1440], "size": [1920, 720], "process_name": "firefox.exe", "class_name": "MozillaWindowClass"}, {"hwnd": 7086826, "title": "Settings", "category": "SYSTEM", "position": [0, 1980], "size": [3840, 329], "process_name": "ApplicationFrameHost.exe", "class_name": "ApplicationFrameWindow"}, {"hwnd": 7542896, "title": "Task Manager", "category": "SYSTEM", "position": [-32000, -32000], "size": [160, 28], "class_name": "TaskManagerWindow"}, {"hwnd": 12784672, "title": "Program Manager", "category": "EXPLORER", "position": [0, 0], "size": [3840, 2160], "process_name": "explorer.exe", "class_name": "<PERSON><PERSON>"}]}